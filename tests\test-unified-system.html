<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一多模态系统测试</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --border-color: #dee2e6;
            --accent-color: #4facfe;
            --accent-hover: #3d8bfe;
            --accent-color-alpha: rgba(79, 172, 254, 0.1);
            --error-color: #dc3545;
            --error-color-alpha: rgba(220, 53, 69, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 8px;
        }

        .test-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--bg-secondary);
            border-radius: 6px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--error-color);
        }

        .status-dot.ready {
            background: #28a745;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-secondary);
        }

        .test-section h3 {
            margin-top: 0;
            color: var(--text-primary);
        }

        .test-result {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .test-result.success {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #155724;
        }

        .test-result.error {
            background: var(--error-color-alpha);
            border-color: var(--error-color);
            color: #721c24;
        }

        button {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px 0;
        }

        button:hover {
            background: var(--accent-hover);
        }

        button:disabled {
            background: var(--text-muted);
            cursor: not-allowed;
        }

        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 统一多模态系统测试</h1>
            <p>验证新的统一处理系统各模块功能</p>
        </div>

        <div class="test-status">
            <div class="status-item">
                <div class="status-dot" id="imageProcessorStatus"></div>
                <span>ImageProcessor</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="fileProcessorStatus"></div>
                <span>FileProcessor</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="unifiedProcessorStatus"></div>
                <span>UnifiedProcessor</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="uiControllerStatus"></div>
                <span>UIController</span>
            </div>
        </div>

        <div class="test-section">
            <h3>📸 图片处理模块测试</h3>
            <div class="test-controls">
                <input type="file" id="testImageInput" accept="image/*" multiple>
                <button onclick="testImageProcessing()">测试图片压缩</button>
            </div>
            <div id="imageTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📄 文件处理模块测试</h3>
            <div class="test-controls">
                <input type="file" id="testFileInput" accept=".pdf,.txt,.md,.json" multiple>
                <button onclick="testFileProcessing()">测试文件处理</button>
            </div>
            <div id="fileTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 统一处理器测试</h3>
            <div class="test-controls">
                <textarea id="testTextInput" placeholder="输入测试文字..." rows="3" style="width: 100%; margin-bottom: 10px;"></textarea>
                <button onclick="testUnifiedProcessing()">测试API调用</button>
                <button onclick="testMockProcessing()">模拟测试</button>
            </div>
            <div id="unifiedTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🎛️ UI控制器测试</h3>
            <div class="test-controls">
                <button onclick="testUIInitialization()">测试UI初始化</button>
                <button onclick="testEventHandlers()">测试事件处理</button>
            </div>
            <div id="uiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 完整流程测试</h3>
            <div class="test-controls">
                <button onclick="runFullTest()">运行完整测试</button>
                <button onclick="clearAllResults()">清空结果</button>
            </div>
            <div id="fullTestResult" class="test-result"></div>
        </div>
    </div>

    <!-- 引入所有模块 -->
    <script src="ImageProcessor.js"></script>
    <script src="FileProcessor.js"></script>
    <script src="UnifiedMultiModalProcessor.js"></script>
    <script src="UIController.js"></script>

    <script>
        // 测试脚本
        let testResults = {};

        // 检查模块加载状态
        function checkModuleStatus() {
            const modules = [
                { name: 'ImageProcessor', status: 'imageProcessorStatus', available: typeof ImageProcessor !== 'undefined' },
                { name: 'FileProcessor', status: 'fileProcessorStatus', available: typeof FileProcessor !== 'undefined' },
                { name: 'UnifiedMultiModalProcessor', status: 'unifiedProcessorStatus', available: typeof UnifiedMultiModalProcessor !== 'undefined' },
                { name: 'UIController', status: 'uiControllerStatus', available: typeof UIController !== 'undefined' }
            ];

            modules.forEach(module => {
                const statusElement = document.getElementById(module.status);
                if (module.available) {
                    statusElement.classList.add('ready');
                    console.log(`✅ ${module.name} 已加载`);
                } else {
                    console.error(`❌ ${module.name} 加载失败`);
                }
            });
        }

        // 图片处理测试
        async function testImageProcessing() {
            const input = document.getElementById('testImageInput');
            const result = document.getElementById('imageTestResult');

            if (input.files.length === 0) {
                result.textContent = '请先选择图片文件';
                result.className = 'test-result error';
                return;
            }

            try {
                const processor = new ImageProcessor();
                const files = Array.from(input.files);

                result.textContent = '正在处理图片...';
                result.className = 'test-result';

                const processed = await processor.processImages(files);
                const stats = processor.getCompressionStats(processed);

                result.textContent = `✅ 图片处理成功
处理文件数: ${stats.totalFiles}
原始大小: ${formatSize(stats.totalOriginalSize)}
压缩后: ${formatSize(stats.totalCompressedSize)}
节省空间: ${stats.savingPercentage}%
平均压缩比: ${stats.avgCompressionRatio}%

详细信息:
${processed.map(img => `- ${img.name}: ${img.dimensions.width}x${img.dimensions.height} (${formatSize(img.size)})`).join('\n')}`;

                result.className = 'test-result success';
                testResults.imageProcessing = { success: true, data: processed };

            } catch (error) {
                result.textContent = `❌ 图片处理失败: ${error.message}`;
                result.className = 'test-result error';
                testResults.imageProcessing = { success: false, error };
            }
        }

        // 文件处理测试
        async function testFileProcessing() {
            const input = document.getElementById('testFileInput');
            const result = document.getElementById('fileTestResult');

            if (input.files.length === 0) {
                result.textContent = '请先选择文件';
                result.className = 'test-result error';
                return;
            }

            try {
                const processor = new FileProcessor();
                const files = Array.from(input.files);

                result.textContent = '正在处理文件...';
                result.className = 'test-result';

                const processed = await processor.processFiles(files);
                const stats = processor.getProcessingStats(processed.processedFiles);

                result.textContent = `✅ 文件处理成功
处理文件数: ${stats.totalFiles}
成功: ${stats.successCount}
失败: ${stats.failureCount}
成功率: ${stats.successRate}%

提取的文本长度: ${processed.extractedText.length} 字符
转换的图片数: ${processed.convertedImages.length}

文件详情:
${processed.processedFiles.map(f => `- ${f.name} (${f.type}): ${f.processed ? '✅' : '❌'}`).join('\n')}`;

                result.className = 'test-result success';
                testResults.fileProcessing = { success: true, data: processed };

            } catch (error) {
                result.textContent = `❌ 文件处理失败: ${error.message}`;
                result.className = 'test-result error';
                testResults.fileProcessing = { success: false, error };
            }
        }

        // 统一处理器测试（模拟）
        async function testMockProcessing() {
            const result = document.getElementById('unifiedTestResult');
            const textInput = document.getElementById('testTextInput').value;

            try {
                const processor = new UnifiedMultiModalProcessor();

                result.textContent = '正在模拟处理...';
                result.className = 'test-result';

                // 模拟输入数据
                const mockInputs = {
                    textInput: textInput || '测试用户输入文字',
                    images: testResults.imageProcessing?.data || [],
                    files: [],
                    systemPrompt: null,
                    sessionId: `mock_${Date.now()}`
                };

                // 测试各个子模块
                const imageResults = await processor.processImages(mockInputs.images, mockInputs.sessionId);
                const fileResults = await processor.processFiles(mockInputs.files, mockInputs.sessionId);

                // 构建消息内容测试
                const content = processor.buildUnifiedContent({
                    textInput: mockInputs.textInput,
                    processedImages: imageResults,
                    processedFiles: fileResults,
                    systemPrompt: mockInputs.systemPrompt
                });

                result.textContent = `✅ 统一处理器模拟测试成功
处理器状态: ${JSON.stringify(processor.getStatus(), null, 2)}

消息内容块数: ${content.length}
- 文本块: ${content.filter(c => c.type === 'text').length}
- 图片块: ${content.filter(c => c.type === 'image_url').length}

提示词预览:
${content.find(c => c.type === 'text')?.text.substring(0, 200)}...`;

                result.className = 'test-result success';
                testResults.unifiedProcessing = { success: true, mockData: content };

            } catch (error) {
                result.textContent = `❌ 统一处理器测试失败: ${error.message}`;
                result.className = 'test-result error';
                testResults.unifiedProcessing = { success: false, error };
            }
        }

        // API调用测试（真实）
        async function testUnifiedProcessing() {
            const result = document.getElementById('unifiedTestResult');
            const textInput = document.getElementById('testTextInput').value;

            if (!textInput.trim()) {
                result.textContent = '请输入测试文字';
                result.className = 'test-result error';
                return;
            }

            try {
                const processor = new UnifiedMultiModalProcessor();

                result.textContent = '正在调用豆包API...';
                result.className = 'test-result';

                const inputs = {
                    textInput: textInput,
                    images: [],
                    files: [],
                    systemPrompt: '你是一个测试助手，请简短回复用户的输入。',
                    sessionId: `test_${Date.now()}`
                };

                const apiResult = await processor.processMultiModalInput(inputs);

                result.textContent = `✅ API调用成功
模型: ${processor.model}
响应长度: ${apiResult.rawResponse?.length || 0} 字符
是否成功: ${apiResult.success}
提取的数据: ${apiResult.data ? '是' : '否'}

原始响应:
${apiResult.rawResponse}`;

                result.className = 'test-result success';
                testResults.apiCall = { success: true, data: apiResult };

            } catch (error) {
                result.textContent = `❌ API调用失败: ${error.message}

可能的原因:
1. 网络连接问题
2. API密钥无效
3. CSP策略阻止请求
4. 服务端错误`;

                result.className = 'test-result error';
                testResults.apiCall = { success: false, error };
            }
        }

        // UI控制器测试
        function testUIInitialization() {
            const result = document.getElementById('uiTestResult');

            try {
                // 检查UI控制器是否正确初始化
                const hasUIController = typeof UIController !== 'undefined';
                const hasGlobalInstance = typeof window.uiController !== 'undefined';

                result.textContent = `✅ UI控制器测试结果
UIController类可用: ${hasUIController}
全局实例存在: ${hasGlobalInstance}

DOM元素检查:
- unifiedUploadArea: ${document.getElementById('unifiedUploadArea') ? '✅' : '❌'}
- 当前页面元素数: ${document.querySelectorAll('*').length}`;

                result.className = 'test-result success';
                testResults.uiController = { success: true };

            } catch (error) {
                result.textContent = `❌ UI控制器测试失败: ${error.message}`;
                result.className = 'test-result error';
                testResults.uiController = { success: false, error };
            }
        }

        // 事件处理器测试
        function testEventHandlers() {
            const result = document.getElementById('uiTestResult');

            try {
                // 模拟事件测试
                const mockEvent = {
                    preventDefault: () => {},
                    currentTarget: { classList: { add: () => {}, remove: () => {} }},
                    dataTransfer: { files: [] }
                };

                result.textContent = `✅ 事件处理器测试
拖拽事件处理: 可用
文件选择事件: 可用
按钮点击事件: 可用

注意: 这是模拟测试，实际功能需要在Chrome扩展环境中验证`;

                result.className = 'test-result success';

            } catch (error) {
                result.textContent = `❌ 事件处理器测试失败: ${error.message}`;
                result.className = 'test-result error';
            }
        }

        // 完整流程测试
        async function runFullTest() {
            const result = document.getElementById('fullTestResult');

            result.textContent = '🚀 开始完整流程测试...\n';
            result.className = 'test-result';

            const testSteps = [
                { name: '模块加载检查', fn: () => checkModuleStatus() },
                { name: '图片处理', fn: () => testResults.imageProcessing?.success },
                { name: '文件处理', fn: () => testResults.fileProcessing?.success },
                { name: '统一处理', fn: () => testResults.unifiedProcessing?.success },
                { name: 'UI控制器', fn: () => testResults.uiController?.success }
            ];

            let successCount = 0;
            let output = result.textContent;

            for (const step of testSteps) {
                try {
                    const passed = step.fn();
                    if (passed) {
                        output += `✅ ${step.name}: 通过\n`;
                        successCount++;
                    } else {
                        output += `❌ ${step.name}: 失败\n`;
                    }
                } catch (error) {
                    output += `❌ ${step.name}: 错误 - ${error.message}\n`;
                }
                result.textContent = output;
            }

            output += `\n📊 测试总结:
通过: ${successCount}/${testSteps.length}
成功率: ${Math.round((successCount / testSteps.length) * 100)}%

系统状态: ${successCount === testSteps.length ? '✅ 就绪' : '⚠️ 需要修复'}`;

            result.textContent = output;
            result.className = successCount === testSteps.length ? 'test-result success' : 'test-result error';
        }

        // 清空所有结果
        function clearAllResults() {
            const resultElements = document.querySelectorAll('.test-result');
            resultElements.forEach(el => {
                el.textContent = '';
                el.className = 'test-result';
            });
            testResults = {};
        }

        // 工具函数
        function formatSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时检查模块状态
        document.addEventListener('DOMContentLoaded', checkModuleStatus);
    </script>
</body>
</html>