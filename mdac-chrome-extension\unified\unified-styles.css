/* 统一多模态界面样式 */

.unified-upload-area {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
  border: 1px solid var(--border-color);
}

.upload-section h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.1em;
}

/* 文字输入区域 */
.text-input-area {
  margin-bottom: 20px;
}

.text-input-area label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-primary);
  font-weight: 500;
}

.text-input-area textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.text-input-area textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

/* 文件上传区域 */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 30px 20px;
  text-align: center;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: var(--accent-color);
  background: var(--accent-color-alpha);
}

.upload-prompt {
  color: var(--text-secondary);
}

.upload-icon {
  font-size: 2em;
  margin-bottom: 10px;
}

.upload-prompt p {
  margin: 5px 0;
}

.upload-prompt button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.upload-prompt button:hover {
  background: var(--accent-hover);
}

.upload-hint {
  font-size: 0.85em;
  color: var(--text-muted);
}

/* 文件列表 */
.file-list {
  margin-bottom: 20px;
}

.file-list h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
}

.file-category {
  margin-bottom: 15px;
  background: var(--bg-primary);
  border-radius: 6px;
  padding: 12px;
}

.file-category h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 0.95em;
}

.file-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  font-size: 0.9em;
}

.file-name {
  flex: 1;
  color: var(--text-primary);
  font-weight: 500;
}

.file-size {
  color: var(--text-muted);
  margin-left: 10px;
}

.remove-file {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 10px;
  font-size: 0.8em;
}

.remove-file:hover {
  background: var(--error-color-alpha);
}

/* 处理控制按钮 */
.processing-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.primary-button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  flex: 1;
}

.primary-button:hover:not(:disabled) {
  background: var(--accent-hover);
}

.primary-button:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
}

.secondary-button {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.secondary-button:hover {
  background: var(--bg-secondary);
}

/* 进度显示 */
.progress-area {
  background: var(--bg-primary);
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid var(--accent-color);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.progress-stats {
  font-size: 0.9em;
  color: var(--text-muted);
}

.progress-steps {
  font-size: 0.85em;
  color: var(--text-secondary);
}

/* 结果显示 */
.result-area {
  background: var(--bg-primary);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid var(--border-color);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-actions button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85em;
}

.result-actions button:hover {
  background: var(--accent-hover);
}

.result-json pre,
.result-raw pre {
  background: var(--bg-secondary);
  padding: 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  overflow-x: auto;
  font-size: 0.85em;
  color: var(--text-primary);
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.result-metadata {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border-color);
}

.result-metadata small {
  color: var(--text-muted);
}

/* 错误消息 */
.error-message {
  background: var(--error-color-alpha);
  border: 1px solid var(--error-color);
  border-radius: 6px;
  padding: 12px;
  color: var(--error-color);
}

.error-message h5 {
  margin: 0 0 8px 0;
}

.error-message p {
  margin: 0;
  font-size: 0.9em;
}

/* Toast消息 */
.toast-message {
  animation: slideInRight 0.3s ease-out, slideOutRight 0.3s ease-in 2.7s forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .unified-upload-area {
    padding: 15px;
  }

  .file-upload-area {
    padding: 20px 15px;
  }

  .processing-controls {
    flex-direction: column;
  }

  .result-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .result-actions {
    align-self: stretch;
  }

  .result-actions button {
    flex: 1;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .file-upload-area {
  border-color: var(--border-color-dark);
}

[data-theme="dark"] .file-upload-area:hover,
[data-theme="dark"] .file-upload-area.drag-over {
  border-color: var(--accent-color);
  background: var(--accent-color-alpha);
}

[data-theme="dark"] .result-json pre,
[data-theme="dark"] .result-raw pre {
  background: var(--bg-tertiary);
}