<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>API修复验证测试</h1>
    <div id="results"></div>
    
    <h2>测试操作</h2>
    <button onclick="testMoonshotAPIClass()">测试MoonshotAPI类暴露</button>
    <button onclick="testAliyunAPIInit()">测试阿里云API初始化</button>
    <button onclick="testAPICallFormat()">测试API调用格式</button>
    
    <script src="moonshot-api.js"></script>
    <script>
        const HARDCODED_API_KEYS = {
            moonshot: 'sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY',
            aliyun: 'sk-e5619d5a3c7c4d6793b9decc743cea9f'
        };

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            resultsDiv.appendChild(div);
        }

        // 测试1: MoonshotAPI类暴露
        function testMoonshotAPIClass() {
            try {
                if (typeof window.MoonshotAPI === 'function') {
                    const instance = new window.MoonshotAPI();
                    if (instance && typeof instance.setApiKey === 'function') {
                        logResult('✅ MoonshotAPI类暴露修复成功 - 可以正常实例化', 'success');
                    } else {
                        logResult('❌ MoonshotAPI实例方法不完整', 'error');
                    }
                } else {
                    logResult('❌ window.MoonshotAPI不存在或不是构造函数', 'error');
                }
            } catch (error) {
                logResult(`❌ MoonshotAPI类测试失败: ${error.message}`, 'error');
            }
        }

        // 测试2: 阿里云API初始化  
        function testAliyunAPIInit() {
            try {
                // 模拟阿里云API初始化
                const aliyunAPI = {
                    apiKey: HARDCODED_API_KEYS.aliyun,
                    baseUrl: 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1',
                    endpoint: 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions',
                    models: ['qwen-vl-max', 'qwen-vl-plus', 'qwen-turbo', 'qwen-plus', 'qwen-max']
                };
                
                if (aliyunAPI.apiKey && aliyunAPI.endpoint && aliyunAPI.models.length > 0) {
                    logResult('✅ 阿里云API初始化修复成功 - 使用OpenAI兼容端点', 'success');
                } else {
                    logResult('❌ 阿里云API初始化不完整', 'error');
                }
            } catch (error) {
                logResult(`❌ 阿里云API初始化测试失败: ${error.message}`, 'error');
            }
        }

        // 测试3: API调用格式
        function testAPICallFormat() {
            try {
                // 测试Moonshot API调用格式
                const moonshotRequest = {
                    model: 'moonshot-v1-8k',
                    messages: [
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": "测试消息"}
                    ],
                    temperature: 0.1,
                    max_tokens: 100
                };

                // 测试阿里云API调用格式 (OpenAI兼容)
                const aliyunRequest = {
                    model: 'qwen-turbo',
                    messages: [
                        {"role": "system", "content": [{"type": "text", "text": "You are a helpful assistant."}]},
                        {"role": "user", "content": [{"type": "text", "text": "测试消息"}]}
                    ],
                    temperature: 0.1,
                    max_tokens: 100
                };

                if (moonshotRequest.model && moonshotRequest.messages && 
                    aliyunRequest.model && aliyunRequest.messages) {
                    logResult('✅ API调用格式修复成功 - 符合OpenAI兼容标准', 'success');
                } else {
                    logResult('❌ API调用格式不完整', 'error');
                }
            } catch (error) {
                logResult(`❌ API调用格式测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            logResult('开始API修复验证测试...', 'info');
            setTimeout(() => {
                testMoonshotAPIClass();
                testAliyunAPIInit(); 
                testAPICallFormat();
                logResult('🎉 所有基础验证测试完成！', 'success');
            }, 500);
        });
    </script>
</body>
</html>