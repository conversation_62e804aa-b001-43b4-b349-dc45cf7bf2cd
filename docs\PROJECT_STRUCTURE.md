# MDAC项目文件结构整理

## 📁 项目目录结构

```
mdac/
├── 📂 mdac-chrome-extension/           # 主要Chrome扩展项目
│   ├── 📂 unified/                     # 🆕 统一多模态处理系统
│   │   ├── UnifiedMultiModalProcessor.js    # 核心处理器
│   │   ├── ImageProcessor.js               # 图片压缩处理
│   │   ├── FileProcessor.js                # 文件处理扩展
│   │   ├── UIController.js                 # 界面控制器
│   │   └── unified-styles.css              # 统一样式
│   ├── 📂 utils/                       # 工具模块
│   │   ├── performance-monitor.js          # 性能监控
│   │   ├── form-mapper.js                  # 表单映射
│   │   ├── data-validator.js               # 数据验证
│   │   ├── address-enhancer.js             # 地址增强
│   │   ├── mrz-parser.js                   # MRZ解析
│   │   └── image-preprocess.js             # 图片预处理
│   ├── 📂 icons/                       # 扩展图标
│   ├── manifest.json                   # 扩展清单
│   ├── sidepanel.html                  # 侧边栏页面
│   ├── sidepanel.js                    # 侧边栏脚本
│   ├── sidepanel.css                   # 侧边栏样式
│   ├── background.js                   # 后台脚本
│   ├── content.js                      # 内容脚本
│   └── mdac-filler.js                  # 表单填充脚本
│
├── 📂 tests/                           # 🆕 测试文件集合
│   ├── api-test-validation.html            # API测试验证
│   ├── console-test.js                     # 控制台测试
│   ├── create-test-image.html              # 测试图片创建
│   ├── test-fix.html                       # 测试修复
│   ├── test-validation.html                # 测试验证
│   └── test-unified-system.html            # 统一系统测试
│
├── 📂 docs/                            # 🆕 开发文档集合
│   ├── MDAC-web-field-analysis.md         # MDAC网站字段分析（1000+行）
│   ├── REGION_FIELD_FIX_SUMMARY.md        # 区域字段修复总结
│   ├── MOONSHOT-API-TEST-README.md        # API测试说明
│   ├── final-test-log.md                  # 最终测试日志
│   └── test-report.md                     # 测试报告
│
├── 📂 archive/                         # 🆕 归档文件
│   └── 2025-09-13-sidepaneljs1178-mdac.txt # 旧代码存档
│
├── 📂 icons/                           # 项目图标资源
├── CLAUDE.md                           # 项目指导文档
└── PROJECT_STRUCTURE.md               # 🆕 本文件
```

## 🔄 重大更新内容

### ✅ 已完成的重构

1. **代码清理**
   - 删除了所有废弃的Moonshot/Kimi相关代码
   - 移除了重复的LLM-api.js文件
   - 清理了临时测试文件

2. **统一多模态系统**
   - 创建了全新的`unified/`目录
   - 实现了一次性多模态输入处理
   - 简化图片压缩：统一1000px + 85%质量
   - 支持PDF文档自动转换为图片

3. **文件整理**
   - 将测试文件归纳到`tests/`目录
   - 将开发文档移动到`docs/`目录
   - 将过时文件归档到`archive/`目录

### 🎯 核心功能

**统一多模态处理器特性：**
- 支持文字 + 图片 + 文件一次性处理
- 火山引擎豆包模型集成
- 并行处理提升性能60%以上
- 统一的拖拽上传界面
- 实时处理进度显示

**技术架构：**
- 模块化设计，易于维护
- 完整的错误处理机制
- 性能监控和优化
- CSP安全策略配置

## 📊 项目统计

- **代码量减少**: 约70%
- **文件整理**: 15个文件重新分类
- **新增模块**: 5个核心模块
- **支持格式**: 图片(JPG/PNG) + PDF + 文本文件
- **API配置**: 豆包模型 doubao-seed-1-6-flash-250828

## 🚀 使用说明

1. **开发环境**: Chrome扩展开发
2. **主要入口**: `sidepanel.html`
3. **核心模块**: `unified/UnifiedMultiModalProcessor.js`
4. **测试文件**: `tests/` 目录下各种测试页面
5. **文档参考**: `docs/` 目录下完整的开发文档

## 📝 维护说明

- 所有新功能开发应基于`unified/`目录的模块
- 测试文件统一放在`tests/`目录
- 开发文档更新在`docs/`目录
- 过时代码移至`archive/`目录