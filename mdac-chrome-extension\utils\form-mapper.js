// 表单字段映射工具
class FormMapper {
    constructor() {
        // MDAC表单字段映射 (与content.js统一的实际MDAC网站字段ID)
        this.fieldMapping = {
            name: '#name',
            passNo: '#passNo',
            dob: '#dob',
            nationality: '#nationality',
            sex: '#sex',
            passExpDte: '#passExpDte',
            email: '#email',
            confirmEmail: '#confirmEmail',
            region: '#region',
            mobile: '#mobile',
            arrDt: '#arrDt',
            depDt: '#depDt',
            vesselNm: '#vesselNm',
            trvlMode: '#trvlMode',
            embark: '#embark',
            accommodationStay: '#accommodationStay',
            accommodationAddress1: '#accommodationAddress1',
            accommodationAddress2: '#accommodationAddress2',
            accommodationState: '#accommodationState',
            accommodationCity: '#accommodationCity',
            accommodationPostcode: '#accommodationPostcode'
        };
        
        // 字段标签映射（用于显示）
        this.fieldLabels = {
            name: '姓名',
            passNo: '护照号码',
            dob: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passExpDte: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            region: '电话区号',
            mobile: '手机号码',
            arrDt: '到达日期',
            depDt: '离开日期',
            vesselNm: '航班号',
            trvlMode: '交通方式',
            embark: '登船港',
            accommodationStay: '住宿类型',
            accommodationAddress1: '住宿地址',
            accommodationAddress2: '地址第二行',
            accommodationState: '州属',
            accommodationCity: '城市',
            accommodationPostcode: '邮编'
        };
        
        // 国籍代码映射
        this.nationalityCodes = {
            'CHN': '中国',
            'USA': '美国',
            'GBR': '英国',
            'SGP': '新加坡',
            'JPN': '日本',
            'KOR': '韩国',
            'THA': '泰国',
            'AUS': '澳洲',
            'IND': '印度',
            'IDN': '印尼',
            'VNM': '越南',
            'PHL': '菲律宾',
            'MYS': '马来西亚'
        };
        
        // 电话区号映射
        this.phoneRegions = {
            '86': '中国',
            '1': '美国/加拿大',
            '44': '英国',
            '65': '新加坡',
            '81': '日本',
            '82': '韩国',
            '66': '泰国',
            '61': '澳洲',
            '91': '印度',
            '62': '印尼',
            '84': '越南',
            '63': '菲律宾',
            '60': '马来西亚'
        };
        
        // 马来西亚州属代码
        this.stateCodes = {
            '01': '柔佛 (JOHOR)',
            '02': '吉打 (KEDAH)',
            '03': '吉兰丹 (KELANTAN)',
            '04': '马六甲 (MELAKA)',
            '05': '森美兰 (NEGERI SEMBILAN)',
            '06': '彭亨 (PAHANG)',
            '07': '槟城 (PULAU PINANG)',
            '08': '霹雳 (PERAK)',
            '09': '玻璃市 (PERLIS)',
            '10': '雪兰莪 (SELANGOR)',
            '11': '登嘉楼 (TERENGGANU)',
            '12': '沙巴 (SABAH)',
            '13': '砂拉越 (SARAWAK)',
            '14': '吉隆坡 (WP KUALA LUMPUR)',
            '15': '纳闽 (WP LABUAN)',
            '16': '布城 (WP PUTRAJAYA)'
        };
        
        // 城市关键词映射
        this.cityMapping = {
            'kuala lumpur': 'KUALA LUMPUR',
            'kl': 'KUALA LUMPUR',
            '吉隆坡': 'KUALA LUMPUR',
            'johor bahru': 'JOHOR BAHRU',
            'jb': 'JOHOR BAHRU',
            '新山': 'JOHOR BAHRU',
            'penang': 'GEORGE TOWN',
            '槟城': 'GEORGE TOWN',
            'malacca': 'MELAKA',
            '马六甲': 'MELAKA',
            'ipoh': 'IPOH',
            '怡保': 'IPOH',
            'kota kinabalu': 'KOTA KINABALU',
            '亚庇': 'KOTA KINABALU',
            'kuching': 'KUCHING',
            '古晋': 'KUCHING',
            'shah alam': 'SHAH ALAM',
            'petaling jaya': 'PETALING JAYA',
            'pj': 'PETALING JAYA',
            'subang jaya': 'SUBANG JAYA',
            'klang': 'KLANG',
            '巴生': 'KLANG'
        };
    }
    
    // 获取字段选择器
    getFieldSelector(fieldName) {
        return this.fieldMapping[fieldName] || null;
    }

    // 检查字段是否存在于DOM中
    isFieldAvailable(fieldName) {
        const selector = this.getFieldSelector(fieldName);
        if (!selector) return false;
        return document.querySelector(selector) !== null;
    }

    // 获取可用字段列表（增强版本 - 智能处理缺失字段）
    getAvailableFields() {
        const available = {};
        const missing = {};
        const suggestions = {};

        for (const [fieldName, selector] of Object.entries(this.fieldMapping)) {
            if (selector && document.querySelector(selector)) {
                available[fieldName] = selector;
            } else {
                missing[fieldName] = selector || '无选择器';

                // 为缺失字段提供智能建议
                suggestions[fieldName] = this.getFieldSuggestions(fieldName, selector);
            }
        }

        return {
            available,
            missing,
            suggestions,
            totalFields: Object.keys(this.fieldMapping).length,
            availableCount: Object.keys(available).length,
            missingCount: Object.keys(missing).length
        };
    }

    // 为缺失字段提供智能建议
    getFieldSuggestions(fieldName, originalSelector) {
        const suggestions = [];

        switch (fieldName) {
            case 'embark':
                suggestions.push({
                    type: 'info',
                    message: '登船港字段在MDAC表单中不存在，将使用国籍作为默认值',
                    action: '使用nationality字段代替'
                });
                break;

            case 'accommodationAddress2':
                suggestions.push({
                    type: 'warning',
                    message: '地址第二行字段在HTML中不存在，这是可选字段',
                    action: '可以安全忽略此字段'
                });
                break;

            case 'accommodationCity':
                suggestions.push({
                    type: 'warning',
                    message: '城市字段在HTML中不存在，将尝试从地址推断',
                    action: '建议从address1或州属推断城市信息'
                });
                break;

            default:
                if (originalSelector) {
                    suggestions.push({
                        type: 'error',
                        message: `选择器 "${originalSelector}" 在DOM中未找到`,
                        action: '检查HTML结构或更新选择器'
                    });

                    // 尝试提供替代选择器建议
                    const altSelectors = this.getAlternativeSelectors(fieldName);
                    if (altSelectors.length > 0) {
                        suggestions.push({
                            type: 'suggestion',
                            message: '尝试以下替代选择器',
                            alternatives: altSelectors
                        });
                    }
                } else {
                    suggestions.push({
                        type: 'error',
                        message: '字段缺少选择器配置',
                        action: '在fieldMapping中添加有效选择器'
                    });
                }
        }

        return suggestions;
    }

    // 获取替代选择器建议
    getAlternativeSelectors(fieldName) {
        const alternatives = [];
        const fieldPatterns = {
            'embark': ['#embark', '#lastPort', '#departurePort'],
            'accommodationAddress2': ['#address2', '#addressLine2', '#additionalAddress'],
            'accommodationCity': ['#city', '#accommodationCity', '#town']
        };

        if (fieldPatterns[fieldName]) {
            fieldPatterns[fieldName].forEach(selector => {
                if (document.querySelector(selector)) {
                    alternatives.push(selector);
                }
            });
        }

        return alternatives;
    }
    
    // 获取字段标签
    getFieldLabel(fieldName) {
        return this.fieldLabels[fieldName] || fieldName;
    }
    
    // 获取所有字段映射
    getAllMappings() {
        return { ...this.fieldMapping };
    }
    
    // 验证字段数据
    validateFieldData(fieldName, value) {
        if (!value || value === '') {
            return { valid: false, error: '值不能为空' };
        }
        
        switch (fieldName) {
            case 'name':
                if (!/^[A-Z\s]+$/.test(value)) {
                    return { valid: false, error: '姓名必须是大写英文字母' };
                }
                break;
                
            case 'passNo':
                if (!/^[A-Z0-9]{6,12}$/.test(value)) {
                    return { valid: false, error: '护照号码格式不正确' };
                }
                break;
                
            case 'email':
            case 'confirmEmail':
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    return { valid: false, error: '邮箱格式不正确' };
                }
                break;
                
            case 'mobile':
                if (!/^\d{7,15}$/.test(value)) {
                    return { valid: false, error: '手机号码格式不正确' };
                }
                break;
                
            case 'dob':
            case 'passExpDte':
            case 'arrDt':
            case 'depDt':
                if (!/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                    return { valid: false, error: '日期格式必须为DD/MM/YYYY' };
                }
                break;
                
            case 'sex':
                if (!['1', '2'].includes(value)) {
                    return { valid: false, error: '性别必须是1(男)或2(女)' };
                }
                break;
                
            case 'nationality':
                if (!this.nationalityCodes[value]) {
                    return { valid: false, error: '国籍代码不正确' };
                }
                break;
                
            case 'region':
                if (!this.phoneRegions[value]) {
                    return { valid: false, error: '电话区号不正确' };
                }
                break;
                
            case 'accommodationState':
                if (!this.stateCodes[value]) {
                    return { valid: false, error: '州属代码不正确' };
                }
                break;
        }
        
        return { valid: true };
    }
    
    // 格式化日期 (从各种格式转为DD/MM/YYYY)
    formatDate(dateStr) {
        if (!dateStr) return null;
        
        // 如果已经是DD/MM/YYYY格式
        if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
            return dateStr;
        }
        
        // 尝试解析其他格式
        let date;
        
        // YYYY-MM-DD格式
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            const [year, month, day] = dateStr.split('-');
            return `${day}/${month}/${year}`;
        }
        
        // MM/DD/YYYY格式
        if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
            const [month, day, year] = dateStr.split('/');
            return `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
        }
        
        // 中文日期格式：2025年8月1日
        const chineseDateMatch = dateStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
        if (chineseDateMatch) {
            const [, year, month, day] = chineseDateMatch;
            return `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
        }
        
        // 尝试使用Date对象解析
        try {
            date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}/${month}/${year}`;
            }
        } catch (error) {
            console.log('日期解析失败:', error);
        }
        
        return null;
    }
    
    // 从地址推断州属
    inferStateFromAddress(address) {
        if (!address) return null;
        
        const addressLower = address.toLowerCase();
        
        const stateKeywords = {
            '14': ['kuala lumpur', 'kl', '吉隆坡'],
            '10': ['selangor', 'shah alam', 'petaling jaya', 'subang jaya', 'klang', '雪兰莪'],
            '01': ['johor', 'johor bahru', 'jb', '柔佛', '新山'],
            '07': ['penang', 'george town', '槟城'],
            '04': ['malacca', 'melaka', '马六甲'],
            '08': ['perak', 'ipoh', '霹雳', '怡保'],
            '12': ['sabah', 'kota kinabalu', '沙巴', '亚庇'],
            '13': ['sarawak', 'kuching', '砂拉越', '古晋']
        };
        
        for (const [code, keywords] of Object.entries(stateKeywords)) {
            if (keywords.some(keyword => addressLower.includes(keyword))) {
                return code;
            }
        }
        
        return null;
    }
    
    // 从地址推断城市
    inferCityFromAddress(address) {
        if (!address) return null;
        
        const addressLower = address.toLowerCase();
        
        for (const [keyword, cityName] of Object.entries(this.cityMapping)) {
            if (addressLower.includes(keyword)) {
                return cityName;
            }
        }
        
        return null;
    }
    
    // 处理数据后置处理（增强版本 - 包含智能推断和缺失字段处理）
    postProcessData(data) {
        const processed = { ...data };

        // 1. 格式化日期
        ['dob', 'passExpDte', 'arrDt', 'depDt'].forEach(field => {
            if (processed[field]) {
                processed[field] = this.formatDate(processed[field]);
            }
        });

        // 2. 确保邮箱确认字段一致
        if (processed.email && !processed.confirmEmail) {
            processed.confirmEmail = processed.email;
        }

        // 3. 推断州属和城市
        if (processed.accommodationAddress1) {
            if (!processed.accommodationState) {
                processed.accommodationState = this.inferStateFromAddress(processed.accommodationAddress1);
            }
            if (!processed.accommodationCity) {
                processed.accommodationCity = this.inferCityFromAddress(processed.accommodationAddress1);
            }
        }

        // 4. 处理HTML中缺失的字段（智能填充）
        this.handleMissingHtmlFields(processed);

        // 5. 设置合理的默认值
        if (!processed.trvlMode) {
            processed.trvlMode = '1'; // 默认飞机
        }
        if (!processed.accommodationStay) {
            processed.accommodationStay = '01'; // 默认酒店
        }

        return processed;
    }

    // 处理HTML中缺失的字段（智能填充）
    handleMissingHtmlFields(data) {
        // embark字段在HTML中不存在，使用国籍作为默认值
        if (!data.embark && data.nationality) {
            data.embark = data.nationality;
            console.log(`🔄 自动填充缺失字段: embark = ${data.embark} (从nationality推断)`);
        }

        // accommodationAddress2是可选字段，不存在时设置为空字符串
        if (!data.accommodationAddress2) {
            data.accommodationAddress2 = '';
            console.log('🔄 设置可选字段: accommodationAddress2 = 空字符串');
        }

        // accommodationCity在HTML中不存在，尝试从其他字段推断
        if (!data.accommodationCity && data.accommodationAddress1) {
            // 尝试从地址中提取城市信息
            const city = this.extractCityFromAddress(data.accommodationAddress1);
            if (city) {
                data.accommodationCity = city;
                console.log(`🔄 智能推断: accommodationCity = ${city} (从地址提取)`);
            } else if (data.accommodationState) {
                // 如果无法从地址提取，使用州属作为备选
                data.accommodationCity = data.accommodationState;
                console.log(`🔄 备选推断: accommodationCity = ${data.accommodationState} (从州属推断)`);
            }
        }
    }

    // 从地址中提取城市信息（增强版本）
    extractCityFromAddress(address) {
        if (!address) return null;

        // 常见的马来西亚城市模式
        const malaysianCities = [
            'Kuala Lumpur', 'George Town', 'Ipoh', 'Shah Alam', 'Petaling Jaya',
            'Subang Jaya', 'Klang', 'Johor Bahru', 'Kuching', 'Kota Kinabalu',
            'Seremban', 'Malacca City', 'Alor Setar', 'Kuala Terengganu', 'Kota Bharu',
            'Kuantan', 'Segamat', 'Miri', 'Sibu', 'Bintulu', 'Sandakan', 'Tawau',
            'Putrajaya', 'Cyberjaya', 'Sunway', 'Puchong', 'Cheras', 'Ampang'
        ];

        const addressLower = address.toLowerCase();

        // 尝试匹配已知城市
        for (const city of malaysianCities) {
            if (addressLower.includes(city.toLowerCase())) {
                return city;
            }
        }

        // 尝试从地址格式中提取（假设最后一个逗号后是城市）
        const parts = address.split(',').map(part => part.trim());
        if (parts.length >= 2) {
            // 返回最后一部分作为城市，但需要验证是否为合理的城市名称
            const lastPart = parts[parts.length - 1];
            if (lastPart.length > 2 && lastPart.length < 50) {
                return lastPart;
            }
        }

        return null;
    }
}

// 创建全局实例
window.formMapper = new FormMapper();
