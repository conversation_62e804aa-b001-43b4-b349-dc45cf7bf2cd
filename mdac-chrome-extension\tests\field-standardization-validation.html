<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC字段标准化验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .field-valid {
            color: #28a745;
            font-weight: bold;
        }
        .field-invalid {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 MDAC字段标准化验证测试</h1>
        <p>验证整个系统是否正确使用MDAC网站原生字段名，实现真正的"零转换映射"。</p>
        
        <div class="test-container">
            <h3>📋 MDAC原生字段标准</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>字段ID</th>
                        <th>字段名称</th>
                        <th>数据类型</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody id="standardFieldsTable">
                    <!-- 将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>

        <div class="test-container">
            <h3>🧪 测试操作</h3>
            <button onclick="testAIPromptStandardization()">测试AI提示词标准化</button>
            <button onclick="testFieldExtractionStandardization()">测试字段提取标准化</button>
            <button onclick="testDataTransformationStandardization()">测试数据转换标准化</button>
            <button onclick="testEndToEndFlow()">端到端流程测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-container">
            <h3>📊 测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-container">
            <h3>📝 测试日志</h3>
            <div class="test-log" id="testLog"></div>
        </div>
    </div>

    <!-- 加载依赖 -->
    <script src="../utils/SmartPreprocessor.js"></script>
    <script src="../utils/field-mapping-config.js"></script>
    <script src="../unified/UnifiedMultiModalProcessor.js"></script>

    <script>
        // MDAC原生字段标准定义
        const MDAC_NATIVE_FIELDS = [
            { id: 'name', name: '姓名', type: '文本', example: 'ZHANG SAN' },
            { id: 'passNo', name: '护照号码', type: '文本', example: 'E12345678' },
            { id: 'dob', name: '出生日期', type: '日期', example: '01/01/1990' },
            { id: 'passExpDte', name: '护照有效期', type: '日期', example: '01/01/2030' },
            { id: 'nationality', name: '国籍', type: '选择', example: 'CHN' },
            { id: 'sex', name: '性别', type: '选择', example: 'MALE' },
            { id: 'email', name: '电子邮箱', type: '文本', example: '<EMAIL>' },
            { id: 'confirmEmail', name: '确认邮箱', type: '文本', example: '<EMAIL>' },
            { id: 'region', name: '电话区号', type: '选择', example: '86' },
            { id: 'mobile', name: '手机号码', type: '文本', example: '13800138000' },
            { id: 'arrDt', name: '到达日期', type: '日期', example: '01/01/2024' },
            { id: 'depDt', name: '出发日期', type: '日期', example: '07/01/2024' },
            { id: 'vesselNm', name: '航班号', type: '文本', example: 'CA123' },
            { id: 'trvlMode', name: '旅行方式', type: '选择', example: 'AIR' },
            { id: 'embark', name: '最后登船港', type: '选择', example: 'CHN' },
            { id: 'accommodationStay', name: '住宿类型', type: '选择', example: 'HOTEL' },
            { id: 'accommodationAddress1', name: '住宿地址1', type: '文本', example: '123 Hotel Street' },
            { id: 'accommodationAddress2', name: '住宿地址2', type: '文本', example: 'Apt 456' },
            { id: 'accommodationState', name: '住宿州属', type: '选择', example: '14' },
            { id: 'accommodationCity', name: '住宿城市', type: '选择', example: 'Kuala Lumpur' },
            { id: 'accommodationPostcode', name: '住宿邮编', type: '文本', example: '50000' }
        ];

        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 初始化页面
        function initializePage() {
            populateStandardFieldsTable();
            log('页面初始化完成', 'info');
        }

        // 填充标准字段表格
        function populateStandardFieldsTable() {
            const tbody = document.getElementById('standardFieldsTable');
            tbody.innerHTML = '';
            
            MDAC_NATIVE_FIELDS.forEach(field => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><code>${field.id}</code></td>
                    <td>${field.name}</td>
                    <td>${field.type}</td>
                    <td><code>${field.example}</code></td>
                `;
            });
        }

        // 日志函数
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('testLog');
            const colorMap = {
                'info': '#e2e8f0',
                'success': '#68d391',
                'warning': '#fbb6ce',
                'error': '#fc8181'
            };
            
            logDiv.innerHTML += `<div style="color: ${colorMap[level]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 显示测试结果
        function showResult(title, content, isSuccess) {
            const resultsDiv = document.getElementById('testResults');
            const resultClass = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML += `
                <div class="${resultClass}">
                    <h4>${title}</h4>
                    <div>${content}</div>
                </div>
            `;
        }

        // 测试AI提示词标准化
        function testAIPromptStandardization() {
            log('开始测试AI提示词标准化...', 'info');
            testStats.total++;

            try {
                // 检查UnifiedMultiModalProcessor是否存在
                if (typeof UnifiedMultiModalProcessor === 'undefined') {
                    throw new Error('UnifiedMultiModalProcessor未加载');
                }

                const processor = new UnifiedMultiModalProcessor();
                const prompt = processor.getMDACExtractionPrompt();
                
                // 验证提示词是否包含MDAC原生字段
                let validFields = 0;
                let invalidFields = [];
                
                MDAC_NATIVE_FIELDS.forEach(field => {
                    if (prompt.includes(`"${field.id}"`)) {
                        validFields++;
                    } else {
                        invalidFields.push(field.id);
                    }
                });

                const isSuccess = invalidFields.length === 0;
                if (isSuccess) {
                    testStats.passed++;
                    showResult('✅ AI提示词标准化测试通过', 
                        `所有${MDAC_NATIVE_FIELDS.length}个MDAC原生字段都在提示词中正确定义`, true);
                    log(`AI提示词包含所有${validFields}个MDAC原生字段`, 'success');
                } else {
                    testStats.failed++;
                    showResult('❌ AI提示词标准化测试失败', 
                        `缺失字段: ${invalidFields.join(', ')}`, false);
                    log(`AI提示词缺失${invalidFields.length}个字段: ${invalidFields.join(', ')}`, 'error');
                }

            } catch (error) {
                testStats.failed++;
                showResult('❌ AI提示词标准化测试失败', error.message, false);
                log(`AI提示词测试失败: ${error.message}`, 'error');
            }
        }

        // 测试字段提取标准化
        function testFieldExtractionStandardization() {
            log('开始测试字段提取标准化...', 'info');
            testStats.total++;

            try {
                if (typeof SmartPreprocessor === 'undefined') {
                    throw new Error('SmartPreprocessor未加载');
                }

                const preprocessor = new SmartPreprocessor();
                const testText = `
                    姓名：张三
                    护照号：E12345678
                    出生日期：01/01/1990
                    性别：男
                    邮箱：<EMAIL>
                    手机号：13800138000
                `;

                const result = preprocessor.preprocess(testText);
                
                if (result.success) {
                    const extractedFields = Object.keys(result.data);
                    const validFields = extractedFields.filter(field => 
                        MDAC_NATIVE_FIELDS.some(nativeField => nativeField.id === field)
                    );

                    const isSuccess = validFields.length === extractedFields.length;
                    if (isSuccess) {
                        testStats.passed++;
                        showResult('✅ 字段提取标准化测试通过', 
                            `提取的${extractedFields.length}个字段都使用MDAC原生字段名`, true);
                        log(`字段提取使用正确的MDAC原生字段名: ${validFields.join(', ')}`, 'success');
                    } else {
                        testStats.failed++;
                        const invalidFields = extractedFields.filter(field => 
                            !MDAC_NATIVE_FIELDS.some(nativeField => nativeField.id === field)
                        );
                        showResult('❌ 字段提取标准化测试失败', 
                            `使用了非MDAC原生字段名: ${invalidFields.join(', ')}`, false);
                        log(`字段提取使用了错误的字段名: ${invalidFields.join(', ')}`, 'error');
                    }
                } else {
                    throw new Error('字段提取失败');
                }

            } catch (error) {
                testStats.failed++;
                showResult('❌ 字段提取标准化测试失败', error.message, false);
                log(`字段提取测试失败: ${error.message}`, 'error');
            }
        }

        // 测试数据转换标准化
        function testDataTransformationStandardization() {
            log('开始测试数据转换标准化...', 'info');
            testStats.total++;

            try {
                if (typeof MDACFieldMappingConfig === 'undefined') {
                    throw new Error('MDACFieldMappingConfig未加载');
                }

                const config = new MDACFieldMappingConfig();
                
                // 测试数据 - 包含旧字段名和新字段名
                const testData = {
                    name: 'ZHANG SAN',
                    passengerName: 'LI MING', // 旧字段名，应该被忽略
                    passNo: 'E12345678',
                    sex: 'MALE',
                    gender: 'FEMALE' // 旧字段名，应该被忽略
                };

                const result = config.transformAIToMDAC(testData);
                
                // 验证结果只包含MDAC原生字段
                const resultFields = Object.keys(result);
                const validFields = resultFields.filter(field => 
                    MDAC_NATIVE_FIELDS.some(nativeField => nativeField.id === field)
                );

                const isSuccess = validFields.length === resultFields.length && 
                                result.name === 'ZHANG SAN' && // 应该使用MDAC原生字段的值
                                result.sex === 'MALE'; // 应该使用MDAC原生字段的值

                if (isSuccess) {
                    testStats.passed++;
                    showResult('✅ 数据转换标准化测试通过', 
                        `转换结果只包含MDAC原生字段，优先使用原生字段值`, true);
                    log(`数据转换正确: ${JSON.stringify(result)}`, 'success');
                } else {
                    testStats.failed++;
                    showResult('❌ 数据转换标准化测试失败', 
                        `转换结果包含非MDAC字段或字段值不正确`, false);
                    log(`数据转换错误: ${JSON.stringify(result)}`, 'error');
                }

            } catch (error) {
                testStats.failed++;
                showResult('❌ 数据转换标准化测试失败', error.message, false);
                log(`数据转换测试失败: ${error.message}`, 'error');
            }
        }

        // 端到端流程测试
        function testEndToEndFlow() {
            log('开始端到端流程测试...', 'info');
            testStats.total++;

            try {
                // 模拟完整的数据流程
                const testText = '姓名：王五，护照号：G87654321，性别：女，邮箱：<EMAIL>';
                
                // 1. 字段提取
                const preprocessor = new SmartPreprocessor();
                const extractResult = preprocessor.preprocess(testText);
                
                if (!extractResult.success) {
                    throw new Error('字段提取失败');
                }

                // 2. 数据转换
                const config = new MDACFieldMappingConfig();
                const transformResult = config.transformAIToMDAC(extractResult.data);

                // 3. 验证最终结果
                const finalFields = Object.keys(transformResult);
                const allFieldsValid = finalFields.every(field => 
                    MDAC_NATIVE_FIELDS.some(nativeField => nativeField.id === field)
                );

                const hasExpectedData = transformResult.name && transformResult.passNo && 
                                      transformResult.sex && transformResult.email;

                const isSuccess = allFieldsValid && hasExpectedData;

                if (isSuccess) {
                    testStats.passed++;
                    showResult('✅ 端到端流程测试通过', 
                        `完整流程正确使用MDAC原生字段名，数据转换正确`, true);
                    log(`端到端测试成功: ${JSON.stringify(transformResult)}`, 'success');
                } else {
                    testStats.failed++;
                    showResult('❌ 端到端流程测试失败', 
                        `流程中存在非MDAC字段或数据丢失`, false);
                    log(`端到端测试失败: ${JSON.stringify(transformResult)}`, 'error');
                }

            } catch (error) {
                testStats.failed++;
                showResult('❌ 端到端流程测试失败', error.message, false);
                log(`端到端测试失败: ${error.message}`, 'error');
            }

            // 显示最终统计
            showResult('📊 测试统计', 
                `总测试数: ${testStats.total}, 通过: ${testStats.passed}, 失败: ${testStats.failed}`, 
                testStats.failed === 0);
        }

        // 清除结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('testLog').innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0 };
            log('测试结果已清除', 'info');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initializePage);
    </script>
</body>
</html>
