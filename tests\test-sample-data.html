<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试示例数据填充</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { padding: 8px; width: 300px; border: 1px solid #ccc; border-radius: 4px; }
        .test-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; }
        .test-btn:hover { background: #0056b3; }
        .results { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>MDAC示例数据填充测试</h1>
    
    <div class="form-group">
        <label for="passengerName">姓名 (Name)</label>
        <input type="text" id="passengerName" placeholder="LI MING">
    </div>
    
    <div class="form-group">
        <label for="passportNo">护照号码 (Passport No.)</label>
        <input type="text" id="passportNo" placeholder="G12345678">
    </div>
    
    <div class="form-group">
        <label for="birthDate">出生日期 (Date of Birth)</label>
        <input type="date" id="birthDate">
    </div>
    
    <div class="form-group">
        <label for="nationality">国籍 (Nationality)</label>
        <select id="nationality">
            <option value="">请选择国籍</option>
            <option value="CHN">中国 (CHN)</option>
            <option value="USA">美国 (USA)</option>
            <option value="GBR">英国 (GBR)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="gender">性别 (Gender)</label>
        <select id="gender">
            <option value="">请选择</option>
            <option value="1">男性 (Male)</option>
            <option value="2">女性 (Female)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="passportExpiry">护照有效期 (Passport Expiry)</label>
        <input type="date" id="passportExpiry">
    </div>
    
    <div class="form-group">
        <label for="email">电子邮箱 (Email)</label>
        <input type="email" id="email" placeholder="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="confirmEmail">确认邮箱 (Confirm Email)</label>
        <input type="email" id="confirmEmail" placeholder="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="phoneRegion">电话区号</label>
        <select id="phoneRegion">
            <option value="86" selected>+86 (中国)</option>
            <option value="60">+60 (马来西亚)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="phoneNumber">手机号 (Mobile)</label>
        <input type="text" id="phoneNumber" placeholder="*********">
    </div>
    
    <div class="form-group">
        <label for="arrivalDate">到达日期 (Arrival Date)</label>
        <input type="date" id="arrivalDate">
    </div>
    
    <div class="form-group">
        <label for="departureDate">离开日期 (Departure Date)</label>
        <input type="date" id="departureDate">
    </div>
    
    <div class="form-group">
        <label for="flightNo">航班号 (Flight No.)</label>
        <input type="text" id="flightNo" placeholder="MH123">
    </div>
    
    <div class="form-group">
        <label for="embark">出发国家/港口 (Embark)</label>
        <select id="embark">
            <option value="">请选择出发地</option>
            <option value="CHN">中国 (CHN)</option>
            <option value="SGP">新加坡 (SGP)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="travelMode">交通方式 (Mode of Travel)</label>
        <select id="travelMode">
            <option value="1">飞机 (Air)</option>
            <option value="2">陆路 (Land)</option>
            <option value="3">海运 (Sea)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="accommodationType">住宿类型 (Accommodation)</label>
        <select id="accommodationType">
            <option value="01">酒店 (Hotel)</option>
            <option value="02">亲友住所 (Relative's Place)</option>
            <option value="99">其他 (Others)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="address1">住宿地址 (Address)</label>
        <input type="text" id="address1" placeholder="Hotel KL City Center">
    </div>
    
    <div class="form-group">
        <label for="address2">地址第二行 (Address Line 2)</label>
        <input type="text" id="address2" placeholder="Floor 10, Room 1001">
    </div>
    
    <div class="form-group">
        <label for="city">城市 (City)</label>
        <input type="text" id="city" placeholder="KUALA LUMPUR">
    </div>
    
    <div class="form-group">
        <label for="state">州属 (State)</label>
        <select id="state">
            <option value="">请选择州属</option>
            <option value="14">吉隆坡 (KL)</option>
            <option value="10">雪兰莪 (Selangor)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="postcode">邮编 (Postcode)</label>
        <input type="text" id="postcode" placeholder="50000">
    </div>
    
    <button class="test-btn" onclick="fillSampleData()">🚀 填充示例数据</button>
    <button class="test-btn" onclick="clearForm()">🧹 清空表单</button>
    <button class="test-btn" onclick="validateData()">✅ 验证数据</button>
    
    <div id="results" class="results"></div>

    <script>
        // 示例数据 (从sidepanel.js复制)
        const sampleData = {
            // 基本个人信息
            name: 'ZHANG SAN',
            passNo: 'E12345678',
            dob: '01/01/1990',
            passExpDte: '01/01/2030',
            nationality: 'CHN',
            sex: '1',
            
            // 联系信息
            email: '<EMAIL>',
            confirmEmail: '<EMAIL>',
            region: '86',
            mobile: '*********0',
            
            // 旅行信息
            arrDt: '16/09/2025',
            depDt: '23/09/2025',
            vesselNm: 'MH370',
            trvlMode: '1',
            embark: 'CHN',
            
            // 住宿信息
            accommodationStay: '01',
            accommodationAddress1: 'Grand Hyatt Kuala Lumpur',
            accommodationAddress2: 'Jalan Pinang',
            accommodationCity: 'KUALA LUMPUR',
            accommodationState: '14',
            accommodationPostcode: '50450'
        };

        // 字段映射 (从sidepanel.js复制)
        const fieldMapping = {
            'passengerName': 'name',
            'passportNo': 'passNo',
            'birthDate': 'dob',
            'passportExpiry': 'passExpDte',
            'email': 'email',
            'confirmEmail': 'confirmEmail',
            'phoneRegion': 'region',
            'phoneNumber': 'mobile',
            'nationality': 'nationality',
            'gender': 'sex',
            'arrivalDate': 'arrDt',
            'departureDate': 'depDt',
            'flightNo': 'vesselNm',
            'embark': 'embark',
            'travelMode': 'trvlMode',
            'accommodationType': 'accommodationStay',
            'address1': 'accommodationAddress1',
            'address2': 'accommodationAddress2',
            'city': 'accommodationCity',
            'state': 'accommodationState',
            'postcode': 'accommodationPostcode'
        };

        function fillSampleData() {
            let filledCount = 0;
            let errors = [];
            
            Object.keys(fieldMapping).forEach(elementId => {
                const dataKey = fieldMapping[elementId];
                const element = document.getElementById(elementId);
                
                if (element && sampleData[dataKey] !== undefined) {
                    // 处理日期格式转换 (DD/MM/YYYY -> YYYY-MM-DD)
                    let value = sampleData[dataKey];
                    if (element.type === 'date' && value.includes('/')) {
                        const [day, month, year] = value.split('/');
                        value = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                    }
                    
                    element.value = value;
                    filledCount++;
                } else if (!element) {
                    errors.push(`元素 ${elementId} 未找到`);
                } else {
                    errors.push(`数据键 ${dataKey} 未找到`);
                }
            });
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <h3>填充结果</h3>
                <p class="success">✅ 成功填充 ${filledCount} 个字段</p>
                ${errors.length > 0 ? `<p class="error">❌ 错误: ${errors.join(', ')}</p>` : ''}
                <p>总字段数: ${Object.keys(fieldMapping).length}</p>
                <p>示例数据字段数: ${Object.keys(sampleData).length}</p>
            `;
        }

        function clearForm() {
            Object.keys(fieldMapping).forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.value = '';
                }
            });
            
            document.getElementById('results').innerHTML = '<p class="success">✅ 表单已清空</p>';
        }

        function validateData() {
            let validCount = 0;
            let emptyCount = 0;
            let totalCount = Object.keys(fieldMapping).length;
            
            Object.keys(fieldMapping).forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element && element.value.trim()) {
                    validCount++;
                } else {
                    emptyCount++;
                }
            });
            
            const completeness = ((validCount / totalCount) * 100).toFixed(1);
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <h3>验证结果</h3>
                <p class="${completeness >= 90 ? 'success' : completeness >= 70 ? 'warning' : 'error'}">
                    📊 表单完整度: ${completeness}% (${validCount}/${totalCount})
                </p>
                <p>✅ 已填充字段: ${validCount}</p>
                <p>❌ 空白字段: ${emptyCount}</p>
            `;
        }
    </script>
</body>
</html>
