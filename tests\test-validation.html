<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端到端测试验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🧪 端到端测试验证</h1>
    
    <div class="test-section info">
        <h3>测试环境检查</h3>
        <button onclick="checkEnvironment()">检查环境</button>
        <div id="env-status"></div>
    </div>

    <div class="test-section">
        <h3>API 模块加载测试</h3>
        <button onclick="testAPILoading()">测试 API 加载</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h3>智谱AI 文本测试</h3>
        <button onclick="testZhipuText()">测试智谱文本 API</button>
        <div id="zhipu-status"></div>
    </div>

    <div class="test-section">
        <h3>Kimi 视觉测试</h3>
        <input type="file" id="testImage" accept="image/*" multiple>
        <button onclick="testKimiVision()">测试 Kimi 视觉 API</button>
        <div id="kimi-status"></div>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <!-- 加载 LLM API 模块 -->
    <script src="LLM-api.js"></script>
    
    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            document.getElementById('log').textContent = testLog.join('\n');
            console.log(logEntry);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('log').textContent = '';
        }
        
        function updateStatus(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<p class="${isSuccess ? 'success' : 'error'}">${isSuccess ? '✅' : '❌'} ${message}</p>`;
        }
        
        async function checkEnvironment() {
            log('开始环境检查...');
            
            try {
                // 检查必要的全局对象
                const checks = [
                    { name: 'window.LLMAPI', exists: typeof window.LLMAPI !== 'undefined' },
                    { name: 'window.llmAPI', exists: typeof window.llmAPI !== 'undefined' },
                    { name: 'window.moonshotAPI', exists: typeof window.moonshotAPI !== 'undefined' },
                    { name: 'fetch API', exists: typeof fetch !== 'undefined' },
                    { name: 'performance API', exists: typeof performance !== 'undefined' }
                ];
                
                let allPassed = true;
                for (const check of checks) {
                    if (check.exists) {
                        log(`✅ ${check.name} 可用`);
                    } else {
                        log(`❌ ${check.name} 不可用`, 'error');
                        allPassed = false;
                    }
                }
                
                updateStatus('env-status', allPassed ? '环境检查通过' : '环境检查失败', allPassed);
                
            } catch (error) {
                log(`环境检查异常: ${error.message}`, 'error');
                updateStatus('env-status', `环境检查异常: ${error.message}`, false);
            }
        }
        
        async function testAPILoading() {
            log('开始 API 模块加载测试...');
            
            try {
                if (!window.llmAPI) {
                    throw new Error('llmAPI 实例不存在');
                }
                
                // 测试基本方法是否存在
                const methods = ['setApiKey', 'preprocessImage', 'extractDataFromImagesVision', 'buildExtractionPrompt'];
                for (const method of methods) {
                    if (typeof window.llmAPI[method] !== 'function') {
                        throw new Error(`方法 ${method} 不存在或不是函数`);
                    }
                    log(`✅ 方法 ${method} 可用`);
                }
                
                // 测试 API Key 设置
                window.llmAPI.setApiKey('test-key');
                log('✅ API Key 设置成功');
                
                updateStatus('api-status', 'API 模块加载测试通过', true);
                
            } catch (error) {
                log(`API 模块加载测试失败: ${error.message}`, 'error');
                updateStatus('api-status', `API 模块加载测试失败: ${error.message}`, false);
            }
        }
        
        async function testZhipuText() {
            log('开始智谱AI 文本测试...');
            
            try {
                // 模拟智谱 API 配置
                const zhipuAPI = {
                    apiKey: '453a03aa69ee406ea2e7291a6b148015.CnGyfph807m66CJ0',
                    endpoint: 'https://api.z.ai/api/paas/v4/chat/completions'
                };
                
                const testPrompt = window.llmAPI.buildExtractionPrompt('测试旅客信息：姓名：张三，护照号：E12345678');
                log(`✅ 提取提示词构建成功，长度: ${testPrompt.length} 字符`);
                
                // 注意：这里不实际调用 API，只测试提示词构建
                log('✅ 智谱AI 文本模块准备就绪');
                updateStatus('zhipu-status', '智谱AI 文本测试通过（提示词构建）', true);
                
            } catch (error) {
                log(`智谱AI 文本测试失败: ${error.message}`, 'error');
                updateStatus('zhipu-status', `智谱AI 文本测试失败: ${error.message}`, false);
            }
        }
        
        async function testKimiVision() {
            log('开始 Kimi 视觉测试...');
            
            try {
                const fileInput = document.getElementById('testImage');
                if (!fileInput.files || fileInput.files.length === 0) {
                    throw new Error('请先选择测试图片');
                }
                
                const file = fileInput.files[0];
                log(`选择的测试图片: ${file.name}, 大小: ${(file.size / 1024).toFixed(2)} KB`);
                
                // 测试图片预处理
                const startTime = performance.now();
                const processedImage = await window.llmAPI.preprocessImage(file);
                const preprocessTime = Math.round(performance.now() - startTime);
                
                log(`✅ 图片预处理成功，耗时: ${preprocessTime}ms`);
                log(`处理后图片信息: ${processedImage.name}, MIME: ${processedImage.mimeType}, Base64长度: ${processedImage.base64.length}`);
                
                // 注意：这里不实际调用 Vision API，只测试预处理
                log('✅ Kimi 视觉模块准备就绪');
                updateStatus('kimi-status', `Kimi 视觉测试通过（图片预处理，耗时 ${preprocessTime}ms）`, true);
                
            } catch (error) {
                log(`Kimi 视觉测试失败: ${error.message}`, 'error');
                updateStatus('kimi-status', `Kimi 视觉测试失败: ${error.message}`, false);
            }
        }
        
        // 页面加载时自动检查环境
        window.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始自动环境检查...');
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
