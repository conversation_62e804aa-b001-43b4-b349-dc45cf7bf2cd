// MDAC统一字段转换器 - 深度字段统一核心组件
// 用途：提供统一的字段转换、验证和标准化功能
// 依赖：MDACFieldConstants, MDACFieldMappingConfig
// 技术栈：原生JavaScript
// 核心功能：字段转换、数据验证、格式标准化、兼容性处理

/**
 * 统一字段转换器
 * 整合字段常量和映射配置，提供统一的字段转换接口
 */
class UnifiedFieldTransformer {
    constructor() {
        // 初始化依赖
        this.initDependencies();

        // 转换统计
        this.transformStats = {
            totalTransforms: 0,
            successfulTransforms: 0,
            failedTransforms: 0,
            lastTransformTime: null
        };
    }

    /**
     * 初始化依赖组件
     */
    initDependencies() {
        // 加载字段常量
        try {
            if (typeof MDACFieldConstants !== 'undefined') {
                this.constants = new MDACFieldConstants();
                console.log('✅ 字段常量已加载到转换器');
            } else {
                console.error('❌ MDACFieldConstants 未定义');
                this.constants = null;
            }
        } catch (error) {
            console.error('❌ 字段常量加载失败:', error);
            this.constants = null;
        }

        // 加载字段映射配置
        try {
            if (typeof MDACFieldMappingConfig !== 'undefined') {
                this.mappingConfig = new MDACFieldMappingConfig();
                console.log('✅ 字段映射配置已加载到转换器');
            } else {
                console.error('❌ MDACFieldMappingConfig 未定义');
                this.mappingConfig = null;
            }
        } catch (error) {
            console.error('❌ 字段映射配置加载失败:', error);
            this.mappingConfig = null;
        }
    }

    /**
     * 转换AI数据到MDAC标准格式
     * @param {Object} aiData - AI返回的原始数据
     * @param {Object} options - 转换选项
     * @returns {Object} 转换后的MDAC标准数据
     */
    transformAIToMDAC(aiData, options = {}) {
        const startTime = Date.now();
        this.transformStats.totalTransforms++;

        try {
            if (!this.constants || !this.mappingConfig) {
                throw new Error('转换器依赖组件未正确初始化');
            }

            console.log('🔄 开始转换AI数据到MDAC格式:', aiData);
            console.log('🔍 AI数据字段:', Object.keys(aiData));

            // 第一步：使用字段常量进行初步转换
            let transformedData = this.constants.transformAIToStandard(aiData);
            console.log('📋 字段常量转换后:', transformedData);

            // 第二步：使用映射配置进行深度转换
            if (this.mappingConfig) {
                transformedData = this.mappingConfig.transformAIToMDAC(transformedData);
                console.log('📋 映射配置转换后:', transformedData);
            }

            // 第三步：应用数据验证和清理
            const validationResult = this.validateAndCleanData(transformedData, options);
            transformedData = validationResult.data;
            console.log('📋 数据验证后:', transformedData);
            console.log('🔍 验证结果:', {
                errors: validationResult.validation.errors,
                warnings: validationResult.validation.warnings,
                isValid: validationResult.validation.isValid
            });

            // 第四步：应用字段格式化
            transformedData = this.applyFieldFormatting(transformedData, options);
            console.log('📋 字段格式化后:', transformedData);

            // 更新统计信息
            this.transformStats.successfulTransforms++;
            this.transformStats.lastTransformTime = Date.now();

            const transformTime = Date.now() - startTime;
            console.log(`✅ AI数据转换完成，耗时 ${transformTime}ms:`, transformedData);

            return {
                success: true,
                data: transformedData,
                stats: {
                    transformTime,
                    sourceFieldCount: Object.keys(aiData).length,
                    targetFieldCount: Object.keys(transformedData).length,
                    transformRatio: Object.keys(transformedData).length / Object.keys(aiData).length
                }
            };

        } catch (error) {
            this.transformStats.failedTransforms++;
            console.error('❌ AI数据转换失败:', error);

            return {
                success: false,
                error: error.message,
                data: null,
                stats: {
                    transformTime: Date.now() - startTime,
                    sourceFieldCount: Object.keys(aiData).length,
                    targetFieldCount: 0,
                    transformRatio: 0
                }
            };
        }
    }

    /**
     * 转换MDAC数据到AI格式
     * @param {Object} mdacData - MDAC标准数据
     * @param {Object} options - 转换选项
     * @returns {Object} 转换后的AI格式数据
     */
    transformMDACToAI(mdacData, options = {}) {
        try {
            if (!this.constants || !this.mappingConfig) {
                throw new Error('转换器依赖组件未正确初始化');
            }

            console.log('🔄 开始转换MDAC数据到AI格式:', mdacData);

            // 使用映射配置进行反向转换
            let aiData = this.mappingConfig.transformMDACToAI(mdacData);

            // 应用反向兼容性映射
            aiData = this.applyReverseCompatibilityMapping(aiData);

            console.log('✅ MDAC数据转换完成:', aiData);

            return {
                success: true,
                data: aiData,
                stats: {
                    sourceFieldCount: Object.keys(mdacData).length,
                    targetFieldCount: Object.keys(aiData).length,
                    transformRatio: Object.keys(aiData).length / Object.keys(mdacData).length
                }
            };

        } catch (error) {
            console.error('❌ MDAC数据转换失败:', error);

            return {
                success: false,
                error: error.message,
                data: null,
                stats: {
                    sourceFieldCount: Object.keys(mdacData).length,
                    targetFieldCount: 0,
                    transformRatio: 0
                }
            };
        }
    }

    /**
     * 验证和清理数据
     * @param {Object} data - 原始数据
     * @param {Object} options - 验证选项
     * @returns {Object} 清理后的数据
     */
    validateAndCleanData(data, options = {}) {
        const cleaned = {};
        const validationErrors = [];
        const validationWarnings = [];

        if (!this.constants) {
            console.warn('⚠️ 字段常量未初始化，跳过验证');
            return data;
        }

        // 验证每个字段
        for (const [field, value] of Object.entries(data)) {
            if (value === undefined || value === null || value === '') {
                continue;
            }

            // 获取标准字段名
            const standardField = this.constants.getStandardFieldName(field);
            console.log(`🔍 处理字段: ${field} -> ${standardField} = ${value}`);

            // 验证字段
            const validation = this.validateField(standardField, value);
            if (!validation.valid) {
                validationErrors.push({
                    field: standardField,
                    error: validation.error,
                    value: value
                });
                continue;
            }

            // 清理字段值
            const cleanedValue = this.cleanFieldValue(standardField, value);
            if (cleanedValue !== null) {
                cleaned[standardField] = cleanedValue;
            } else {
                validationWarnings.push({
                    field: standardField,
                    warning: '字段值清理后为空',
                    value: value
                });
            }
        }

        // 验证必填字段
        if (options.validateRequired !== false) {
            const missingRequiredFields = this.getMissingRequiredFields(cleaned);
            if (missingRequiredFields.length > 0) {
                validationErrors.push({
                    type: 'missing_required',
                    fields: missingRequiredFields,
                    error: `缺少必填字段: ${missingRequiredFields.join(', ')}`
                });
            }
        }

        // 记录验证结果
        if (validationErrors.length > 0) {
            console.warn('⚠️ 数据验证错误:', validationErrors);
            validationErrors.forEach(error => {
                console.warn(`  • ${error.field || error.type}: ${error.error}`);
            });
        }

        if (validationWarnings.length > 0) {
            console.log('ℹ️ 数据验证警告:', validationWarnings);
            validationWarnings.forEach(warning => {
                console.log(`  • ${warning.field}: ${warning.warning}`);
            });
        }

        return {
            data: cleaned,
            validation: {
                errors: validationErrors,
                warnings: validationWarnings,
                isValid: validationErrors.length === 0
            }
        };
    }

    /**
     * 验证单个字段
     * @param {string} field - 字段名
     * @param {any} value - 字段值
     * @returns {Object} 验证结果
     */
    validateField(field, value) {
        if (!this.constants) {
            return { valid: true };
        }

        // 使用映射配置的验证规则
        if (this.mappingConfig) {
            return this.mappingConfig.validateField(field, value);
        }

        // 基础验证
        if (this.constants.isRequiredField(field) && (!value || value.toString().trim() === '')) {
            return { valid: false, error: `${field} 是必填字段` };
        }

        return { valid: true };
    }

    /**
     * 清理字段值
     * @param {string} field - 字段名
     * @param {any} value - 原始值
     * @returns {any} 清理后的值
     */
    cleanFieldValue(field, value) {
        if (value === undefined || value === null || value === '') {
            return null;
        }

        let cleanedValue = value;

        // 字符串值处理
        if (typeof cleanedValue === 'string') {
            cleanedValue = cleanedValue.trim();
        }

        // 使用映射配置的值转换
        if (this.mappingConfig) {
            cleanedValue = this.mappingConfig.transformFieldValue(field, cleanedValue);
        }

        // 特定字段格式化
        switch (field) {
            case 'name':
            case 'passNo':
            case 'nationality':
                if (typeof cleanedValue === 'string') {
                    cleanedValue = cleanedValue.toUpperCase();
                }
                break;
            case 'email':
                if (typeof cleanedValue === 'string') {
                    cleanedValue = cleanedValue.toLowerCase();
                }
                break;
        }

        return cleanedValue;
    }

    /**
     * 应用字段格式化
     * @param {Object} data - 数据
     * @param {Object} options - 格式化选项
     * @returns {Object} 格式化后的数据
     */
    applyFieldFormatting(data, options = {}) {
        if (!this.constants) {
            return data;
        }

        const formatted = { ...data };

        // 日期格式化
        if (options.formatDates !== false) {
            const dateFields = this.constants.FIELD_TYPES.DATE_FIELDS || [];
            dateFields.forEach(field => {
                if (formatted[field]) {
                    formatted[field] = this.formatDateField(formatted[field]);
                }
            });
        }

        return formatted;
    }

    /**
     * 格式化日期字段
     * @param {string} dateStr - 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDateField(dateStr) {
        if (!dateStr || typeof dateStr !== 'string') {
            return dateStr;
        }

        // 如果已经有映射配置，使用配置的日期转换
        if (this.mappingConfig && this.mappingConfig.transformDateField) {
            return this.mappingConfig.transformDateField(dateStr);
        }

        // 基础日期格式化
        return dateStr; // 保持原样
    }

    /**
     * 应用反向兼容性映射
     * @param {Object} aiData - AI格式数据
     * @returns {Object} 应用兼容性映射后的数据
     */
    applyReverseCompatibilityMapping(aiData) {
        if (!this.constants) {
            return aiData;
        }

        const result = {};

        for (const [field, value] of Object.entries(aiData)) {
            // 查找AI字段名的可能兼容性映射
            let mappedField = field;

            // 检查是否是标准字段的反向映射
            for (const [compatField, standardField] of Object.entries(this.constants.COMPATIBILITY_MAPPING)) {
                if (standardField === field) {
                    mappedField = compatField;
                    break;
                }
            }

            result[mappedField] = value;
        }

        return result;
    }

    /**
     * 获取缺失的必填字段
     * @param {Object} data - 数据
     * @returns {Array} 缺失的必填字段列表
     */
    getMissingRequiredFields(data) {
        if (!this.constants) {
            return [];
        }

        const missingFields = [];
        const requiredFields = this.constants.REQUIRED_FIELDS || [];

        requiredFields.forEach(field => {
            if (!data[field] || data[field] === '' || data[field] === null) {
                missingFields.push(field);
            }
        });

        return missingFields;
    }

    /**
     * 获取转换统计信息
     * @returns {Object} 统计信息
     */
    getTransformStats() {
        return { ...this.transformStats };
    }

    /**
     * 重置转换统计
     */
    resetTransformStats() {
        this.transformStats = {
            totalTransforms: 0,
            successfulTransforms: 0,
            failedTransforms: 0,
            lastTransformTime: null
        };
    }

    /**
     * 获取转换器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            initialized: !!(this.constants && this.mappingConfig),
            constantsLoaded: !!this.constants,
            mappingConfigLoaded: !!this.mappingConfig,
            stats: this.getTransformStats()
        };
    }
}

// 创建全局实例
window.UnifiedFieldTransformer = UnifiedFieldTransformer;

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedFieldTransformer;
}