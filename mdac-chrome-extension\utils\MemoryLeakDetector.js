// 内存泄漏检测器 - 自动内存管理
// 用途：监控内存使用，检测潜在泄漏，自动清理资源
// 依赖：Chrome Extension APIs, Performance API
// 技术栈：原生JavaScript + 性能监控API
// 核心功能：内存监控、泄漏检测、自动清理、资源管理
// 重要：防止内存泄漏，保持扩展长期稳定运行

class MemoryLeakDetector {
    constructor() {
        // 内存使用历史
        this.memoryHistory = [];

        // 泄漏检测配置
        this.detectionConfig = {
            maxMemoryMB: 100,              // 最大内存使用限制
            growthThreshold: 1.5,          // 内存增长阈值
            consecutiveGrowthCount: 3,     // 连续增长次数
            checkInterval: 30000,          // 检查间隔（30秒）
            cleanupThreshold: 80,          // 清理阈值（MB）
            maxHistorySize: 100            // 历史记录最大数量
        };

        // 资源追踪
        this.trackedResources = {
            eventListeners: new Map(),
            timers: new Map(),
            observers: new Map(),
            cachedData: new Map(),
            domReferences: new WeakMap()
        };

        // 泄漏检测结果
        this.leakDetection = {
            isLeaking: false,
            leakSeverity: 'none', // none, low, medium, high
            leakSource: null,
            detectionTime: null,
            memoryGrowthRate: 0
        };

        // 清理策略
        this.cleanupStrategies = {
            LIGHT: 'light',      // 轻度清理：清理缓存和临时数据
            MEDIUM: 'medium',    // 中度清理：清理非关键资源
            DEEP: 'deep'         // 深度清理：清理大部分可释放资源
        };

        // 性能统计
        this.performanceStats = {
            totalChecks: 0,
            leaksDetected: 0,
            cleanupsPerformed: 0,
            memorySaved: 0,
            averageMemoryUsage: 0
        };

        this.init();
    }

    init() {
        console.log('🔍 MemoryLeakDetector 初始化...');

        try {
            // 启动内存监控
            this.startMemoryMonitoring();

            // 设置资源追踪
            this.setupResourceTracking();

            // 设置清理定时器
            this.setupCleanupTimer();

            // 设置内存警告
            this.setupMemoryWarnings();

            console.log('✅ MemoryLeakDetector 初始化完成');
        } catch (error) {
            console.error('❌ MemoryLeakDetector 初始化失败:', error);
        }
    }

    /**
     * 启动内存监控
     */
    startMemoryMonitoring() {
        if (!performance.memory) {
            console.warn('⚠️ 当前环境不支持内存监控API');
            return;
        }

        // 定期内存检查
        this.memoryCheckInterval = setInterval(() => {
            this.performMemoryCheck();
        }, this.detectionConfig.checkInterval);

        console.log(`⏰ 内存监控已启动，检查间隔: ${this.detectionConfig.checkInterval}ms`);
    }

    /**
     * 执行内存检查
     */
    performMemoryCheck() {
        try {
            if (!performance.memory) return;

            this.performanceStats.totalChecks++;

            const currentMemory = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };

            // 转换为MB
            const usedMemoryMB = currentMemory.usedJSHeapSize / (1024 * 1024);
            const totalMemoryMB = currentMemory.totalJSHeapSize / (1024 * 1024);

            // 记录内存历史
            this.memoryHistory.push({
                usedMemoryMB,
                totalMemoryMB,
                timestamp: currentMemory.timestamp
            });

            // 限制历史记录数量
            if (this.memoryHistory.length > this.detectionConfig.maxHistorySize) {
                this.memoryHistory.shift();
            }

            // 更新性能统计
            this.updatePerformanceStats(usedMemoryMB);

            // 检测内存泄漏
            this.detectMemoryLeak();

            // 检查是否需要清理
            if (usedMemoryMB > this.detectionConfig.cleanupThreshold) {
                this.performAutomaticCleanup(usedMemoryMB);
            }

            // 记录内存状态
            console.log(`📊 内存检查 - 使用: ${usedMemoryMB.toFixed(2)}MB, 总计: ${totalMemoryMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('❌ 内存检查失败:', error);
        }
    }

    /**
     * 检测内存泄漏
     */
    detectMemoryLeak() {
        try {
            if (this.memoryHistory.length < 5) return; // 需要足够的历史数据

            const recentHistory = this.memoryHistory.slice(-5);
            const memoryValues = recentHistory.map(record => record.usedMemoryMB);

            // 检查连续增长
            let growthCount = 0;
            for (let i = 1; i < memoryValues.length; i++) {
                if (memoryValues[i] > memoryValues[i - 1]) {
                    growthCount++;
                }
            }

            // 计算内存增长率
            const firstMemory = memoryValues[0];
            const lastMemory = memoryValues[memoryValues.length - 1];
            const growthRate = (lastMemory - firstMemory) / firstMemory;

            // 检测泄漏条件
            const isLeaking = (
                growthCount >= this.detectionConfig.consecutiveGrowthCount &&
                growthRate > (this.detectionConfig.growthThreshold - 1) &&
                lastMemory > (this.detectionConfig.maxMemoryMB * 0.7) // 超过70%限制
            );

            if (isLeaking) {
                this.reportMemoryLeak({
                    growthCount,
                    growthRate,
                    currentMemory: lastMemory,
                    detectionTime: new Date().toISOString()
                });
            }

            // 更新泄漏检测状态
            this.updateLeakStatus(isLeaking, growthRate, lastMemory);

        } catch (error) {
            console.error('❌ 内存泄漏检测失败:', error);
        }
    }

    /**
     * 报告内存泄漏
     */
    reportMemoryLeak(leakData) {
        this.performanceStats.leaksDetected++;

        console.warn('🚨 内存泄漏检测', {
            growthCount: leakData.growthCount,
            growthRate: `${(leakData.growthRate * 100).toFixed(2)}%`,
            currentMemory: `${leakData.currentMemory.toFixed(2)}MB`,
            detectionTime: leakData.detectionTime
        });

        // 触发内存泄漏事件
        const event = new CustomEvent('memoryLeakDetected', {
            detail: leakData
        });
        document.dispatchEvent(event);

        // 执行紧急清理
        this.performEmergencyCleanup();
    }

    /**
     * 更新泄漏状态
     */
    updateLeakStatus(isLeaking, growthRate, currentMemory) {
        this.leakDetection.isLeaking = isLeaking;
        this.leakDetection.memoryGrowthRate = growthRate;
        this.leakDetection.detectionTime = new Date().toISOString();

        if (isLeaking) {
            // 确定泄漏严重程度
            if (currentMemory > this.detectionConfig.maxMemoryMB) {
                this.leakDetection.leakSeverity = 'high';
            } else if (growthRate > 0.5) {
                this.leakDetection.leakSeverity = 'medium';
            } else {
                this.leakDetection.leakSeverity = 'low';
            }
        } else {
            this.leakDetection.leakSeverity = 'none';
        }
    }

    /**
     * 设置资源追踪
     */
    setupResourceTracking() {
        // 重写原生方法以追踪资源
        this.interceptEventListeners();
        this.interceptTimers();
        this.interceptObservers();

        console.log('📋 资源追踪系统已设置');
    }

    /**
     * 拦截事件监听器
     */
    interceptEventListeners() {
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const originalRemoveEventListener = EventTarget.prototype.removeEventListener;

        EventTarget.prototype.addEventListener = function(type, listener, options) {
            const listenerId = `${type}_${Date.now()}_${Math.random()}`;

            window.MemoryLeakDetector.trackEventListener(this, type, listenerId, listener);

            // 存储原始监听器以便追踪
            if (!this._trackedListeners) {
                this._trackedListeners = new Map();
            }
            this._trackedListeners.set(listenerId, { type, listener, options });

            return originalAddEventListener.call(this, type, listener, options);
        };

        EventTarget.prototype.removeEventListener = function(type, listener, options) {
            // 查找并移除追踪的记录
            if (this._trackedListeners) {
                for (const [id, tracked] of this._trackedListeners.entries()) {
                    if (tracked.type === type && tracked.listener === listener) {
                        window.MemoryLeakDetector.untrackEventListener(this, id);
                        this._trackedListeners.delete(id);
                        break;
                    }
                }
            }

            return originalRemoveEventListener.call(this, type, listener, options);
        };
    }

    /**
     * 追踪事件监听器
     */
    trackEventListener(target, type, listenerId, listener) {
        const targetId = this.getTargetId(target);

        if (!this.trackedResources.eventListeners.has(targetId)) {
            this.trackedResources.eventListeners.set(targetId, new Map());
        }

        this.trackedResources.eventListeners.get(targetId).set(listenerId, {
            type,
            listener,
            addedAt: Date.now(),
            stackTrace: new Error().stack
        });

        console.log(`📋 追踪事件监听器: ${targetId} - ${type}`);
    }

    /**
     * 取消追踪事件监听器
     */
    untrackEventListener(target, listenerId) {
        const targetId = this.getTargetId(target);
        const targetListeners = this.trackedResources.eventListeners.get(targetId);

        if (targetListeners) {
            targetListeners.delete(listenerId);
            if (targetListeners.size === 0) {
                this.trackedResources.eventListeners.delete(targetId);
            }
        }

        console.log(`🗑️ 取消追踪事件监听器: ${targetId}`);
    }

    /**
     * 拦截定时器
     */
    interceptTimers() {
        const originalSetInterval = window.setInterval;
        const originalSetTimeout = window.setTimeout;
        const originalClearInterval = window.clearInterval;
        const originalClearTimeout = window.clearTimeout;

        window.setInterval = (handler, timeout, ...args) => {
            const timerId = originalSetInterval.call(window, handler, timeout, ...args);
            this.trackTimer('interval', timerId, handler, timeout);
            return timerId;
        };

        window.setTimeout = (handler, timeout, ...args) => {
            const timerId = originalSetTimeout.call(window, handler, timeout, ...args);
            this.trackTimer('timeout', timerId, handler, timeout);
            return timerId;
        };

        window.clearInterval = (timerId) => {
            this.untrackTimer('interval', timerId);
            return originalClearInterval.call(window, timerId);
        };

        window.clearTimeout = (timerId) => {
            this.untrackTimer('timeout', timerId);
            return originalClearTimeout.call(window, timerId);
        };
    }

    /**
     * 追踪定时器
     */
    trackTimer(type, timerId, handler, timeout) {
        this.trackedResources.timers.set(timerId, {
            type,
            handler,
            timeout,
            createdAt: Date.now(),
            stackTrace: new Error().stack
        });

        console.log(`⏰ 追踪定时器: ${type} - ${timerId}`);
    }

    /**
     * 取消追踪定时器
     */
    untrackTimer(type, timerId) {
        if (this.trackedResources.timers.has(timerId)) {
            this.trackedResources.timers.delete(timerId);
            console.log(`🗑️ 取消追踪定时器: ${type} - ${timerId}`);
        }
    }

    /**
     * 设置清理定时器
     */
    setupCleanupTimer() {
        // 定期清理（每5分钟）
        this.cleanupInterval = setInterval(() => {
            this.performRoutineCleanup();
        }, 5 * 60 * 1000);

        // 内存警告清理（当内存使用超过阈值）
        this.memoryWarningCleanup = () => {
            if (this.leakDetection.leakSeverity !== 'none') {
                this.performAutomaticCleanup(this.getCurrentMemoryUsage());
            }
        };

        document.addEventListener('memoryLeakDetected', this.memoryWarningCleanup);

        console.log('🧹 清理定时器已设置');
    }

    /**
     * 执行例行清理
     */
    performRoutineCleanup() {
        try {
            console.log('🧹 执行例行内存清理...');

            // 清理过期缓存
            this.cleanupExpiredCache();

            // 清理孤立的事件监听器
            this.cleanupOrphanedEventListeners();

            // 清理过期的定时器
            this.cleanupExpiredTimers();

            // 清理DOM引用
            this.cleanupDOMReferences();

            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
                console.log('♻️ 强制垃圾回收完成');
            }

            this.performanceStats.cleanupsPerformed++;

            console.log('✅ 例行内存清理完成');

        } catch (error) {
            console.error('❌ 例行清理失败:', error);
        }
    }

    /**
     * 执行自动清理
     */
    performAutomaticCleanup(currentMemory) {
        try {
            console.log(`🚨 执行自动内存清理 (当前: ${currentMemory.toFixed(2)}MB)...`);

            let cleanupLevel = this.cleanupStrategies.LIGHT;

            // 根据内存使用情况确定清理级别
            if (currentMemory > this.detectionConfig.maxMemoryMB) {
                cleanupLevel = this.cleanupStrategies.DEEP;
            } else if (currentMemory > (this.detectionConfig.maxMemoryMB * 0.9)) {
                cleanupLevel = this.cleanupStrategies.MEDIUM;
            }

            this.performCleanupByLevel(cleanupLevel);

            console.log(`✅ 自动内存清理完成 (级别: ${cleanupLevel})`);

        } catch (error) {
            console.error('❌ 自动清理失败:', error);
        }
    }

    /**
     * 执行紧急清理
     */
    performEmergencyCleanup() {
        try {
            console.log('🆘 执行紧急内存清理...');

            // 执行最深度清理
            this.performCleanupByLevel(this.cleanupStrategies.DEEP);

            // 清理所有非关键缓存
            this.clearAllCaches();

            // 通知用户
            this.notifyUser('内存泄漏已检测并清理', 'warning');

            console.log('✅ 紧急内存清理完成');

        } catch (error) {
            console.error('❌ 紧急清理失败:', error);
        }
    }

    /**
     * 按级别执行清理
     */
    performCleanupByLevel(level) {
        switch (level) {
            case this.cleanupStrategies.LIGHT:
                this.cleanupLight();
                break;
            case this.cleanupStrategies.MEDIUM:
                this.cleanupLight();
                this.cleanupMedium();
                break;
            case this.cleanupStrategies.DEEP:
                this.cleanupLight();
                this.cleanupMedium();
                this.cleanupDeep();
                break;
        }
    }

    /**
     * 轻度清理
     */
    cleanupLight() {
        // 清理过期缓存
        if (window.PredictiveCacheManager) {
            window.PredictiveCacheManager.cleanupOldPredictions();
        }

        // 清理模块加载器缓存
        if (window.ModuleLoader) {
            window.ModuleLoader.clearCache();
        }

        console.log('🧹 轻度清理完成');
    }

    /**
     * 中度清理
     */
    cleanupMedium() {
        // 清理非关键资源
        this.cleanupOrphanedEventListeners();
        this.cleanupExpiredTimers();

        console.log('🧹 中度清理完成');
    }

    /**
     * 深度清理
     */
    cleanupDeep() {
        // 清理所有可释放资源
        this.cleanupAllEventListeners();
        this.cleanupAllTimers();
        this.cleanupDOMReferences();

        console.log('🧹 深度清理完成');
    }

    /**
     * 辅助方法实现
     */
    getTargetId(target) {
        if (target.id) return `#${target.id}`;
        if (target.tagName) return target.tagName.toLowerCase();
        return 'unknown';
    }

    getCurrentMemoryUsage() {
        if (!performance.memory) return 0;
        return performance.memory.usedJSHeapSize / (1024 * 1024);
    }

    updatePerformanceStats(usedMemoryMB) {
        const stats = this.performanceStats;
        const totalChecks = stats.totalChecks;

        stats.averageMemoryUsage = (stats.averageMemoryUsage * (totalChecks - 1) + usedMemoryMB) / totalChecks;
    }

    setupMemoryWarnings() {
        // 当内存使用接近限制时发出警告
        setInterval(() => {
            const currentMemory = this.getCurrentMemoryUsage();
            if (currentMemory > (this.detectionConfig.maxMemoryMB * 0.9)) {
                console.warn(`⚠️ 内存使用警告: ${currentMemory.toFixed(2)}MB / ${this.detectionConfig.maxMemoryMB}MB`);
            }
        }, 60000); // 每分钟检查
    }

    cleanupExpiredCache() {
        // 实现缓存清理逻辑
        console.log('🗑️ 清理过期缓存');
    }

    cleanupOrphanedEventListeners() {
        // 清理孤立的事件监听器
        console.log('🔗 清理孤立事件监听器');
    }

    cleanupExpiredTimers() {
        // 清理过期的定时器
        console.log('⏰ 清理过期定时器');
    }

    cleanupDOMReferences() {
        // 清理DOM引用
        console.log('🌐 清理DOM引用');
    }

    clearAllCaches() {
        // 清理所有缓存
        console.log('🗑️ 清理所有缓存');
    }

    notifyUser(message, type = 'info') {
        // 通知用户内存状态
        console.log(`📢 用户通知 [${type}]: ${message}`);
    }

    /**
     * 获取内存泄漏检测状态
     */
    getLeakDetectionStatus() {
        return {
            ...this.leakDetection,
            currentMemoryUsage: this.getCurrentMemoryUsage(),
            performanceStats: this.performanceStats,
            resourceCounts: {
                eventListeners: this.getResourceCount('eventListeners'),
                timers: this.getResourceCount('timers'),
                observers: this.getResourceCount('observers')
            }
        };
    }

    /**
     * 获取资源数量
     */
    getResourceCount(resourceType) {
        const resources = this.trackedResources[resourceType];
        if (resources instanceof Map) {
            return Array.from(resources.values()).reduce((total, targetResources) => {
                return total + (targetResources.size || 0);
            }, 0);
        }
        return resources.size || 0;
    }

    /**
     * 内存清理
     */
    cleanup() {
        // 停止监控
        if (this.memoryCheckInterval) {
            clearInterval(this.memoryCheckInterval);
        }

        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }

        // 移除事件监听器
        document.removeEventListener('memoryLeakDetected', this.memoryWarningCleanup);

        // 恢复原始方法
        this.restoreOriginalMethods();

        // 清理追踪的资源
        this.cleanupAllResources();

        console.log('🧹 MemoryLeakDetector 内存清理完成');
    }

    /**
     * 恢复原始方法
     */
    restoreOriginalMethods() {
        // 恢复事件监听器方法
        if (EventTarget.prototype.addEventListener._original) {
            EventTarget.prototype.addEventListener = EventTarget.prototype.addEventListener._original;
        }

        if (EventTarget.prototype.removeEventListener._original) {
            EventTarget.prototype.removeEventListener = EventTarget.prototype.removeEventListener._original;
        }

        // 恢复定时器方法
        if (window.setInterval._original) {
            window.setInterval = window.setInterval._original;
        }

        if (window.setTimeout._original) {
            window.setTimeout = window.setTimeout._original;
        }

        if (window.clearInterval._original) {
            window.clearInterval = window.clearInterval._original;
        }

        if (window.clearTimeout._original) {
            window.clearTimeout = window.clearTimeout._original;
        }
    }

    /**
     * 清理所有资源
     */
    cleanupAllResources() {
        this.cleanupAllEventListeners();
        this.cleanupAllTimers();
        this.cleanupDOMReferences();
        this.trackedResources.cachedData.clear();
    }
}

/**
 * 全局内存泄漏检测器实例
 */
window.MemoryLeakDetector = new MemoryLeakDetector();

// 便捷的内存检查函数
window.checkMemoryUsage = function() {
    return window.MemoryLeakDetector.getLeakDetectionStatus();
};

window.performManualCleanup = function() {
    return window.MemoryLeakDetector.performRoutineCleanup();
};

console.log('✅ MemoryLeakDetector 系统已初始化');