// MDAC Chrome扩展 - 侧边栏主要功能类
// 用途：提供MDAC表单智能填充功能的Chrome扩展侧边栏界面
// 依赖：Chrome Extension APIs, Doubao LLM API, UnifiedMultiModalProcessor, MDACFieldConstants
// 技术栈：原生JavaScript + Chrome Extension Manifest V3
// 核心功能：文本解析、图片识别、表单填充、数据持久化
// 重要：使用MDAC原生字段ID，零转换映射

class MDACExtension {
    constructor() {
        // 初始化字段常量
        if (typeof MDACFieldConstants !== 'undefined') {
            this.constants = new MDACFieldConstants();
        } else {
            console.error('❌ MDACFieldConstants 未加载');
            this.constants = null;
        }

        // 初始化统一字段转换器
        if (typeof UnifiedFieldTransformer !== 'undefined') {
            this.transformer = new UnifiedFieldTransformer();
            console.log('✅ 统一字段转换器已初始化');
        } else {
            console.error('❌ UnifiedFieldTransformer 未加载');
            this.transformer = null;
        }

        // 数据状态管理
        this.currentData = {};
        this.isConnected = false;
        this.tabInfo = null;

        // 字段锁定功能
        this.lockedFields = new Set();
        this.lockStorageKey = 'mdacFieldLocks';

        // 文件上传管理
        this.selectedFiles = [];

        // 自动解析功能
        this.autoParseTimer = null;
        this.autoParseDelay = 800; // ms
        this.lastParsedTextHash = '';

        // 配置管理
        this.themeKey = 'mdacTheme';
        this.contactKey = 'mdacContact';
        this.visionConfigKey = 'mdacVisionConfig';
        this.visionConfig = {
            confidenceThreshold: 0.55,
            enableMrzOcr: true,
            enableAddressEnhance: true,
            requireDiffConfirm: true
        };

  
        // 性能监控
        this.cacheStatsTimer = null;

        // 启动初始化
        this.init();
    }

    async init() {
        console.log('初始化 MDAC扩展侧边栏组件...');

        try {
            // 绑定事件监听器
            this.bindEvents();

            // 检查与MDAC网站的连接状态
            await this.checkConnection();

            // 加载字段锁定状态
            await this.loadFieldLocks();
            this.applyLockedStateToUI();

            // 只加载锁定字段的数据（非锁定字段保持空白）
            await this.loadLockedFieldData();

            // 加载主题配置
            this.loadTheme();

            // 加载联系信息快捷缓存
            await this.loadContactInfo();

            // 加载视觉模型配置
            await this.loadVisionConfig();

            console.log('✅ 侧边栏组件初始化完成');

            // 启动缓存统计定时器
            this.startCacheStatsLogging();

        } catch (error) {
            console.error('❌ MDAC扩展组件初始化失败:', error);
            console.error('错误堆栈:', error.stack);

            // 显示错误状态给用户
            this.showStatus(`初始化失败: ${error.message}`, 'error');

            // 仍然尝试继续运行，但某些功能可能不可用
            console.warn('⚠️ 尝试以降级模式继续运行...');
        }
    }

    bindEvents() {
        try {
            // 自动解析功能 - 监听旅客信息输入框
            const travelerInfoEl = document.getElementById('travelerInfo');
            if (travelerInfoEl) {
                travelerInfoEl.addEventListener('input', () => this.handleAutoParseInput());
                console.log('✅ 自动解析事件已绑定到 travelerInfo');
            } else {
                console.error('❌ 找不到 travelerInfo 元素，自动解析功能将不工作');
            }

            // 主要操作按钮事件绑定
            const generateBtn = document.getElementById('generateScriptBtn');
            const fillBtn = document.getElementById('fillSampleBtn');
            const parseBtn = document.getElementById('aiParseBtn');
            const clearBtn = document.getElementById('clearBtn');
            const analyzeBtn = document.getElementById('analyzeImagesBtn');
            const copyBtn = document.getElementById('copyScriptBtn');
            const beautifyBtn = document.getElementById('beautifyScriptBtn');
            const minifyBtn = document.getElementById('minifyScriptBtn');
            const resetBtn = document.getElementById('resetScriptBtn');

            if (generateBtn) generateBtn.addEventListener('click', () => this.generateAndFillForm());
            if (fillBtn) fillBtn.addEventListener('click', () => this.fillSampleText());
            if (parseBtn) parseBtn.addEventListener('click', () => this.handleAIParse());
            if (clearBtn) {
              console.log('✅ 找到清空按钮，绑定事件');
              clearBtn.addEventListener('click', async () => {
                  console.log('🧹 清空按钮被点击');
                  await this.clearAll();
              });
          } else {
              console.error('❌ 未找到清空按钮');
          }
            if (analyzeBtn) analyzeBtn.addEventListener('click', () => this.extractDataFromImages(this.selectedFiles));
            if (copyBtn) copyBtn.addEventListener('click', () => this.copyScript());
            if (beautifyBtn) beautifyBtn.addEventListener('click', () => this.beautifyScript());
            if (minifyBtn) minifyBtn.addEventListener('click', () => this.minifyScript());
            if (resetBtn) resetBtn.addEventListener('click', () => this.resetScript());

            // 图片上传相关事件
            this.initImageUploadEvents();

            // 表单字段实时同步事件监听器
            this.initFormFieldListeners = () => {
                // 定义需要监听的表单字段
                const formFields = [
                    'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpDte',
                    'email', 'confirmEmail', 'region', 'mobile', 'arrDt', 'depDt',
                    'vesselNm', 'trvlMode', 'embark', 'accommodationStay',
                    'accommodationAddress1', 'accommodationAddress2',
                    'accommodationState', 'accommodationCity', 'accommodationPostcode'
                ];

                formFields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element && !element.__mdacBound) {
                        // 监听输入事件（实时同步）
                        element.addEventListener('input', () => {
                            this.handleFormFieldChange(fieldId, element.value);
                        });

                        // 监听变化事件（失去焦点时同步）
                        element.addEventListener('change', () => {
                            this.handleFormFieldChange(fieldId, element.value);
                        });

                        element.__mdacBound = true;
                        console.log(`✅ 表单字段监听器已绑定: ${fieldId}`);
                    }
                });
            };

            // 字段锁定相关事件
            this.initFieldLockEvents = () => {
                const lockButtons = document.querySelectorAll('.lock-btn[data-field]');
                lockButtons.forEach(btn => {
                    const fieldId = btn.getAttribute('data-field');
                    if (!fieldId) return;
                    if (!btn.__mdacBound) {
                        btn.addEventListener('click', () => this.toggleFieldLock(fieldId, btn));
                        btn.__mdacBound = true;
                    }
                });

                // 使用字段常量定义分组
                const passengerGroup = [
                    'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpDte'
                ];

                const travelGroup = [
                    'arrDt', 'depDt', 'vesselNm', 'trvlMode', 'embark',
                    'accommodationStay', 'accommodationAddress1', 'accommodationAddress2',
                    'accommodationState', 'accommodationCity', 'accommodationPostcode'
                ];

                const contactGroup = [
                    'email', 'confirmEmail', 'region', 'mobile'
                ];

                const pBtn = document.getElementById('lockAllPassengerBtn');
                if (pBtn && !pBtn.__mdacBound) {
                    pBtn.addEventListener('click', () => {
                        const shouldLock = !passengerGroup.every(id => this.lockedFields.has(id));
                        passengerGroup.forEach(id => this.setFieldLock(id, shouldLock));
                        this.updateGroupLockButton(pBtn, shouldLock);
                    });
                    pBtn.__mdacBound = true;
                }
                const tBtn = document.getElementById('lockAllTravelBtn');
                if (tBtn && !tBtn.__mdacBound) {
                    tBtn.addEventListener('click', () => {
                        const shouldLock = !travelGroup.every(id => this.lockedFields.has(id));
                        travelGroup.forEach(id => this.setFieldLock(id, shouldLock));
                        this.updateGroupLockButton(tBtn, shouldLock);
                    });
                    tBtn.__mdacBound = true;
                }
                const cBtn = document.getElementById('lockAllContactBtn');
                if (cBtn && !cBtn.__mdacBound) {
                    cBtn.addEventListener('click', () => {
                        const shouldLock = !contactGroup.every(id => this.lockedFields.has(id));
                        contactGroup.forEach(id => this.setFieldLock(id, shouldLock));
                        this.updateGroupLockButton(cBtn, shouldLock);
                    });
                    cBtn.__mdacBound = true;
                }
            };

            // 调用锁定事件初始化
            this.initFieldLockEvents();

            // 初始化表单字段实时同步监听器
            this.initFormFieldListeners();

            // 上传按钮事件绑定
            const uploadBtn = document.getElementById('uploadDropzone');
            if (uploadBtn) {
                uploadBtn.addEventListener('click', () => {
                    try {
                        console.log('📁 上传按钮被点击');

                        // 优先使用UIController的文件上传功能
                        if (window.uiController) {
                            const unifiedFileInput = document.getElementById('unifiedFileInput');
                            if (unifiedFileInput) {
                                unifiedFileInput.click();
                                console.log('✅ 使用统一文件输入对话框');
                                return;
                            } else {
                                console.warn('⚠️ 统一文件输入元素不存在，尝试初始化');
                                // 尝试重新初始化UIController
                                window.uiController.setupUploadIntegration();
                                setTimeout(() => {
                                    const retryFileInput = document.getElementById('unifiedFileInput');
                                    if (retryFileInput) {
                                        retryFileInput.click();
                                    } else {
                                        console.error('❌ 无法创建统一文件输入元素');
                                        this.showStatus('文件上传功能初始化失败', 'error');
                                    }
                                }, 100);
                                return;
                            }
                        }

                        // 回退到传统图片上传
                        const imageUpload = document.getElementById('imageUpload');
                        if (imageUpload) {
                            imageUpload.click();
                            console.log('✅ 使用传统图片上传对话框');
                        } else {
                            console.warn('⚠️ 所有文件上传方式都不可用');
                            this.showStatus('文件上传功能暂时不可用', 'error');
                        }
                    } catch (e) {
                        console.error('❌ 上传按钮点击失败:', e);
                        this.showStatus('文件上传失败', 'error');
                    }
                });
            }

            // 主题切换事件
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) themeBtn.addEventListener('click', () => this.toggleTheme());

            // 高级设置事件
            this.bindAdvancedSettings();

            console.log('✅ 事件绑定完成');

        } catch (error) {
            console.error('❌ 事件绑定失败:', error);
        }
    }

    // 统一入口：若选择了文件则走多模态处理，否则走文本解析
    async handleAIParse() {
        try {
            // 若UI控制器可用且检测到已选择文件，走多模态统一流程
            if (window.uiController && typeof window.uiController.integrateWithExistingAIParse === 'function') {
                const used = await window.uiController.integrateWithExistingAIParse();
                if (used) return;
            }
            // 默认回退到文本/本地图片解析
            this.parseContent();
        } catch (err) {
            console.error('handleAIParse error:', err);
            this.parseContent();
        }
    }

    // 供 unified/integration-patch.js 调用，将AI结果映射到侧边栏表单并生成注入脚本
    applyFieldUpdates(data) {
        if (!data || typeof data !== 'object') return;
        try {
            this.currentData = { ...this.currentData, ...data };
            this.updateFormFields();
            this.saveData?.();
            this.generateScript();
            this.showStatus('已应用AI解析结果并生成脚本', 'success');
        } catch (e) {
            console.error('applyFieldUpdates error:', e);
            this.showStatus('应用AI结果失败', 'error');
        }
    }

    initImageUploadEvents() {
        const imageUpload = document.getElementById('imageUpload');
        const dropZone = document.getElementById('dropZone');

        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => this.handleImageSelect(e));
        }

        if (dropZone) {
            // 拖拽上传功能
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                this.handleImageDrop(e);
            });

            // 点击上传功能
            dropZone.addEventListener('click', () => {
                if (imageUpload) imageUpload.click();
            });
        }
    }

  
    bindAdvancedSettings() {
        // 置信度阈值设置
        const confidenceSlider = document.getElementById('confidenceThreshold');
        if (confidenceSlider) {
            confidenceSlider.addEventListener('input', (e) => {
                this.visionConfig.confidenceThreshold = parseFloat(e.target.value);
                this.saveVisionConfig();
            });
        }

        // OCR设置
        const mrzCheckbox = document.getElementById('enableMrzOcr');
        const addressCheckbox = document.getElementById('enableAddressEnhance');
        const diffCheckbox = document.getElementById('requireDiffConfirm');

        if (mrzCheckbox) {
            mrzCheckbox.addEventListener('change', (e) => {
                this.visionConfig.enableMrzOcr = e.target.checked;
                this.saveVisionConfig();
            });
        }

        if (addressCheckbox) {
            addressCheckbox.addEventListener('change', (e) => {
                this.visionConfig.enableAddressEnhance = e.target.checked;
                this.saveVisionConfig();
            });
        }

        if (diffCheckbox) {
            diffCheckbox.addEventListener('change', (e) => {
                this.visionConfig.requireDiffConfirm = e.target.checked;
                this.saveVisionConfig();
            });
        }
    }

    async handleAutoParseInput() {
        const travelerInfo = document.getElementById('travelerInfo');
        if (!travelerInfo) return;

        const currentText = travelerInfo.value.trim();
        const currentHash = this.hashString(currentText);

        // 避免重复解析相同内容
        if (currentHash === this.lastParsedTextHash || currentText.length < 10) {
            return;
        }

        // 清除之前的定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
        }

        // 设置新的定时器 - 防抖处理
        this.autoParseTimer = setTimeout(async () => {
            try {
                this.lastParsedTextHash = currentHash;
                console.log('🚀 自动解析触发...');

                await this.parseTextContent(currentText);

            } catch (error) {
                console.error('❌ 自动解析失败:', error);
            }
        }, this.autoParseDelay);
    }

    async parseTextContent(text) {
        if (!text || text.trim().length < 10) {
            this.showStatus('输入内容太短，无法解析', 'warning');
            return;
        }

        this.showStatus('正在解析文本内容...', 'loading');

        try {
            // 调用豆包API进行文本解析
            console.log('🌐 开始调用豆包API...');
            const result = await this.callDoubaoAPI(text);
            console.log('📥 豆包API原始响应:', result);

            if (result && result.data) {
                console.log('✅ 豆包API返回有效数据:', result.data);
                console.log('📊 提取到的字段:', Object.keys(result.data));

                // 详细分析字段映射问题
                console.log('🔍 字段映射问题分析:');
                Object.keys(result.data).forEach(key => {
                    console.log(`  • ${key}: "${result.data[key]}"`);
                });

                // 合并解析结果到当前数据
                const oldData = { ...this.currentData };
                this.currentData = { ...this.currentData, ...result.data };
                console.log('🔄 数据合并:', {
                    旧数据字段数: Object.keys(oldData).length,
                    新数据字段数: Object.keys(result.data).length,
                    合并后字段数: Object.keys(this.currentData).length
                });

                // 更新界面显示
                this.updateFormFields();
                this.saveData(); // 自动保存

                this.showStatus(`✅ 文本解析完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');

                return result.data;
            } else {
                console.error('❌ 豆包API响应无效:', { result, hasData: !!(result?.data) });
                this.showStatus('❌ 解析失败：无法提取有效数据', 'error');
                return null;
            }

        } catch (error) {
            console.error('❌ 文本解析错误:', error);
            this.showStatus(`❌ 解析错误: ${error.message}`, 'error');
            throw error;
        }
    }

    async extractDataFromImages(files) {
        if (!files || files.length === 0) {
            throw new Error('没有选择图片文件');
        }

        this.showStatus(`正在处理 ${files.length} 张图片...`, 'loading');

        try {
            // 使用统一多模态处理器处理图片
            const processor = window.UnifiedMultiModalProcessor?.getInstance();
            if (!processor) {
                throw new Error('多模态处理器未加载，请检查依赖文件');
            }

            const result = await processor.processImages(files, this.visionConfig);

            if (result && result.data) {
                // 合并图片解析结果
                this.currentData = { ...this.currentData, ...result.data };

                // 更新界面
                this.updateFormFields();
                this.saveData(); // 自动保存

                this.showStatus(`✅ 图片处理完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');

                return result.data;
            } else {
                throw new Error('图片处理返回无效数据');
            }

        } catch (error) {
            console.error('❌ 图片处理错误:', error);
            this.showStatus(`❌ 图片处理错误: ${error.message}`, 'error');
            throw error;
        }
    }

    async generateAndFillForm() {
        try {
            console.log('🚀 开始收集表单数据并生成脚本...');

            // 首先从表单收集最新的用户输入数据
            const formData = this.collectFormData();
            console.log('📋 收集到的表单数据:', formData);

            // 将表单数据合并到 currentData 中（表单数据优先级更高）
            this.currentData = { ...this.currentData, ...formData };
            console.log('🔄 合并后的数据:', this.currentData);

            // 检查是否有数据可以填充
            if (!this.currentData || Object.keys(this.currentData).length === 0) {
                const formFields = document.querySelectorAll('input[id], select[id]');
                const hasUserInput = Array.from(formFields).some(field => field.value.trim());

                if (hasUserInput) {
                    this.showStatus('⚠️ 表单有数据但未同步，请稍后重试', 'warning');
                } else {
                    this.showStatus('❌ 没有可用数据，请先输入表单数据或进行AI解析', 'error');
                }
                return;
            }

            this.showStatus('正在生成脚本...', 'loading');

            // 生成脚本
            this.generateScript();
            console.log('✅ 脚本生成完成');

            // 显示生成的字段数量信息
            const fieldCount = Object.keys(this.currentData).length;
            const formFieldCount = Object.keys(formData).length;
            this.showStatus(`✅ 脚本已生成 (${formFieldCount} 个表单字段，共 ${fieldCount} 个字段)`, 'success');

            // 检查连接并自动填充表单
            await this.checkConnection();

            if (this.isConnected) {
                console.log('🚀 开始填充MDAC表单...');
                this.showStatus('正在填充表单...', 'loading');
                await this.fillForm(this.currentData);
            } else {
                this.showStatus('✅ 脚本已生成，请先打开MDAC网站', 'warning');
            }

        } catch (error) {
            console.error('❌ 生成和填充失败:', error);
            this.showStatus(`❌ 操作失败: ${error.message}`, 'error');
        }
    }

    async fillForm(data) {
        if (!data || Object.keys(data).length === 0) {
            this.showStatus('❌ 没有数据可填充', 'error');
            return;
        }

        try {
            this.showStatus('正在填充表单...', 'loading');
            console.log('🔄 开始填充表单，数据:', data);

            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab) {
                throw new Error('无法获取当前标签页');
            }

            console.log('📋 当前标签页URL:', tab.url);

            // 发送消息到content script进行填充
            console.log('📤 发送填充消息到content script...');
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'fillForm',
                data: data,
                lockedFields: Array.from(this.lockedFields),
                config: this.visionConfig
            });

            console.log('📥 Content script响应:', response);

            if (response && response.success) {
                const result = response.result || {};
                const successCount = result.success ? result.success.length : 0;
                const failedCount = result.failed ? result.failed.length : 0;

                console.log(`✅ 填充成功: ${successCount} 个字段`);
                console.log(`❌ 填充失败: ${failedCount} 个字段`);

                if (result.success && result.success.length > 0) {
                    console.log('成功填充的字段:', result.success);
                }

                if (result.failed && result.failed.length > 0) {
                    console.log('失败的字段:', result.failed);
                }

                this.showStatus(`✅ 表单填充完成，成功 ${successCount} 个，失败 ${failedCount} 个`, 'success');

                // 记录填充历史
                this.recordFillHistory(data, successCount);

            } else {
                const errorMsg = response?.error || '填充失败，请检查网页是否正确加载';
                console.error('❌ 填充失败响应:', response);
                throw new Error(errorMsg);
            }

        } catch (error) {
            console.error('❌ 表单填充错误:', error);
            this.showStatus(`❌ 填充错误: ${error.message}`, 'error');
        }
    }

    async callDoubaoAPI(text, images = []) {
        // 豆包API配置
        const apiKey = '47a1d437-af1e-4833-abc9-82a97235e236';
        const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        const model = 'doubao-seed-1-6-flash-250828';

        // 构建消息内容 - 使用MDAC原生字段标准
        const messages = [{
            role: 'user',
            content: `请从以下信息中提取MDAC表单字段数据，严格使用MDAC网站原生字段ID，返回JSON格式：

【MDAC网站原生字段标准】：
- name: 姓名（英文全大写）
- passNo: 护照号码
- dob: 出生日期 (DD/MM/YYYY格式)
- passExpDte: 护照有效期 (DD/MM/YYYY格式)
- nationality: 国籍代码（3位字母，如CHN、USA、MYS等）
- sex: 性别 (MALE或FEMALE)
- email: 邮箱地址
- confirmEmail: 确认邮箱（通常与email相同）
- region: 电话区号（如86、60、1等）
- mobile: 手机号码
- arrDt: 到达日期 (DD/MM/YYYY格式)
- depDt: 出发日期 (DD/MM/YYYY格式)
- vesselNm: 航班/船舶/交通工具号码
- trvlMode: 旅行方式 (AIR、LAND或SEA)
- embark: 最后登船港（国家代码）
- accommodationStay: 住宿类型 (HOTEL、RESIDENCE或OTHERS)
- accommodationAddress1: 住宿地址第一行
- accommodationAddress2: 住宿地址第二行
- accommodationState: 马来西亚州属代码（01-16）
- accommodationCity: 住宿城市
- accommodationPostcode: 邮政编码（5位数字）

【重要要求】：
1. 必须使用上述MDAC原生字段ID，不得使用其他字段名
2. 只返回有效的JSON数据，不要包含任何解释或额外文字
3. 日期格式严格为DD/MM/YYYY
4. 性别值必须为MALE或FEMALE
5. 如果信息不确定，返回null

信息内容：
${text}`
        }];

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.1,
                    max_tokens: 1500,
                    top_p: 0.9
                })
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            const content = result.choices?.[0]?.message?.content;

            if (content) {
                // 提取JSON数据
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    try {
                        const data = JSON.parse(jsonMatch[0]);

                        // 使用统一字段转换器进行数据转换和验证
                        let transformedData;
                        if (this.transformer) {
                            const transformResult = this.transformer.transformAIToMDAC(data, {
                                validateRequired: true,
                                formatDates: true
                            });

                            if (transformResult.success) {
                                transformedData = transformResult.data;
                                console.log('✅ 统一字段转换成功:', transformResult.stats);
                            } else {
                                console.error('❌ 统一字段转换失败:', transformResult.error);
                                // 降级到基础验证
                                transformedData = this.validateAndCleanData(data);
                            }
                        } else {
                            // 降级到基础验证
                            transformedData = this.validateAndCleanData(data);
                        }

                        return { data: transformedData };
                    } catch (parseError) {
                        console.error('JSON解析错误:', parseError);
                        throw new Error('API返回的数据格式无效');
                    }
                }
            }

            throw new Error('API未返回有效的JSON数据');

        } catch (error) {
            console.error('❌ API调用错误:', error);
            throw error;
        }
    }

    validateAndCleanData(data) {
        const cleaned = {};

        // 使用字段常量定义验证规则
        if (!this.constants) {
            console.error('❌ 字段常量未初始化');
            return data;
        }

        const validators = {
            [this.constants.NATIVE_FIELDS.NAME]: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            [this.constants.NATIVE_FIELDS.PASSPORT_NO]: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            [this.constants.NATIVE_FIELDS.DATE_OF_BIRTH]: (value) => this.validateDate(value),
            [this.constants.NATIVE_FIELDS.PASSPORT_EXPIRY]: (value) => this.validateDate(value),
            [this.constants.NATIVE_FIELDS.EMAIL]: (value) => this.validateEmail(value),
            [this.constants.NATIVE_FIELDS.MOBILE]: (value) => typeof value === 'string' && value.length > 0 ? value.replace(/\D/g, '') : null,
            [this.constants.NATIVE_FIELDS.NATIONALITY]: (value) => typeof value === 'string' && value.length === 3 ? value.toUpperCase() : null,
            [this.constants.NATIVE_FIELDS.SEX]: (value) => ['MALE', 'FEMALE'].includes(value?.toUpperCase()) ? value.toUpperCase() : null,
            [this.constants.NATIVE_FIELDS.ARRIVAL_DATE]: (value) => this.validateDate(value),
            [this.constants.NATIVE_FIELDS.DEPARTURE_DATE]: (value) => this.validateDate(value),
            [this.constants.NATIVE_FIELDS.VESSEL_NAME]: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_STAY]: (value) => ['HOTEL', 'RESIDENCE', 'OTHERS'].includes(value?.toUpperCase()) ? value.toUpperCase() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_ADDRESS1]: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_ADDRESS2]: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_STATE]: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_CITY]: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            [this.constants.NATIVE_FIELDS.ACCOMMODATION_POSTCODE]: (value) => this.validatePostcode(value)
        };

        // 应用验证规则
        Object.keys(validators).forEach(field => {
            if (data[field] !== undefined && data[field] !== null) {
                const cleanedValue = validators[field](data[field]);
                if (cleanedValue !== null) {
                    cleaned[field] = cleanedValue;
                }
            }
        });

        return cleaned;
    }

    validateDate(dateStr) {
        if (typeof dateStr !== 'string') return null;

        // DD/MM/YYYY格式验证
        const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
        const match = dateStr.match(dateRegex);

        if (match) {
            const [, day, month, year] = match;
            const date = new Date(year, month - 1, day);

            // 验证日期有效性
            if (date.getDate() == day && date.getMonth() == month - 1 && date.getFullYear() == year) {
                return dateStr;
            }
        }

        return null;
    }

    validateEmail(email) {
        if (typeof email !== 'string') return null;

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? email.toLowerCase().trim() : null;
    }

    validatePostcode(postcode) {
        if (typeof postcode !== 'string' && typeof postcode !== 'number') return null;

        const postcodeStr = postcode.toString().trim();
        return /^\d{5}$/.test(postcodeStr) ? postcodeStr : null;
    }

    updateFormFields() {
        // 更新侧边栏界面上的表单字段显示 - 使用统一字段映射
        console.log('🔄 开始更新表单字段...');
        console.log('📊 当前数据:', this.currentData);
        console.log('📊 数据字段数量:', Object.keys(this.currentData || {}).length);

        let updatedCount = 0;
        let skippedCount = 0;

        // 使用统一字段映射配置
        try {
            if (typeof MDACFieldMappingConfig !== 'undefined') {
                const config = new MDACFieldMappingConfig();

                // 获取MDAC原生字段到AI字段的映射
                const mdacToAIMapping = config.MDAC_TO_AI_MAPPING;

                // 遍历所有可能的表单元素ID - 使用字段常量
                const possibleElementIds = this.constants ? [
                    // 使用兼容性映射中的字段名
                    ...Object.keys(this.constants.COMPATIBILITY_MAPPING),
                    // 同时包含标准字段名
                    ...this.constants.getAllStandardFields()
                ] : [
                    'passengerName', 'passportNo', 'birthDate', 'passportExpiry',
                    'email', 'confirmEmail', 'phoneRegion', 'phoneNumber',
                    'arrivalDate', 'departureDate', 'flightNo', 'embark', 'travelMode',
                    'accommodationType', 'address1', 'address2', 'city', 'state', 'postcode',
                    'nationality', 'gender'
                ];

                possibleElementIds.forEach(elementId => {
                    const element = document.getElementById(elementId);
                    if (!element) return;

                    // 查找对应的AI字段数据
                    let dataKey = null;
                    let value = null;

                    // 首先直接匹配元素ID
                    if (this.currentData[elementId]) {
                        dataKey = elementId;
                        value = this.currentData[elementId];
                    } else {
                        // 尝试通过映射找到对应的MDAC字段数据
                        const mdacField = mdacToAIMapping[elementId];
                        if (mdacField && this.currentData[mdacField]) {
                            dataKey = mdacField;
                            value = this.currentData[mdacField];
                        }
                    }

                    console.log(`🔍 检查字段 ${elementId} -> ${dataKey}:`, {
                        element: !!element,
                        hasData: !!value,
                        dataValue: value
                    });

                    if (value) {
                        // 应用值转换
                        value = config.transformFieldValue(elementId, value);

                        // 特殊处理日期格式 - 转换为HTML date输入框需要的yyyy-MM-dd格式
                        if (element.type === 'date' && value) {
                            if (/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                                const [day, month, year] = value.split('/');
                                value = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                                console.log(`📅 日期格式转换: ${this.currentData[dataKey]} -> ${value}`);
                            }
                        }

                        element.value = value;
                        updatedCount++;
                        console.log(`✅ 已更新字段 ${elementId}: ${value}`);

                        // 如果字段被锁定，添加视觉指示
                        if (this.lockedFields.has(elementId)) {
                            element.classList.add('locked');
                        }
                    } else {
                        skippedCount++;
                        console.log(`⚪ 无数据: ${elementId}`);
                    }
                });

                console.log(`📈 表单更新完成: 更新了 ${updatedCount} 个字段，跳过 ${skippedCount} 个字段`);
            } else {
                // 备用映射处理
                this.updateFormFieldsFallback();
            }
        } catch (error) {
            console.error('使用统一字段映射失败，使用备用方案:', error);
            this.updateFormFieldsFallback();
        }

        // 更新数据预览
        this.updateDataPreview();
    }

    // 备用字段更新方法（向后兼容）
    updateFormFieldsFallback() {
        console.log('🔄 使用备用字段映射...');

        const fieldMapping = {
            // 基本个人信息 - 使用MDAC原生字段ID
            'name': 'name',
            'passNo': 'passNo',
            'dob': 'dob',
            'passExpDte': 'passExpDte',
            'nationality': 'nationality',
            'sex': 'sex',

            // 联系信息 - 使用MDAC原生字段ID
            'email': 'email',
            'confirmEmail': 'confirmEmail',
            'region': 'region',
            'mobile': 'mobile',

            // 旅行信息 - 使用MDAC原生字段ID
            'arrDt': 'arrDt',
            'depDt': 'depDt',
            'vesselNm': 'vesselNm',
            'embark': 'embark',
            'trvlMode': 'trvlMode',
            'accommodationStay': 'accommodationStay',
            'accommodationAddress1': 'accommodationAddress1',
            'accommodationAddress2': 'accommodationAddress2',
            'accommodationState': 'accommodationState',
            'accommodationCity': 'accommodationCity',
            'accommodationPostcode': 'accommodationPostcode'
        };

        let updatedCount = 0;
        let skippedCount = 0;

        Object.keys(fieldMapping).forEach(elementId => {
            const dataKey = fieldMapping[elementId];
            const element = document.getElementById(elementId);

            if (element && this.currentData[dataKey]) {
                let value = this.currentData[dataKey];

                // 特殊值转换处理
                if (elementId === 'gender' && value) {
                    if (value.toLowerCase() === 'male' || value.toLowerCase() === '男性') {
                        value = '1';
                    } else if (value.toLowerCase() === 'female' || value.toLowerCase() === '女性') {
                        value = '2';
                    }
                }

                // 住宿类型转换
                if (elementId === 'accommodationType' && value) {
                    if (value.toLowerCase() === 'hotel' || value.toLowerCase() === '酒店') {
                        value = '01';
                    } else if (value.toLowerCase() === 'relative' || value.toLowerCase() === '亲友') {
                        value = '02';
                    } else {
                        value = '99';
                    }
                }

                // 特殊处理日期格式
                if (element.type === 'date' && value) {
                    if (/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                        const [day, month, year] = value.split('/');
                        value = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                    }
                }

                element.value = value;
                updatedCount++;
                console.log(`✅ 已更新字段 ${elementId}: ${value}`);

                if (this.lockedFields.has(elementId)) {
                    element.classList.add('locked');
                }
            } else {
                skippedCount++;
            }
        });

        console.log(`📈 备用映射完成: 更新了 ${updatedCount} 个字段，跳过 ${skippedCount} 个字段`);
    }

    updateDataPreview() {
        const preview = document.getElementById('dataPreview');
        if (preview) {
            const dataCount = Object.keys(this.currentData).length;
            const dataList = Object.keys(this.currentData).map(key =>
                `${key}: ${this.currentData[key]}`
            ).join('\n');

            preview.innerHTML = `
                <div class="data-summary">已提取 ${dataCount} 个字段</div>
                <div class="data-details">${dataList}</div>
            `;
        }

        // 更新图片预览
        this.updateImagePreview();
    }

    updateImagePreview() {
        const preview = document.getElementById('imagePreview');
        if (preview) {
            if (this.selectedFiles.length > 0) {
                preview.innerHTML = `
                    <div class="image-count">已选择 ${this.selectedFiles.length} 张图片</div>
                    <div class="image-list">
                        ${this.selectedFiles.map(file => `<div class="image-item">${file.name}</div>`).join('')}
                    </div>
                `;
            } else {
                preview.innerHTML = '<div class="no-images">未选择图片</div>';
            }
        }
    }

    async checkConnection() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            console.log('🔍 检查当前标签页:', tab?.url);

            if (tab && tab.url && (
                tab.url.includes('malaysia.travel') ||
                tab.url.includes('mdac') ||
                tab.url.includes('visa.malaysia.gov.my') ||
                tab.url.toLowerCase().includes('digital arrival')
            )) {
                this.isConnected = true;
                this.tabInfo = tab;
                this.showStatus('✅ 已连接到MDAC网站', 'success');
                console.log('✅ MDAC网站连接成功:', tab.url);

                // 尝试注入content script
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
                    console.log('✅ Content script 通信正常:', response);
                } catch (e) {
                    // 如果无法通信，重新注入content script
                    console.log('📝 注入content script...');
                    try {
                        await chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            files: ['content.js']
                        });
                        console.log('✅ Content script 注入成功');
                    } catch (injectError) {
                        console.error('❌ Content script 注入失败:', injectError);
                    }
                }

            } else {
                this.isConnected = false;
                this.showStatus('⚠️ 请先打开MDAC网站 (malaysia.travel)', 'warning');
                console.log('⚠️ 当前页面不是MDAC网站:', tab?.url);
            }

        } catch (error) {
            this.isConnected = false;
            this.showStatus('❌ 连接检查失败', 'error');
            console.error('连接检查错误:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['mdacData']);
            if (result.mdacData) {
                this.currentData = result.mdacData;
                this.updateFormFields();
                console.log('✅ 已加载保存的数据');
                this.showStatus('✅ 已加载保存的数据', 'success');
            }
        } catch (error) {
            console.error('❌ 加载数据失败:', error);
        }
    }

    async saveData() {
        try {
            await chrome.storage.local.set({
                mdacData: this.currentData,
                lastSaved: Date.now()
            });
            console.log('✅ 数据已保存');
            this.showStatus('✅ 数据已保存', 'success');
        } catch (error) {
            console.error('❌ 保存数据失败:', error);
            this.showStatus('❌ 保存数据失败', 'error');
        }
    }

    // 只加载锁定字段的数据（非锁定字段保持空白）
    async loadLockedFieldData() {
        try {
            const result = await chrome.storage.local.get(['mdacData']);
            if (result.mdacData && this.lockedFields.size > 0) {
                // 只提取锁定字段的数据
                const lockedData = {};
                this.lockedFields.forEach(fieldName => {
                    if (result.mdacData[fieldName] !== undefined) {
                        lockedData[fieldName] = result.mdacData[fieldName];
                    }
                });
                this.currentData = lockedData;
                this.updateFormFields();
                console.log('✅ 已加载锁定字段数据，非锁定字段保持空白');
            } else {
                // 没有锁定字段或保存的数据，清空表单
                this.currentData = {};
                this.updateFormFields();
                console.log('✅ 表单已重置为空白状态');
            }
        } catch (error) {
            console.error('❌ 加载锁定字段数据失败:', error);
        }
    }

    async loadFieldLocks() {
        try {
            const result = await chrome.storage.local.get([this.lockStorageKey]);
            if (result[this.lockStorageKey]) {
                this.lockedFields = new Set(result[this.lockStorageKey]);
                console.log('✅ 已加载字段锁定状态');
            }
        } catch (error) {
            console.error('❌ 加载字段锁定状态失败:', error);
        }
    }

    async saveFieldLocks() {
        try {
            await chrome.storage.local.set({
                [this.lockStorageKey]: Array.from(this.lockedFields)
            });
            console.log('✅ 字段锁定状态已保存');
        } catch (error) {
            console.error('❌ 保存字段锁定状态失败:', error);
        }
    }

    async loadVisionConfig() {
        try {
            const result = await chrome.storage.local.get([this.visionConfigKey]);
            if (result[this.visionConfigKey]) {
                this.visionConfig = { ...this.visionConfig, ...result[this.visionConfigKey] };
                this.applyVisionConfigToUI();
                console.log('✅ 已加载视觉模型配置');
            }
        } catch (error) {
            console.error('❌ 加载视觉配置失败:', error);
        }
    }

    async saveVisionConfig() {
        try {
            await chrome.storage.local.set({
                [this.visionConfigKey]: this.visionConfig
            });
            console.log('✅ 视觉配置已保存');
        } catch (error) {
            console.error('❌ 保存视觉配置失败:', error);
        }
    }

    async loadContactInfo() {
        try {
            const result = await chrome.storage.local.get([this.contactKey]);
            if (result[this.contactKey]) {
                const contactInfo = result[this.contactKey];
                // 自动填充常用联系信息
                this.currentData = { ...contactInfo, ...this.currentData };
                console.log('✅ 已加载联系信息快捷缓存');
            }
        } catch (error) {
            console.error('❌ 加载联系信息失败:', error);
        }
    }

    async saveContactInfo() {
        try {
            const contactFields = ['name', 'email', 'mobile', 'nationality'];
            const contactInfo = {};

            contactFields.forEach(field => {
                if (this.currentData[field]) {
                    contactInfo[field] = this.currentData[field];
                }
            });

            if (Object.keys(contactInfo).length > 0) {
                await chrome.storage.local.set({ [this.contactKey]: contactInfo });
                console.log('✅ 联系信息快捷缓存已更新');
            }
        } catch (error) {
            console.error('❌ 保存联系信息失败:', error);
        }
    }

    loadTheme() {
        try {
            chrome.storage.local.get([this.themeKey]).then(result => {
                const theme = result[this.themeKey] || 'light';
                if (theme === 'dark') {
                    document.body.classList.add('theme-dark');
                } else {
                    document.body.classList.remove('theme-dark');
                }
                const themeBtn = document.getElementById('themeToggleBtn');
                if (themeBtn) {
                    themeBtn.textContent = theme === 'dark' ? 'Dark' : 'Light';
                }
            });
        } catch (error) {
            console.error('loadTheme error:', error);
        }
    }

    toggleTheme() {
        try {
            const isDark = document.body.classList.contains('theme-dark');
            const newTheme = isDark ? 'light' : 'dark';
            if (newTheme === 'dark') {
                document.body.classList.add('theme-dark');
            } else {
                document.body.classList.remove('theme-dark');
            }
            chrome.storage.local.set({ [this.themeKey]: newTheme });
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) {
                themeBtn.textContent = newTheme === 'dark' ? 'Dark' : 'Light';
            }
        } catch (error) {
            console.error('toggleTheme error:', error);
        }
    }

    applyLockedStateToUI() {
        this.lockedFields.forEach(fieldId => {
            this.setFieldLock(fieldId, true);
        });
    }

    applyVisionConfigToUI() {
        // 将视觉配置应用到UI控件
        const confidenceSlider = document.getElementById('confidenceThreshold');
        const mrzCheckbox = document.getElementById('enableMrzOcr');
        const addressCheckbox = document.getElementById('enableAddressEnhance');
        const diffCheckbox = document.getElementById('requireDiffConfirm');

        if (confidenceSlider) confidenceSlider.value = this.visionConfig.confidenceThreshold;
        if (mrzCheckbox) mrzCheckbox.checked = this.visionConfig.enableMrzOcr;
        if (addressCheckbox) addressCheckbox.checked = this.visionConfig.enableAddressEnhance;
        if (diffCheckbox) diffCheckbox.checked = this.visionConfig.requireDiffConfirm;
    }

    toggleFieldLock(fieldId, lockBtnEl = null) {
        const willLock = !this.lockedFields.has(fieldId);
        this.setFieldLock(fieldId, willLock, lockBtnEl);
        this.saveFieldLocks();
        this.showStatus(`${willLock ? 'Locked' : 'Unlocked'} ${fieldId}`, 'info');
    }


    // ????????????? UI
    setFieldLock(fieldId, lock, lockBtnEl = null) {
        const field = document.getElementById(fieldId);
        if (!lockBtnEl) {
            lockBtnEl = document.querySelector(`.lock-btn[data-field="${fieldId}"]`);
        }
        if (lock) {
            this.lockedFields.add(fieldId);
            if (field) {
                field.setAttribute("disabled", "true");
                field.classList.add("locked");
            }
            if (lockBtnEl) {
                lockBtnEl.classList.add("locked");
                lockBtnEl.classList.remove("unlocked");
            }
        } else {
            this.lockedFields.delete(fieldId);
            if (field) {
                field.removeAttribute("disabled");
                field.classList.remove("locked");
            }
            if (lockBtnEl) {
                lockBtnEl.classList.remove("locked");
                lockBtnEl.classList.add("unlocked");
            }
        }
    }

    updateGroupLockButton(btn, locked) {
        if (!btn) return;
        if (locked) {
            btn.classList.add("locked");
            btn.classList.remove("unlocked");
        } else {
            btn.classList.remove("locked");
            btn.classList.add("unlocked");
        }
    }

    startCacheStatsLogging() {
        // 启动缓存统计定时器 - 每分钟记录一次
        this.cacheStatsTimer = setInterval(() => {
            this.logCacheStats();
        }, 60000); // 60秒

        console.log('✅ 缓存统计定时器已启动');
    }

    logCacheStats() {
        try {
            chrome.storage.local.getBytesInUse().then(bytesInUse => {
                console.log(`📊 缓存使用情况: ${bytesInUse} 字节`);

                // 如果缓存使用过多，提醒用户清理
                if (bytesInUse > 1024 * 1024) { // 1MB
                    this.showStatus('⚠️ 缓存使用较多，建议清理', 'warning');
                }
            });
        } catch (error) {
            console.error('❌ 缓存统计失败:', error);
        }
    }

    recordFillHistory(data, filledCount) {
        // 记录填充历史
        const historyEntry = {
            timestamp: Date.now(),
            dataCount: Object.keys(data).length,
            filledCount: filledCount,
            success: filledCount > 0
        };

        chrome.storage.local.get(['fillHistory']).then(result => {
            const history = result.fillHistory || [];
            history.push(historyEntry);

            // 只保留最近100次记录
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            chrome.storage.local.set({ fillHistory: history });
        });
    }

    showStatus(message, type = 'info') {
        const msgEl = document.getElementById('statusMessage');
        const loadingEl = document.getElementById('loadingIndicator');
        if (loadingEl) {
            if (type === 'loading') {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        if (msgEl) {
            msgEl.textContent = message || '';
            msgEl.className = `status ${type}`;

            // 自动清除状态消息
            if (type === 'success' || type === 'error' || type === 'warning') {
                setTimeout(() => {
                    if (msgEl.textContent === message) {
                        msgEl.textContent = '';
                        msgEl.className = 'status-message';
                    }
                }, 3000);
            }
        }

        // 控制台日志
        const emoji = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            loading: '⏳'
        };

        console.log(`${emoji[type] || 'ℹ️'} ${message}`);
    }

    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }

    // 图片处理相关方法
    handleImageSelect(e) {
        this.selectedFiles = Array.from(e.target.files);
        this.updateImagePreview();
        console.log(`已选择 ${this.selectedFiles.length} 张图片`);

        // 如果启用了自动处理，立即处理图片
        if (this.selectedFiles.length > 0) {
            this.showStatus(`已选择 ${this.selectedFiles.length} 张图片`, 'info');
        }
    }

    
    handleImageDrop(e) {
        const files = Array.from(e.dataTransfer.files);

        // 过滤图片文件
        this.selectedFiles = files.filter(file => file.type.startsWith('image/'));
        this.updateImagePreview();

        console.log(`拖拽上传 ${this.selectedFiles.length} 张图片`);
        this.showStatus(`已上传 ${this.selectedFiles.length} 张图片`, 'info');
    }

    // 清理和重置方法
    async clearAll() {
        console.log('🧹 clearAll 方法被调用');
        console.log('🔒 当前锁定字段:', Array.from(this.lockedFields));

        // 🔒 保留锁定字段的数据
        const lockedData = {};
        this.lockedFields.forEach(fieldName => {
            if (this.currentData[fieldName] !== undefined) {
                lockedData[fieldName] = this.currentData[fieldName];
            }
        });

        // 清除非锁定数据和状态
        this.currentData = lockedData; // 保留锁定数据
        this.selectedFiles = [];
        this.lastParsedTextHash = '';

        // 清除定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
            this.autoParseTimer = null;
        }

        // 清除UI（保留锁定字段的表单值）
        const travelerInfo = document.getElementById('travelerInfo');
        if (travelerInfo) travelerInfo.value = '';

        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) imageUpload.value = '';

        // 清除所有侧边栏表单字段（保留锁定字段）
        this.clearSidebarFormFields();

        // 清除MDAC网站表单字段（保留锁定字段）
        this.clearMDACFormFields();

        // 更新显示（锁定字段会保持原值）
        this.updateFormFields();
        this.updateDataPreview();
        this.updateImagePreview();

        const lockedCount = Object.keys(lockedData).length;
        if (lockedCount > 0) {
            this.showStatus(`已清除数据（保留${lockedCount}个锁定字段）`, 'info');
            console.log(`🧹 数据已清除，保留了${lockedCount}个锁定字段:`, Object.keys(lockedData));
        } else {
            this.showStatus('已清除所有数据', 'info');
            console.log('🧹 所有数据已清除');
        }
    }

    // 清除MDAC网站表单字段
    async clearMDACFormFields() {
        console.log('🧹 开始清除MDAC表单字段...');
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab) {
                console.warn('⚠️ 无法获取当前活动标签页');
                return;
            }
            console.log('📍 当前标签页:', tab.url);

            // 检查是否在MDAC页面
            if (!tab.url.includes('malaysia.travel') &&
                !tab.url.includes('mdac') &&
                !tab.url.includes('visa.malaysia.gov.my') &&
                !tab.url.toLowerCase().includes('digital arrival') &&
                !tab.url.includes('imigresen-online.imi.gov.my')) {
                console.log('📍 当前不在MDAC页面，跳过表单清除');
                return;
            }

            console.log('📤 发送清除消息到content script...');

            // 尝试发送消息到content script
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'clearForm',
                lockedFields: Array.from(this.lockedFields)
            });

            console.log('📥 清除表单响应:', response);

            if (response && response.success) {
                const result = response.result || {};
                console.log(`✅ MDAC表单清除完成: 清除${result.cleared?.length || 0}个，跳过${result.skipped?.length || 0}个，失败${result.failed?.length || 0}个`);
            } else {
                console.warn('⚠️ MDAC表单清除失败:', response?.error);
            }

        } catch (error) {
            console.error('❌ 清除MDAC表单时出错:', error);
            console.error('错误详情:', error.message);
        }
    }

    // 清除侧边栏表单字段（保留锁定字段）
    clearSidebarFormFields() {
        console.log('🧹 开始清除侧边栏表单字段...');

        // 定义所有需要清除的侧边栏字段ID
        const sidebarFields = [
            // 联系信息
            'email', 'confirmEmail', 'region', 'mobile',
            // 游客信息
            'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpDte',
            // 行程信息
            'arrDt', 'depDt', 'vesselNm', 'embark', 'trvlMode', 'accommodationStay',
            'accommodationAddress1', 'accommodationAddress2', 'accommodationCity',
            'accommodationState', 'accommodationPostcode'
        ];

        let clearedCount = 0;
        let lockedCount = 0;

        sidebarFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                // 检查字段是否被锁定
                if (this.lockedFields.has(fieldId)) {
                    console.log(`🔒 跳过锁定字段: ${fieldId}`);
                    lockedCount++;
                } else {
                    // 清除字段值
                    if (element.tagName === 'SELECT') {
                        element.selectedIndex = 0; // 下拉选择设为第一个选项
                    } else {
                        element.value = ''; // 输入框清空
                    }

                    // 触发change事件
                    const event = new Event('change', { bubbles: true });
                    element.dispatchEvent(event);

                    console.log(`✅ 已清除侧边栏字段: ${fieldId}`);
                    clearedCount++;
                }
            } else {
                console.warn(`⚠️ 未找到侧边栏字段: ${fieldId}`);
            }
        });

        console.log(`🧹 侧边栏表单清除完成: 清除${clearedCount}个，跳过${lockedCount}个锁定字段`);
    }

    fillSampleText() {
        // 填充示例文本到输入框，并触发AI解析
        // 重要：使用MDAC原生字段ID的描述，确保AI解析结果使用正确的字段名
        const sampleText = `姓名：ZHANG SAN，护照号码：E12345678，出生日期：01/01/1990，性别：MALE，护照有效期：01/01/2026
国籍：CHN，邮箱：<EMAIL>，确认邮箱：<EMAIL>
电话区号：86，手机号码：13800138000
到达日期：01/08/2025，出发日期：07/08/2025
航班号：MH123，旅行方式：AIR，最后登船港：CHN
住宿类型：HOTEL，住宿地址第一行：Hotel KL City Center，住宿地址第二行：Jalan Bukit Bintang
住宿州属：14，住宿城市：KUALA LUMPUR，住宿邮编：50000`;

        const travelerInfoEl = document.getElementById('travelerInfo');
        if (travelerInfoEl) {
            // 填入示例文本
            travelerInfoEl.value = sampleText;

            // 触发输入事件以启动自动解析
            travelerInfoEl.dispatchEvent(new Event('input', { bubbles: true }));

            this.showStatus('✅ 示例文本已填入，正在自动解析...', 'info');
            console.log('✅ 示例文本已填入输入框');

            // 立即触发自动解析
            setTimeout(() => {
                this.handleAutoParseInput();
            }, 100);

        } else {
            this.showStatus('❌ 找不到输入框', 'error');
            console.error('❌ 找不到 travelerInfo 输入框');
        }
    }

    parseContent() {
        // 手动触发内容解析
        const travelerInfo = document.getElementById('travelerInfo')?.value?.trim();

        if (travelerInfo && travelerInfo.length > 10) {
            this.parseTextContent(travelerInfo);
        } else if (this.selectedFiles.length > 0) {
            this.extractDataFromImages(this.selectedFiles);
        } else {
            this.showStatus('❌ 请先输入文本或选择图片', 'error');
        }
    }

    // 脚本操作方法
    copyScript() {
        const scriptArea = document.getElementById('previewCode');
        if (scriptArea && scriptArea.textContent) {
            navigator.clipboard.writeText(scriptArea.textContent).then(() => {
                this.showStatus('✅ 脚本已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showStatus('❌ 复制失败', 'error');
            });
        } else {
            this.showStatus('❌ 没有可复制的脚本', 'error');
        }
    }

    beautifyScript() {
        const scriptArea = document.getElementById('previewCode');
        if (scriptArea && scriptArea.textContent) {
            try {
                // 简单的代码格式化
                const script = scriptArea.textContent;
                const formatted = script
                    .replace(/;/g, ';\n')
                    .replace(/{/g, '{\n    ')
                    .replace(/}/g, '\n}')
                    .replace(/,/g, ',\n');

                scriptArea.textContent = formatted;
                this.showStatus('✅ 脚本已格式化', 'success');
            } catch (error) {
                console.error('格式化失败:', error);
                this.showStatus('❌ 格式化失败', 'error');
            }
        } else {
            this.showStatus('❌ 没有可格式化的脚本', 'error');
        }
    }

    minifyScript() {
        const scriptArea = document.getElementById('previewCode');
        if (scriptArea && scriptArea.textContent) {
            try {
                // 简单的代码压缩
                const script = scriptArea.textContent;
                const minified = script
                    .replace(/\s+/g, ' ')
                    .replace(/;\s+/g, ';')
                    .replace(/{\s+/g, '{')
                    .replace(/\s+}/g, '}')
                    .trim();

                scriptArea.textContent = minified;
                this.showStatus('✅ 脚本已压缩', 'success');
            } catch (error) {
                console.error('压缩失败:', error);
                this.showStatus('❌ 压缩失败', 'error');
            }
        } else {
            this.showStatus('❌ 没有可压缩的脚本', 'error');
        }
    }

    resetScript() {
        const scriptArea = document.getElementById('previewCode');
        if (scriptArea) {
            scriptArea.textContent = '';
            this.showStatus('✅ 脚本已重置', 'success');
        }

        // 如果有数据，重新生成脚本
        if (Object.keys(this.currentData).length > 0) {
            this.generateScript();
        }
    }

    generateScript() {
        // 生成表单填充脚本
        if (Object.keys(this.currentData).length === 0) {
            this.showStatus('❌ 没有数据可生成脚本', 'error');
            return;
        }

        try {
            const script = this.createFillScript(this.currentData);
            const scriptArea = document.getElementById('previewCode');
            const scriptContainer = document.getElementById('scriptPreview');
            if (scriptArea) {
                scriptArea.textContent = script;
                if (scriptContainer) {
                    scriptContainer.classList.remove('hidden'); // 显示脚本预览区域
                }
                this.showStatus('✅ 脚本生成完成', 'success');
                console.log('✅ 脚本已生成并显示');
            } else {
                console.error('❌ 找不到 previewCode 元素');
                this.showStatus('❌ 脚本预览区域不可用', 'error');
            }
        } catch (error) {
            console.error('脚本生成失败:', error);
            this.showStatus('❌ 脚本生成失败', 'error');
        }
    }

    createFillScript(data) {
        // 创建表单填充脚本 - 使用MDAC原生字段ID，零转换映射
        // 重要：数据必须使用MDAC网站原生字段ID，无需任何映射转换

        // 定义MDAC网站原生字段（无需映射）
        const mdacFields = [
            'name', 'passNo', 'dob', 'passExpDte', 'nationality', 'sex',
            'email', 'confirmEmail', 'region', 'mobile',
            'arrDt', 'depDt', 'vesselNm', 'trvlMode', 'embark',
            'accommodationStay', 'accommodationAddress1', 'accommodationAddress2',
            'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];

        // 定义特殊处理字段
        const selectFields = ['nationality', 'sex', 'region', 'trvlMode', 'embark',
                             'accommodationStay', 'accommodationState', 'accommodationCity'];
        const dateFields = ['dob', 'passExpDte', 'arrDt', 'depDt'];

        let script = '// MDAC表单自动填充脚本 - 零转换映射\n';
        script += '// 重要：所有字段使用MDAC网站原生ID，无需映射转换\n';
        script += '(function() {\n';
        script += '    console.log("🚀 开始填充MDAC表单（零转换映射）...");\n\n';
        script += '    // 等待函数\n';
        script += '    const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\n';

        // 零转换映射 - 直接使用MDAC原生字段ID
        mdacFields.forEach(fieldId => {
            if (data[fieldId]) {  // 直接使用MDAC字段ID作为key
                const isSelect = selectFields.includes(fieldId);
                const isDate = dateFields.includes(fieldId);

                script += `    // 填充 ${fieldId}\n`;
                script += `    const ${fieldId}Field = document.getElementById('${fieldId}');\n`;
                script += `    if (${fieldId}Field) {\n`;

                if (isSelect) {
                    script += `        // 下拉选择框\n`;
                    script += `        ${fieldId}Field.value = '${data[fieldId]}';\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('change', { bubbles: true }));\n`;
                } else if (isDate) {
                    script += `        // 日期字段 - 确保DD/MM/YYYY格式\n`;
                    script += `        let dateValue = '${data[fieldId]}';\n`;
                    script += `        // 如果已经是DD/MM/YYYY格式，直接使用\n`;
                    script += `        if (/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(dateValue)) {\n`;
                    script += `            ${fieldId}Field.value = dateValue;\n`;
                    script += `        }\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('input', { bubbles: true }));\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('change', { bubbles: true }));\n`;
                } else {
                    script += `        // 输入框\n`;
                    script += `        ${fieldId}Field.focus();\n`;
                    script += `        ${fieldId}Field.value = '${data[fieldId]}';\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('input', { bubbles: true }));\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('change', { bubbles: true }));\n`;
                    script += `        ${fieldId}Field.dispatchEvent(new Event('blur', { bubbles: true }));\n`;
                }

                script += `        console.log('✅ ${fieldId}:', '${data[fieldId]}');\n`;
                script += `    } else {\n`;
                script += `        console.log('❌ 字段不存在: ${fieldId}');\n`;
                script += `    }\n\n`;
            }
        });

        script += '    console.log("✅ MDAC表单填充完成（零转换映射）");\n';
        script += '})();';

        return script;
    }

    // 收集表单数据 - 从DOM中获取所有表单字段的最新值
    collectFormData() {
        console.log('🔄 开始收集表单数据...');
        const formData = {};

        // 定义所有需要收集的MDAC原生字段ID
        const formFields = [
            'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpDte',
            'email', 'confirmEmail', 'region', 'mobile', 'arrDt', 'depDt',
            'vesselNm', 'trvlMode', 'embark', 'accommodationStay',
            'accommodationAddress1', 'accommodationAddress2',
            'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];

        let collectedCount = 0;

        formFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                const value = element.value ? element.value.trim() : '';
                if (value) {
                    formData[fieldId] = value;
                    collectedCount++;
                    console.log(`📋 收集字段 ${fieldId}: "${value}"`);
                } else {
                    console.log(`⚪ 空字段 ${fieldId}`);
                }
            } else {
                console.warn(`❌ 找不到字段元素: ${fieldId}`);
            }
        });

        console.log(`📊 表单数据收集完成: ${collectedCount} 个字段有数据`);
        console.log('📋 收集到的数据:', formData);

        return formData;
    }

    // 处理表单字段变化 - 实时同步到 currentData
    handleFormFieldChange(fieldId, value) {
        if (!fieldId) return;

        const trimmedValue = value ? value.trim() : '';

        if (trimmedValue) {
            // 更新 currentData
            this.currentData[fieldId] = trimmedValue;
            console.log(`🔄 表单字段 ${fieldId} 已更新: "${trimmedValue}"`);
        } else {
            // 空值，从 currentData 中删除
            delete this.currentData[fieldId];
            console.log(`🗑️ 表单字段 ${fieldId} 已清空`);
        }

        // 更新数据预览（如果有）
        if (typeof this.updateDataPreview === 'function') {
            this.updateDataPreview();
        }
    }

    // 销毁方法 - 清理资源
    destroy() {
        // 清除定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
        }

        if (this.cacheStatsTimer) {
            clearInterval(this.cacheStatsTimer);
        }

        console.log('🗑️ MDAC扩展组件已销毁');
    }
}

// 初始化扩展
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 MDAC扩展侧边栏加载中...');

    try {
        window.mdacExtension = new MDACExtension();
        console.log('✅ MDAC扩展初始化完成');
    } catch (error) {
        console.error('❌ MDAC扩展初始化失败:', error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.mdacExtension) {
        window.mdacExtension.destroy();
    }
});

// Ensure initialization even if DOMContentLoaded already fired
// and expose constructor for integration patches
if (!window.MDACExtension) {
  window.MDACExtension = MDACExtension;
}

(function robustInit() {
  function start() {
    try {
      if (!window.mdacExtension) {
        window.mdacExtension = new MDACExtension();
      }
    } catch (e) {
      console.error('Failed to start MDAC sidepanel:', e);
    }
  }
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', start, { once: true });
  } else {
    start();
  }
})();


