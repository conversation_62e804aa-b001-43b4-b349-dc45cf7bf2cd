<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartPreprocessor 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-input { width: 100%; height: 100px; margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace; }
        .result { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .performance { font-size: 12px; color: #666; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 SmartPreprocessor 功能测试</h1>
        <p>测试智能预处理器的各项功能，包括MRZ识别、实体标准化、质量评估等。</p>

        <div class="section">
            <h3>1. 基础文本预处理测试</h3>
            <label>输入测试文本：</label>
            <textarea id="basicText" class="test-input">李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。
邮箱：<EMAIL>，手机：+60123456789
计划2025年8月1日到达马来西亚，8月7日离开
乘坐MH123航班，住宿地址：Hotel KL City Center，邮编50000</textarea>
            <button class="btn-primary" onclick="testBasicPreprocessing()">测试基础预处理</button>
            <div id="basicResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>2. MRZ识别测试</h3>
            <label>模拟护照图片数据：</label>
            <textarea id="mrzImageData" class="test-input">{
  "name": "passport.jpg",
  "base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
  "docType": "passport"
}</textarea>
            <button class="btn-primary" onclick="testMRZRecognition()">测试MRZ识别</button>
            <div id="mrzResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>3. 质量评估测试</h3>
            <label>输入不同质量的文本：</label>
            <textarea id="qualityText" class="test-input">张三，护照E87654321，1985年5月15日出生，女性，护照2025年5月15日到期。
邮箱：<EMAIL>，手机：+8613800138000</textarea>
            <button class="btn-primary" onclick="testQualityAssessment()">测试质量评估</button>
            <div id="qualityResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>4. 完整流程测试</h3>
            <label>综合测试（包含图片）：</label>
            <textarea id="fullFlowText" class="test-input">王五，中国护照P98765432，1992年12月30日出生，男性。
计划2025年10月1日到达，10月10日离开，乘坐CA888航班。
邮箱：<EMAIL>，电话：+8613900139000</textarea>
            <button class="btn-success" onclick="testFullFlow()">测试完整流程</button>
            <div id="fullFlowResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>5. 性能基准测试</h3>
            <button class="btn-secondary" onclick="runPerformanceBenchmark()">运行性能基准测试</button>
            <div id="benchmarkResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="../utils/SmartPreprocessor.js"></script>
    <script>
        let testPreprocessor = null;

        // 初始化测试
        function initializeTest() {
            try {
                testPreprocessor = new SmartPreprocessor({
                    enableOCR: true,
                    enableMRZ: true,
                    enableNormalization: true,
                    enableQualityCheck: true,
                    enableCache: true,
                    cacheTTL: 5 * 60 * 1000
                });
                console.log('✅ 测试预处理器初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 测试预处理器初始化失败:', error);
                return false;
            }
        }

        // 基础预处理测试
        async function testBasicPreprocessing() {
            const resultDiv = document.getElementById('basicResult');
            const text = document.getElementById('basicText').value;

            if (!testPreprocessor) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理器未初始化</span>`;
                resultDiv.style.display = 'block';
                return;
            }

            const startTime = performance.now();

            try {
                const result = await testPreprocessor.preprocessInput({
                    textInput: text,
                    images: [],
                    files: []
                });

                const endTime = performance.now();
                const processingTime = (endTime - startTime).toFixed(2);

                resultDiv.innerHTML = `<div class="success">✅ 基础预处理测试成功</div>
<div><strong>处理时间：</strong> ${processingTime}ms</div>
<div><strong>质量分数：</strong> ${(result.qualityScore * 100).toFixed(1)}%</div>
<div><strong>原始长度：</strong> ${text.length} 字符</div>
<div><strong>处理后长度：</strong> ${result.processedInput.length} 字符</div>
<div><strong>提取字段：</strong> ${Object.keys(result.extractedFields || {}).length} 个</div>
<div><strong>MRZ数据：</strong> ${result.mrzData ? '✅ 检测到' : '❌ 未检测到'}</div>
<div><strong>缓存命中：</strong> ${result.metadata?.cacheHit ? '✅' : '❌'}</div>
<div><strong>提取结果：</strong></div>
<pre>${JSON.stringify(result.extractedFields, null, 2)}</pre>`;

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理失败: ${error.message}</span>`;
            }

            resultDiv.style.display = 'block';
        }

        // MRZ识别测试
        async function testMRZRecognition() {
            const resultDiv = document.getElementById('mrzResult');

            if (!testPreprocessor) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理器未初始化</span>`;
                resultDiv.style.display = 'block';
                return;
            }

            const startTime = performance.now();

            try {
                // 创建模拟图片数据
                const mockImageData = {
                    name: "passport-mrz.jpg",
                    base64: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                    docType: "passport",
                    mimeType: "image/jpeg"
                };

                const result = await testPreprocessor.preprocessInput({
                    textInput: null,
                    images: [mockImageData],
                    files: []
                });

                const endTime = performance.now();
                const processingTime = (endTime - startTime).toFixed(2);

                resultDiv.innerHTML = `<div class="success">✅ MRZ识别测试完成</div>
<div><strong>处理时间：</strong> ${processingTime}ms</div>
<div><strong>图片数量：</strong> 1</div>
<div><strong>MRZ数据：</strong> ${result.mrzData ? '✅ 检测到' : '❌ 未检测到'}</div>
${result.mrzData ? `
<div><strong>MRZ解析结果：</strong></div>
<pre>${JSON.stringify(result.mrzData, null, 2)}</pre>
` : ''}
<div><strong>提取字段：</strong></div>
<pre>${JSON.stringify(result.extractedFields, null, 2)}</pre>`;

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ MRZ识别失败: ${error.message}</span>`;
            }

            resultDiv.style.display = 'block';
        }

        // 质量评估测试
        async function testQualityAssessment() {
            const resultDiv = document.getElementById('qualityResult');
            const text = document.getElementById('qualityText').value;

            if (!testPreprocessor) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理器未初始化</span>`;
                resultDiv.style.display = 'block';
                return;
            }

            try {
                const qualityScore = testPreprocessor.assessInputQuality({
                    textInput: text,
                    images: [],
                    files: []
                });

                resultDiv.innerHTML = `<div class="info">📊 质量评估结果</div>
<div><strong>质量分数：</strong> ${(qualityScore * 100).toFixed(1)}%</div>
<div><strong>评估等级：</strong> ${qualityScore >= 0.8 ? '🟢 优秀' : qualityScore >= 0.6 ? '🟡 良好' : '🔴 需改进'}</div>
<div><strong>文本长度：</strong> ${text.length} 字符</div>
<div><strong>建议：</strong> ${qualityScore >= 0.8 ? '输入质量良好，可直接处理' : qualityScore >= 0.6 ? '输入质量可接受，建议补充信息' : '输入质量较低，建议提供更多详细信息'}</div>`;

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 质量评估失败: ${error.message}</span>`;
            }

            resultDiv.style.display = 'block';
        }

        // 完整流程测试
        async function testFullFlow() {
            const resultDiv = document.getElementById('fullFlowResult');
            const text = document.getElementById('fullFlowText').value;

            if (!testPreprocessor) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理器未初始化</span>`;
                resultDiv.style.display = 'block';
                return;
            }

            const startTime = performance.now();

            try {
                const result = await testPreprocessor.preprocessInput({
                    textInput: text,
                    images: [],
                    files: []
                });

                const endTime = performance.now();
                const totalTime = (endTime - startTime).toFixed(2);

                resultDiv.innerHTML = `<div class="success">✅ 完整流程测试成功</div>
<div><strong>总处理时间：</strong> ${totalTime}ms</div>
<div><strong>质量分数：</strong> ${(result.qualityScore * 100).toFixed(1)}%</div>
<div><strong>提取字段数：</strong> ${Object.keys(result.extractedFields || {}).length}</div>
<div><strong>MRZ数据：</strong> ${result.mrzData ? '✅ 检测到' : '❌ 未检测到'}</div>
<div><strong>缓存命中：</strong> ${result.metadata?.cacheHit ? '✅' : '❌'}</div>
<div><strong>处理摘要：</strong> ${result.metadata?.processingTime || 'N/A'}ms</div>
<div><strong>最终提取结果：</strong></div>
<pre>${JSON.stringify(result.extractedFields, null, 2)}</pre>`;

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 完整流程失败: ${error.message}</span>`;
            }

            resultDiv.style.display = 'block';
        }

        // 性能基准测试
        async function runPerformanceBenchmark() {
            const resultDiv = document.getElementById('benchmarkResult');

            if (!testPreprocessor) {
                resultDiv.innerHTML = `<span class="error">❌ 预处理器未初始化</span>`;
                resultDiv.style.display = 'block';
                return;
            }

            const testCases = [
                { name: "短文本", text: "张三，护照E12345678，1990年出生。" },
                { name: "中等文本", text: "李四，中国护照P87654321，1985年3月15日出生，男性，护照2025年3月15日到期。邮箱：<EMAIL>，手机：+8613800138000" },
                { name: "长文本", text: "王五，中国护照P98765432，1992年12月30日出生，男性。计划2025年10月1日到达马来西亚，10月10日离开，乘坐CA888航班。邮箱：<EMAIL>，电话：+8613900139000，住宿地址：吉隆坡市中心酒店，邮编50000。" }
            ];

            resultDiv.innerHTML = `<div class="info">⏳ 正在运行性能基准测试... </div>`;
            resultDiv.style.display = 'block';

            const benchmarkResults = [];

            for (const testCase of testCases) {
                try {
                    const startTime = performance.now();

                    const result = await testPreprocessor.preprocessInput({
                        textInput: testCase.text,
                        images: [],
                        files: []
                    });

                    const endTime = performance.now();
                    const processingTime = (endTime - startTime).toFixed(2);

                    benchmarkResults.push({
                        name: testCase.name,
                        processingTime: processingTime,
                        qualityScore: result.qualityScore,
                        fieldCount: Object.keys(result.extractedFields || {}).length,
                        cacheHit: result.metadata?.cacheHit || false
                    });

                } catch (error) {
                    benchmarkResults.push({
                        name: testCase.name,
                        error: error.message
                    });
                }
            }

            // 显示结果
            let html = `<div class="success">✅ 性能基准测试完成</div>
<div><strong>测试结果汇总：</strong></div>
<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
<thead>
<tr style="background: #f8f9fa;">
<th style="border: 1px solid #ddd; padding: 8px;">测试用例</th>
<th style="border: 1px solid #ddd; padding: 8px;">处理时间</th>
<th style="border: 1px solid #ddd; padding: 8px;">质量分数</th>
<th style="border: 1px solid #ddd; padding: 8px;">字段数</th>
<th style="border: 1px solid #ddd; padding: 8px;">缓存</th>
</tr>
</thead>
<tbody>`;

            let totalTime = 0;
            let successCount = 0;

            for (const result of benchmarkResults) {
                if (!result.error) {
                    totalTime += parseFloat(result.processingTime);
                    successCount++;

                    html += `<tr>
<td style="border: 1px solid #ddd; padding: 8px;">${result.name}</td>
<td style="border: 1px solid #ddd; padding: 8px;">${result.processingTime}ms</td>
<td style="border: 1px solid #ddd; padding: 8px;">${(result.qualityScore * 100).toFixed(1)}%</td>
<td style="border: 1px solid #ddd; padding: 8px;">${result.fieldCount}</td>
<td style="border: 1px solid #ddd; padding: 8px;">${result.cacheHit ? '✅' : '❌'}</td>
</tr>`;
                } else {
                    html += `<tr>
<td style="border: 1px solid #ddd; padding: 8px;">${result.name}</td>
<td colspan="4" style="border: 1px solid #ddd; padding: 8px; color: red;">❌ ${result.error}</td>
</tr>`;
                }
            }

            const avgTime = successCount > 0 ? (totalTime / successCount).toFixed(2) : 0;

            html += `</tbody>
</table>
<div style="margin-top: 15px;">
<div><strong>平均处理时间：</strong> ${avgTime}ms</div>
<div><strong>成功率：</strong> ${successCount}/${benchmarkResults.length} (${(successCount/benchmarkResults.length*100).toFixed(1)}%)</div>
<div><strong>性能评估：</strong> ${avgTime < 100 ? '🟢 优秀' : avgTime < 300 ? '🟡 良好' : '🔴 需优化'}</div>
</div>`;

            resultDiv.innerHTML = html;
        }

        // 页面加载时初始化
        window.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 SmartPreprocessor测试页面加载完成');
            const initialized = initializeTest();
            if (initialized) {
                console.log('✅ 测试环境准备就绪');
            } else {
                console.error('❌ 测试环境初始化失败');
            }
        });
    </script>
</body>
</html>