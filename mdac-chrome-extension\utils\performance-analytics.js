// 性能分析系统 - 成功率追踪和性能统计
// 用途：监控AI处理器的性能指标，追踪成功率，提供详细的分析报告
// 依赖：浏览器存储API，性能时间API
// 技术栈：统计分析，趋势分析，实时监控
// 核心功能：成功率统计，响应时间分析，错误分类，趋势预测

class PerformanceAnalytics {
  constructor(config = {}) {
    this.config = {
      // 存储配置
      storageKey: config.storageKey || 'mdacPerformanceData',
      maxRecords: config.maxRecords || 1000,
      retentionDays: config.retentionDays || 30,

      // 监控配置
      enableRealTimeMonitoring: config.enableRealTimeMonitoring !== false,
      enableTrendAnalysis: config.enableTrendAnalysis !== false,
      enableAlerting: config.enableAlerting !== false,

      // 阈值配置
      successRateThreshold: config.successRateThreshold || 0.8,
      responseTimeThreshold: config.responseTimeThreshold || 10000, // 10秒
      errorRateThreshold: config.errorRateThreshold || 0.2,

      // 采样配置
      samplingRate: config.samplingRate || 1.0, // 100%采样

      // 报告配置
      enableAutoReporting: config.enableAutoReporting !== false,
      reportInterval: config.reportInterval || 24 * 60 * 60 * 1000 // 24小时
    };

    // 初始化数据存储
    this.performanceData = this.loadPerformanceData();
    this.realTimeMetrics = this.initializeRealTimeMetrics();
    this.alertHistory = [];

    // 启动监控
    if (this.config.enableRealTimeMonitoring) {
      this.startRealTimeMonitoring();
    }

    // 启动自动报告
    if (this.config.enableAutoReporting) {
      this.startAutoReporting();
    }

    console.log('📊 性能分析系统初始化完成');
  }

  // 初始化实时指标
  initializeRealTimeMetrics() {
    return {
      sessionStart: Date.now(),
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      currentSuccessRate: 0,
      errorsByType: {},
      processorMetrics: {
        doubao: { requests: 0, success: 0, avgTime: 0 }
      }
    };
  }

  // 记录性能数据
  recordPerformance(sessionData) {
    if (Math.random() > this.config.samplingRate) return;

    const record = {
      timestamp: Date.now(),
      sessionId: sessionData.sessionId,
      processor: sessionData.processor || 'unknown',
      success: sessionData.success,
      responseTime: sessionData.responseTime,
      fieldCount: sessionData.fieldCount || 0,
      errorType: sessionData.errorType || null,
      errorMessage: sessionData.errorMessage || null,
      cacheHit: sessionData.cacheHit || false,
      qualityScore: sessionData.qualityScore || null,
      inputLength: sessionData.inputLength || 0,
      metadata: sessionData.metadata || {}
    };

    // 添加到性能数据
    this.performanceData.records.push(record);

    // 更新实时指标
    this.updateRealTimeMetrics(record);

    // 清理过期数据
    this.cleanupOldData();

    // 检查警报条件
    if (this.config.enableAlerting) {
      this.checkAlertConditions(record);
    }

    // 保存数据
    this.savePerformanceData();
  }

  // 更新实时指标
  updateRealTimeMetrics(record) {
    const metrics = this.realTimeMetrics;

    metrics.totalRequests++;
    metrics.totalResponseTime += record.responseTime;

    if (record.success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;

      // 错误分类统计
      const errorType = record.errorType || 'unknown';
      metrics.errorsByType[errorType] = (metrics.errorsByType[errorType] || 0) + 1;
    }

    // 更新响应时间统计
    metrics.minResponseTime = Math.min(metrics.minResponseTime, record.responseTime);
    metrics.maxResponseTime = Math.max(metrics.maxResponseTime, record.responseTime);
    metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalRequests;

    // 更新成功率
    metrics.currentSuccessRate = metrics.successfulRequests / metrics.totalRequests;

    // 更新处理器特定指标
    if (metrics.processorMetrics[record.processor]) {
      const procMetrics = metrics.processorMetrics[record.processor];
      procMetrics.requests++;
      if (record.success) procMetrics.success++;
      procMetrics.avgTime = (procMetrics.avgTime * (procMetrics.requests - 1) + record.responseTime) / procMetrics.requests;
    }
  }

  // 检查警报条件
  checkAlertConditions(record) {
    const alerts = [];

    // 成功率警报
    if (this.realTimeMetrics.currentSuccessRate < this.config.successRateThreshold &&
        this.realTimeMetrics.totalRequests > 10) {
      alerts.push({
        type: 'low_success_rate',
        severity: 'warning',
        message: `成功率过低: ${(this.realTimeMetrics.currentSuccessRate * 100).toFixed(1)}%`,
        timestamp: Date.now(),
        value: this.realTimeMetrics.currentSuccessRate
      });
    }

    // 响应时间警报
    if (record.responseTime > this.config.responseTimeThreshold) {
      alerts.push({
        type: 'high_response_time',
        severity: 'warning',
        message: `响应时间过高: ${record.responseTime}ms`,
        timestamp: Date.now(),
        value: record.responseTime
      });
    }

    // 错误率警报
    const recentErrorRate = this.calculateRecentErrorRate();
    if (recentErrorRate > this.config.errorRateThreshold) {
      alerts.push({
        type: 'high_error_rate',
        severity: 'error',
        message: `错误率过高: ${(recentErrorRate * 100).toFixed(1)}%`,
        timestamp: Date.now(),
        value: recentErrorRate
      });
    }

    // 添加警报到历史
    if (alerts.length > 0) {
      this.alertHistory.push(...alerts);
      this.showAlerts(alerts);
    }
  }

  // 计算近期错误率（最近10分钟）
  calculateRecentErrorRate() {
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
    const recentRecords = this.performanceData.records.filter(r => r.timestamp > tenMinutesAgo);

    if (recentRecords.length === 0) return 0;

    const failedRecords = recentRecords.filter(r => !r.success);
    return failedRecords.length / recentRecords.length;
  }

  // 显示警报
  showAlerts(alerts) {
    alerts.forEach(alert => {
      console.warn(`⚠️ ${alert.type}: ${alert.message}`);

      // 可以在这里添加UI通知逻辑
      if (window.mdacExtension) {
        window.mdacExtension.showStatus(`性能警报: ${alert.message}`, 'warning');
      }
    });
  }

  // 获取性能报告
  getPerformanceReport(timeRange = '24h') {
    const now = Date.now();
    let startTime;

    switch (timeRange) {
      case '1h':
        startTime = now - 60 * 60 * 1000;
        break;
      case '6h':
        startTime = now - 6 * 60 * 60 * 1000;
        break;
      case '24h':
        startTime = now - 24 * 60 * 60 * 1000;
        break;
      case '7d':
        startTime = now - 7 * 24 * 60 * 60 * 1000;
        break;
      default:
        startTime = now - 24 * 60 * 60 * 1000;
    }

    const relevantRecords = this.performanceData.records.filter(r => r.timestamp >= startTime);

    return this.generateDetailedReport(relevantRecords, timeRange);
  }

  // 生成详细报告
  generateDetailedReport(records, timeRange) {
    if (records.length === 0) {
      return {
        timeRange: timeRange,
        totalRequests: 0,
        message: 'No data available for the specified time range'
      };
    }

    const totalRequests = records.length;
    const successfulRequests = records.filter(r => r.success).length;
    const successRate = successfulRequests / totalRequests;

    // 响应时间统计
    const responseTimes = records.map(r => r.responseTime).filter(rt => rt > 0);
    const avgResponseTime = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const minResponseTime = Math.min(...responseTimes);
    const maxResponseTime = Math.max(...responseTimes);

    // 处理器性能对比
    const processorStats = this.analyzeProcessorPerformance(records);

    // 错误分析
    const errorAnalysis = this.analyzeErrors(records);

    // 趋势分析
    const trendAnalysis = this.config.enableTrendAnalysis ?
      this.analyzeTrends(records) : null;

    // 缓存性能分析
    const cacheStats = this.analyzeCachePerformance(records);

    return {
      timeRange: timeRange,
      period: this.formatTimeRange(records),
      totalRequests: totalRequests,
      successfulRequests: successfulRequests,
      failedRequests: totalRequests - successfulRequests,
      successRate: successRate,
      successRatePercentage: (successRate * 100).toFixed(2),

      responseTime: {
        average: avgResponseTime.toFixed(2),
        minimum: minResponseTime.toFixed(2),
        maximum: maxResponseTime.toFixed(2),
        median: this.calculateMedian(responseTimes).toFixed(2)
      },

      processorStats: processorStats,
      errorAnalysis: errorAnalysis,
      cacheStats: cacheStats,
      trendAnalysis: trendAnalysis,

      qualityMetrics: this.analyzeQualityMetrics(records),

      recommendations: this.generateRecommendations({
        successRate,
        avgResponseTime,
        errorAnalysis,
        cacheStats
      })
    };
  }

  // 分析处理器性能
  analyzeProcessorPerformance(records) {
    const processorStats = {};

    ['doubao'].forEach(processor => {
      const processorRecords = records.filter(r => r.processor === processor);
      if (processorRecords.length > 0) {
        const successful = processorRecords.filter(r => r.success);
        const avgTime = processorRecords.reduce((sum, r) => sum + r.responseTime, 0) / processorRecords.length;

        processorStats[processor] = {
          totalRequests: processorRecords.length,
          successfulRequests: successful.length,
          successRate: successful.length / processorRecords.length,
          averageResponseTime: avgTime.toFixed(2),
          fieldExtractionRate: this.calculateFieldExtractionRate(successful)
        };
      }
    });

    return processorStats;
  }

  // 计算字段提取率
  calculateFieldExtractionRate(successfulRecords) {
    if (successfulRecords.length === 0) return 0;

    const totalFields = successfulRecords.reduce((sum, r) => sum + (r.fieldCount || 0), 0);
    return (totalFields / successfulRecords.length).toFixed(2);
  }

  // 分析错误
  analyzeErrors(records) {
    const failedRecords = records.filter(r => !r.success);
    const errorTypes = {};

    failedRecords.forEach(record => {
      const errorType = record.errorType || 'unknown';
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });

    return {
      totalErrors: failedRecords.length,
      errorRate: failedRecords.length / records.length,
      errorTypes: errorTypes,
      topErrors: Object.entries(errorTypes)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([type, count]) => ({ type, count, percentage: ((count / failedRecords.length) * 100).toFixed(1) }))
    };
  }

  // 分析缓存性能
  analyzeCachePerformance(records) {
    const cacheHits = records.filter(r => r.cacheHit).length;
    const cacheHitRate = cacheHits / records.length;

    // 对比缓存命中vs未命中的响应时间
    const cacheHitRecords = records.filter(r => r.cacheHit);
    const cacheMissRecords = records.filter(r => !r.cacheHit);

    const cacheHitAvgTime = cacheHitRecords.length > 0 ?
      cacheHitRecords.reduce((sum, r) => sum + r.responseTime, 0) / cacheHitRecords.length : 0;

    const cacheMissAvgTime = cacheMissRecords.length > 0 ?
      cacheMissRecords.reduce((sum, r) => sum + r.responseTime, 0) / cacheMissRecords.length : 0;

    return {
      cacheHits: cacheHits,
      cacheHitRate: cacheHitRate,
      cacheHitRatePercentage: (cacheHitRate * 100).toFixed(2),
      averageResponseTime: {
        cacheHit: cacheHitAvgTime.toFixed(2),
        cacheMiss: cacheMissAvgTime.toFixed(2),
        improvement: cacheMissAvgTime > 0 ?
          (((cacheMissAvgTime - cacheHitAvgTime) / cacheMissAvgTime) * 100).toFixed(2) : 0
      }
    };
  }

  // 分析质量指标
  analyzeQualityMetrics(records) {
    const qualityScores = records.map(r => r.qualityScore).filter(qs => qs !== null);

    if (qualityScores.length === 0) return null;

    const avgQuality = qualityScores.reduce((sum, qs) => sum + qs, 0) / qualityScores.length;

    return {
      averageQualityScore: avgQuality.toFixed(2),
      qualityDistribution: this.calculateQualityDistribution(qualityScores),
      correlationWithSuccess: this.calculateQualitySuccessCorrelation(records)
    };
  }

  // 分析趋势
  analyzeTrends(records) {
    if (records.length < 10) return null;

    // 按时间分组（每小时）
    const hourlyGroups = this.groupByHour(records);

    const successRateTrend = this.calculateTrend(hourlyGroups.map(g => g.successRate));
    const responseTimeTrend = this.calculateTrend(hourlyGroups.map(g => g.avgResponseTime));

    return {
      hourlyMetrics: hourlyGroups,
      successRateTrend: successRateTrend,
      responseTimeTrend: responseTimeTrend,
      trendDirection: {
        successRate: successRateTrend > 0.1 ? 'improving' : successRateTrend < -0.1 ? 'declining' : 'stable',
        responseTime: responseTimeTrend > 100 ? 'worsening' : responseTimeTrend < -100 ? 'improving' : 'stable'
      }
    };
  }

  // 生成建议
  generateRecommendations(metrics) {
    const recommendations = [];

    if (metrics.successRate < 0.8) {
      recommendations.push({
        priority: 'high',
        category: 'reliability',
        message: '成功率较低，建议检查AI处理器配置和网络连接',
        action: 'review_processor_config'
      });
    }

    if (metrics.avgResponseTime > 10000) {
      recommendations.push({
        priority: 'high',
        category: 'performance',
        message: '平均响应时间过长，建议启用缓存或优化模型参数',
        action: 'optimize_performance'
      });
    }

    if (metrics.cacheStats && metrics.cacheStats.cacheHitRate < 0.3) {
      recommendations.push({
        priority: 'medium',
        category: 'caching',
        message: '缓存命中率较低，建议调整缓存策略或增加缓存容量',
        action: 'improve_caching'
      });
    }

    if (metrics.errorAnalysis && metrics.errorAnalysis.errorRate > 0.2) {
      recommendations.push({
        priority: 'high',
        category: 'error_handling',
        message: '错误率较高，建议增强错误处理和重试机制',
        action: 'improve_error_handling'
      });
    }

    return recommendations;
  }

  // 辅助方法
  calculateMedian(values) {
    if (values.length === 0) return 0;
    const sorted = values.slice().sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ?
      (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
  }

  formatTimeRange(records) {
    if (records.length === 0) return 'No data';
    const start = new Date(Math.min(...records.map(r => r.timestamp)));
    const end = new Date(Math.max(...records.map(r => r.timestamp)));
    return `${start.toLocaleString()} - ${end.toLocaleString()}`;
  }

  groupByHour(records) {
    const groups = {};

    records.forEach(record => {
      const hour = new Date(record.timestamp).setMinutes(0, 0, 0);
      if (!groups[hour]) {
        groups[hour] = {
          hour: hour,
          records: [],
          successRate: 0,
          avgResponseTime: 0
        };
      }
      groups[hour].records.push(record);
    });

    return Object.values(groups).map(group => {
      const successful = group.records.filter(r => r.success);
      const avgTime = group.records.reduce((sum, r) => sum + r.responseTime, 0) / group.records.length;

      return {
        hour: group.hour,
        recordCount: group.records.length,
        successRate: successful.length / group.records.length,
        avgResponseTime: avgTime
      };
    }).sort((a, b) => a.hour - b.hour);
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;

    // 简单的线性趋势计算
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, idx) => sum + idx * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }

  calculateQualityDistribution(qualityScores) {
    const ranges = {
      excellent: 0, // 0.8-1.0
      good: 0,      // 0.6-0.8
      fair: 0,      // 0.4-0.6
      poor: 0       // 0.0-0.4
    };

    qualityScores.forEach(score => {
      if (score >= 0.8) ranges.excellent++;
      else if (score >= 0.6) ranges.good++;
      else if (score >= 0.4) ranges.fair++;
      else ranges.poor++;
    });

    return ranges;
  }

  calculateQualitySuccessCorrelation(records) {
    const qualityRecords = records.filter(r => r.qualityScore !== null);
    if (qualityRecords.length === 0) return 0;

    const successfulQuality = qualityRecords.filter(r => r.success).reduce((sum, r) => sum + r.qualityScore, 0);
    const failedQuality = qualityRecords.filter(r => !r.success).reduce((sum, r) => sum + r.qualityScore, 0);

    const successfulAvg = successfulQuality / qualityRecords.filter(r => r.success).length;
    const failedAvg = failedQuality / qualityRecords.filter(r => !r.success).length;

    return (successfulAvg - failedAvg).toFixed(3);
  }

  // 存储相关方法
  loadPerformanceData() {
    try {
      const stored = localStorage.getItem(this.config.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        // 清理过期数据
        const cutoffTime = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
        data.records = data.records.filter(r => r.timestamp > cutoffTime);
        return data;
      }
    } catch (error) {
      console.warn('Failed to load performance data:', error);
    }

    return { records: [], createdAt: Date.now() };
  }

  savePerformanceData() {
    try {
      localStorage.setItem(this.config.storageKey, JSON.stringify(this.performanceData));
    } catch (error) {
      console.error('Failed to save performance data:', error);
    }
  }

  cleanupOldData() {
    const cutoffTime = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
    const initialLength = this.performanceData.records.length;

    this.performanceData.records = this.performanceData.records.filter(r => r.timestamp > cutoffTime);

    if (this.performanceData.records.length < initialLength) {
      console.log(`🧹 清理了${initialLength - this.performanceData.records.length}条过期性能数据`);
    }
  }

  // 实时监控
  startRealTimeMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.logRealTimeMetrics();
      this.cleanupOldData();
    }, 30000); // 每30秒记录一次

    console.log('📊 实时性能监控已启动');
  }

  logRealTimeMetrics() {
    const metrics = this.realTimeMetrics;

    if (metrics.totalRequests > 0) {
      console.log(`📊 实时性能指标:`);
      console.log(`  📈 总请求数: ${metrics.totalRequests}`);
      console.log(`  ✅ 成功率: ${(metrics.currentSuccessRate * 100).toFixed(1)}%`);
      console.log(`  ⏱️  平均响应时间: ${metrics.averageResponseTime.toFixed(0)}ms`);
      console.log(`  💾 缓存命中率: ${this.getCacheHitRate()}%`);

      // 处理器对比
      Object.entries(metrics.processorMetrics).forEach(([processor, procMetrics]) => {
        if (procMetrics.requests > 0) {
          console.log(`  🤖 ${processor}: ${(procMetrics.success / procMetrics.requests * 100).toFixed(1)}% 成功率, ${procMetrics.avgTime.toFixed(0)}ms 平均响应`);
        }
      });
    }
  }

  getCacheHitRate() {
    const cacheStats = this.getCacheStats();
    return cacheStats ? cacheStats.hitRate : 0;
  }

  // 自动报告
  startAutoReporting() {
    this.reportingInterval = setInterval(() => {
      this.generateAndLogReport();
    }, this.config.reportInterval);

    console.log('📋 自动性能报告已启动');
  }

  generateAndLogReport() {
    const report = this.getPerformanceReport('24h');

    console.log('📋 24小时性能报告:');
    console.log(`  📊 总请求数: ${report.totalRequests}`);
    console.log(`  ✅ 成功率: ${report.successRatePercentage}%`);
    console.log(`  ⏱️  平均响应时间: ${report.responseTime.average}ms`);
    console.log(`  💾 缓存命中率: ${report.cacheStats.cacheHitRatePercentage}%`);

    if (report.recommendations.length > 0) {
      console.log('  💡 建议:');
      report.recommendations.forEach(rec => {
        console.log(`     - ${rec.message}`);
      });
    }
  }

  // 获取实时指标
  getRealTimeMetrics() {
    return { ...this.realTimeMetrics };
  }

  // 获取缓存统计
  getCacheStats() {
    // 从处理器获取缓存统计
    // ERNIE处理器已移除
    if (window.unifiedProcessor) {
      return window.unifiedProcessor.getCacheStats ? window.unifiedProcessor.getCacheStats() : null;
    }
    return null;
  }

  // 清理资源
  destroy() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
    }
    this.savePerformanceData();
    console.log('📊 性能分析系统已关闭');
  }
}

// 全局暴露
window.PerformanceAnalytics = PerformanceAnalytics;