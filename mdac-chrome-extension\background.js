// Service Worker for MDAC Chrome Extension
console.log('🚀 MDAC扩展Service Worker已启动');

// 安装事件
chrome.runtime.onInstalled.addListener((details) => {
  console.log('🎉 MDAC扩展已安装，版本:', details.reason);
  
  // 为所有MDAC页面启用侧边栏
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
});

// 扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  console.log('🖱️ 扩展图标被点击，当前标签:', tab.url);
  
  // 检查是否在MDAC网站
  if (tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
    // 打开侧边栏
    await chrome.sidePanel.open({ tabId: tab.id });
    console.log('📱 侧边栏已打开');
  } else {
    // 不在MDAC网站，提示用户
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'MDAC填充助手',
      message: '请先访问MDAC官网再使用此扩展'
    });
  }
});

// 标签页更新事件
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  // 当页面加载完成且是MDAC网站时
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('imigresen-online.imi.gov.my/mdac')) {
    
    console.log('✅ MDAC页面加载完成:', tab.url);
    
    // 注入内容脚本确保功能正常
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
      console.log('📝 内容脚本注入成功');
    } catch (error) {
      console.log('⚠️ 内容脚本注入失败:', error.message);
    }
  }
});

// 处理来自侧边栏的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('📨 收到消息:', request);
  
  switch (request.action) {
    case 'fillMDACForm':
      handleFillForm(request.data, sender, sendResponse);
      return true; // 保持消息通道开启
      
    case 'getCurrentTab':
      getCurrentTabInfo(sendResponse);
      return true;
      
    case 'saveUserData':
      saveUserData(request.data, sendResponse);
      return true;
      
    case 'loadUserData':
      loadUserData(sendResponse);
      return true;
      
    default:
      console.log('❓ 未知消息类型:', request.action);
      sendResponse({ success: false, error: '未知消息类型' });
  }
});

// 处理表单填充
async function handleFillForm(formData, sender, sendResponse) {
  try {
    console.log('🔄 开始处理表单填充请求...');
    
    // 获取当前活动标签页
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (!activeTab || !activeTab.url.includes('imigresen-online.imi.gov.my')) {
      throw new Error('请在MDAC官网页面使用此功能');
    }
    
    // 向内容脚本发送填充请求
    const response = await chrome.tabs.sendMessage(activeTab.id, {
      action: 'fillForm',
      data: formData
    });
    
    console.log('✅ 表单填充完成:', response);
    sendResponse({ success: true, result: response });
    
  } catch (error) {
    console.error('❌ 表单填充失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取当前标签页信息
async function getCurrentTabInfo(sendResponse) {
  try {
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    sendResponse({ 
      success: true, 
      tab: {
        id: activeTab.id,
        url: activeTab.url,
        title: activeTab.title,
        isMDACPage: activeTab.url.includes('imigresen-online.imi.gov.my')
      }
    });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// 保存用户数据
async function saveUserData(data, sendResponse) {
  try {
    await chrome.storage.sync.set(data);
    console.log('💾 用户数据已保存:', Object.keys(data));
    sendResponse({ success: true });
  } catch (error) {
    console.error('❌ 保存数据失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 加载用户数据
async function loadUserData(sendResponse) {
  try {
    const data = await chrome.storage.sync.get(null);
    console.log('📂 已加载用户数据:', Object.keys(data));
    sendResponse({ success: true, data });
  } catch (error) {
    console.error('❌ 加载数据失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
  console.log('⏸️ Service Worker即将暂停');
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log('🔄 存储数据发生变化:', changes, 'namespace:', namespace);
});
