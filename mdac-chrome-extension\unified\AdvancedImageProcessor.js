// 高级图片处理器 - 智能压缩和质量增强
// 用途：提供多级压缩、质量增强、格式优化的图片处理
// 依赖：浏览器Canvas API, ImageData API
// 技术栈：原生JavaScript + 图像处理算法
// 核心功能：智能压缩、质量增强、格式优化、批量处理
// 重要：提升豆包API识别准确率，减少处理时间

class AdvancedImageProcessor {
    constructor(options = {}) {
        // 高级配置选项
        this.config = {
            // 基础压缩参数
            maxSize: options.maxSize || 1200,
            quality: options.quality || 0.85,
            format: options.format || 'image/jpeg',

            // 智能压缩参数
            adaptiveQuality: options.adaptiveQuality !== false,
            contentAwareCompression: options.contentAwareCompression !== false,
            faceDetection: options.faceDetection !== false,

            // 质量增强参数
            enableEnhancement: options.enableEnhancement !== false,
            sharpnessLevel: options.sharpnessLevel || 0.3,
            contrastLevel: options.contrastLevel || 0.2,
            brightnessLevel: options.brightnessLevel || 0.1,

            // 性能参数
            maxConcurrent: options.maxConcurrent || 3,
            memoryLimit: options.memoryLimit || 100 * 1024 * 1024, // 100MB

            // 豆包优化参数
            optimizeForOCR: options.optimizeForOCR !== false,
            textEnhancement: options.textEnhancement !== false,
            documentMode: options.documentMode || 'auto' // auto, passport, id, general
        };

        // 处理统计
        this.processingStats = {
            totalProcessed: 0,
            averageCompressionRatio: 0,
            totalTimeSaved: 0,
            ocrAccuracyImprovement: 0
        };

        // 内存管理
        this.activeProcesses = 0;
        this.processQueue = [];

        console.log('🖼️ AdvancedImageProcessor 初始化完成');
    }

    /**
     * 批量处理图片（高级版本）
     */
    async processImagesAdvanced(imageFiles, options = {}) {
        if (!imageFiles || imageFiles.length === 0) return [];

        console.log(`🚀 开始高级图片处理: ${imageFiles.length} 张图片`);

        const startTime = performance.now();
        const results = [];
        const config = { ...this.config, ...options };

        try {
            // 图片预分析和分类
            const imageAnalysis = await this.analyzeImages(imageFiles);
            console.log('📊 图片分析完成:', imageAnalysis);

            // 根据分析结果调整处理参数
            const processingPlan = this.createProcessingPlan(imageAnalysis, config);

            // 批量处理（控制并发）
            const batchSize = Math.min(config.maxConcurrent, imageFiles.length);

            for (let i = 0; i < imageFiles.length; i += batchSize) {
                const batch = imageFiles.slice(i, i + batchSize);
                const batchPromises = batch.map((file, index) =>
                    this.processSingleImageAdvanced(file, processingPlan[i + index])
                );

                const batchResults = await Promise.allSettled(batchPromises);

                batchResults.forEach((result, index) => {
                    if (result.status === 'fulfilled' && result.value) {
                        results.push(result.value);
                    } else {
                        console.warn(`⚠️ 图片处理失败: ${batch[index]?.name}`, result.reason);
                    }
                });

                // 内存管理：小延迟让浏览器回收内存
                if (i + batchSize < imageFiles.length) {
                    await this.delay(100);
                }
            }

            // 后处理和验证
            const validatedResults = await this.postProcessResults(results, imageAnalysis);

            // 更新统计
            this.updateProcessingStats(validatedResults, performance.now() - startTime);

            console.log(`✅ 高级图片处理完成: ${validatedResults.length}/${imageFiles.length} 成功`);
            return validatedResults;

        } catch (error) {
            console.error('❌ 高级图片处理失败:', error);
            throw error;
        }
    }

    /**
     * 分析图片内容
     */
    async analyzeImages(imageFiles) {
        const analysisPromises = imageFiles.map(async (file) => {
            try {
                const image = await this.loadImage(file);
                const analysis = await this.analyzeImageContent(image, file);

                // 释放图片内存
                URL.revokeObjectURL(image.src);

                return {
                    file: file.name,
                    type: analysis.type,
                    confidence: analysis.confidence,
                    features: analysis.features,
                    recommendedProcessing: analysis.recommendedProcessing
                };
            } catch (error) {
                console.warn(`⚠️ 图片分析失败: ${file.name}`, error);
                return {
                    file: file.name,
                    type: 'unknown',
                    confidence: 0,
                    features: {},
                    recommendedProcessing: 'standard'
                };
            }
        });

        return Promise.all(analysisPromises);
    }

    /**
     * 分析单张图片内容
     */
    async analyzeImageContent(image, file) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = Math.min(image.width, 200); // 缩小分析以提高性能
        canvas.height = Math.min(image.height, 200);

        ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

        // 基础特征分析
        const features = this.extractBasicFeatures(imageData);

        // 内容类型检测
        const contentType = this.detectContentType(features, file);

        // 推荐处理策略
        const recommendedProcessing = this.determineProcessingStrategy(contentType, features);

        // 清理canvas
        canvas.width = 0;
        canvas.height = 0;

        return {
            type: contentType.type,
            confidence: contentType.confidence,
            features: features,
            recommendedProcessing: recommendedProcessing
        };
    }

    /**
     * 提取基础特征
     */
    extractBasicFeatures(imageData) {
        const { data, width, height } = imageData;

        let totalBrightness = 0;
        let totalContrast = 0;
        let edgeDensity = 0;
        let textIndicators = 0;
        let faceIndicators = 0;

        // 简化特征提取
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // 亮度
            const brightness = (r + g + b) / 3;
            totalBrightness += brightness;

            // 对比度（简化计算）
            const contrast = Math.max(r, g, b) - Math.min(r, g, b);
            totalContrast += contrast;

            // 边缘检测（简化）
            if (i > 0 && i < data.length - 4) {
                const prevBrightness = (data[i - 4] + data[i - 3] + data[i - 2]) / 3;
                const brightnessDiff = Math.abs(brightness - prevBrightness);
                if (brightnessDiff > 30) edgeDensity++;
            }

            // 文本指示器（高对比度区域）
            if (contrast > 100) textIndicators++;

            // 肤色检测（简化版）
            const isSkinColor = this.isSkinColor(r, g, b);
            if (isSkinColor) faceIndicators++;
        }

        const pixelCount = width * height;

        return {
            averageBrightness: totalBrightness / pixelCount,
            averageContrast: totalContrast / pixelCount,
            edgeDensity: edgeDensity / pixelCount,
            textProbability: textIndicators / pixelCount,
            faceProbability: faceIndicators / pixelCount,
            sharpness: this.estimateSharpness(imageData)
        };
    }

    /**
     * 检测内容类型
     */
    detectContentType(features, file) {
        const { textProbability, faceProbability, edgeDensity, averageContrast } = features;

        // 文档类型检测
        if (textProbability > 0.15 && edgeDensity > 0.1) {
            return { type: 'document', confidence: 0.8 };
        }

        // 证件类型检测（基于文件名启发式）
        if (this.isLikelyIDDocument(file.name)) {
            return { type: 'id_document', confidence: 0.9 };
        }

        // 人脸检测
        if (faceProbability > 0.2) {
            return { type: 'portrait', confidence: 0.7 };
        }

        // 一般图片
        return { type: 'general', confidence: 0.5 };
    }

    /**
     * 确定处理策略
     */
    determineProcessingStrategy(contentType, features) {
        switch (contentType.type) {
            case 'document':
                return {
                    mode: 'document',
                    quality: 0.9,
                    enhanceText: true,
                    sharpenLevel: 0.4,
                    contrastBoost: 0.3
                };

            case 'id_document':
                return {
                    mode: 'id_document',
                    quality: 0.95,
                    enhanceText: true,
                    sharpenLevel: 0.5,
                    contrastBoost: 0.4,
                    noiseReduction: true
                };

            case 'portrait':
                return {
                    mode: 'portrait',
                    quality: 0.85,
                    enhanceFace: true,
                    sharpenLevel: 0.2,
                    brightnessAdjust: 0.1
                };

            default:
                return {
                    mode: 'general',
                    quality: 0.85,
                    adaptiveQuality: true
                };
        }
    }

    /**
     * 处理单张图片（高级版本）
     */
    async processSingleImageAdvanced(imageFile, processingConfig) {
        console.log(`🔧 高级处理图片: ${imageFile.name}`, processingConfig);

        const startTime = performance.now();

        try {
            // 加载原始图片
            const originalImage = await this.loadImage(imageFile);

            // 应用智能压缩
            const compressedImage = await this.applySmartCompression(originalImage, processingConfig);

            // 应用质量增强
            const enhancedImage = processingConfig.enableEnhancement
                ? await this.applyQualityEnhancement(compressedImage, processingConfig)
                : compressedImage;

            // OCR优化（如果需要）
            const finalImage = processingConfig.optimizeForOCR
                ? await this.optimizeForOCR(enhancedImage, processingConfig)
                : enhancedImage;

            // 转换为输出格式
            const result = await this.convertToOutputFormat(finalImage, imageFile, processingConfig);

            // 验证结果
            const isValid = await this.validateAdvancedResult(result, originalImage);

            if (!isValid) {
                throw new Error('高级图片处理结果验证失败');
            }

            const processingTime = performance.now() - startTime;

            console.log(`✅ 高级图片处理完成: ${imageFile.name} (${processingTime.toFixed(2)}ms)`);

            return {
                ...result,
                processingTime,
                processingConfig: processingConfig,
                success: true
            };

        } catch (error) {
            console.error(`❌ 高级图片处理失败: ${imageFile.name}`, error);

            // 回退到基础处理
            console.log(`🔄 回退到基础处理: ${imageFile.name}`);
            return this.fallbackToBasicProcessing(imageFile);
        }
    }

    /**
     * 应用智能压缩
     */
    async applySmartCompression(image, config) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 计算最优尺寸
        const optimalSize = this.calculateOptimalSize(image.width, image.height, config);

        canvas.width = optimalSize.width;
        canvas.height = optimalSize.height;

        // 高质量缩放
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // 自适应质量
        const quality = this.calculateAdaptiveQuality(image, config);

        // 绘制图片
        ctx.drawImage(image, 0, 0, optimalSize.width, optimalSize.height);

        // 转换为blob
        return new Promise((resolve) => {
            canvas.toBlob((blob) => {
                resolve({
                    blob,
                    canvas,
                    quality,
                    dimensions: optimalSize
                });
            }, config.format || this.config.format, quality);
        });
    }

    /**
     * 应用质量增强
     */
    async applyQualityEnhancement(imageData, config) {
        const { canvas, quality, dimensions } = imageData;
        const ctx = canvas.getContext('2d');

        // 获取图像数据
        const imageDataObj = ctx.getImageData(0, 0, dimensions.width, dimensions.height);

        // 应用增强滤镜
        const enhancedData = this.applyEnhancementFilters(imageDataObj, config);

        // 应用锐化
        if (config.sharpenLevel > 0) {
            this.applySharpening(enhancedData, config.sharpenLevel);
        }

        // 应用对比度增强
        if (config.contrastBoost > 0) {
            this.applyContrastEnhancement(enhancedData, config.contrastBoost);
        }

        // 应用亮度调整
        if (config.brightnessAdjust !== 0) {
            this.applyBrightnessAdjustment(enhancedData, config.brightnessAdjust);
        }

        // 降噪处理
        if (config.noiseReduction) {
            this.applyNoiseReduction(enhancedData);
        }

        // 应用增强后的数据
        ctx.putImageData(enhancedData, 0, 0);

        return canvas;
    }

    /**
     * OCR优化
     */
    async optimizeForOCR(canvas, config) {
        const ctx = canvas.getContext('2d');

        if (config.textEnhancement) {
            // 文本区域增强
            const enhancedData = this.enhanceTextRegions(ctx.getImageData(0, 0, canvas.width, canvas.height));
            ctx.putImageData(enhancedData, 0, 0);
        }

        if (config.mode === 'document' || config.mode === 'id_document') {
            // 文档专用优化
            this.applyDocumentOptimizations(ctx, canvas.width, canvas.height);
        }

        return canvas;
    }

    /**
     * 计算最优尺寸（考虑豆包API要求）
     */
    calculateOptimalSize(originalWidth, originalHeight, config) {
        const maxSize = config.maxSize || this.config.maxSize;

        // 豆包API推荐尺寸：保持宽高比，最大1200px
        if (originalWidth <= maxSize && originalHeight <= maxSize) {
            // 如果图片已经很小，保持原尺寸以提高质量
            return { width: originalWidth, height: originalHeight };
        }

        const ratio = Math.min(maxSize / originalWidth, maxSize / originalHeight);

        // 确保证件类图片的关键尺寸
        if (config.mode === 'id_document') {
            // 证件照片通常需要更高的分辨率
            const docRatio = Math.min(800 / originalWidth, 800 / originalHeight);
            return {
                width: Math.round(originalWidth * Math.max(ratio, docRatio)),
                height: Math.round(originalHeight * Math.max(ratio, docRatio))
            };
        }

        return {
            width: Math.round(originalWidth * ratio),
            height: Math.round(originalHeight * ratio)
        };
    }

    /**
     * 计算自适应质量
     */
    calculateAdaptiveQuality(originalImage, config) {
        let baseQuality = config.quality || this.config.quality;

        // 根据内容类型调整质量
        if (config.mode === 'id_document') {
            baseQuality = Math.max(baseQuality, 0.9); // 证件需要高质量
        } else if (config.mode === 'document') {
            baseQuality = Math.max(baseQuality, 0.85); // 文档需要较高质量
        }

        // 根据图片复杂度调整
        const imageSize = originalImage.width * originalImage.height;
        if (imageSize > 2000 * 2000) {
            baseQuality = Math.max(baseQuality - 0.05, 0.8); // 大图片稍微降低质量
        }

        return baseQuality;
    }

    /**
     * 应用增强滤镜
     */
    applyEnhancementFilters(imageData, config) {
        const { data } = imageData;

        for (let i = 0; i < data.length; i += 4) {
            let r = data[i];
            let g = data[i + 1];
            let b = data[i + 2];

            // 简单的颜色增强
            const enhancement = 1.1;
            r = Math.min(255, r * enhancement);
            g = Math.min(255, g * enhancement);
            b = Math.min(255, b * enhancement);

            data[i] = r;
            data[i + 1] = g;
            data[i + 2] = b;
        }

        return imageData;
    }

    /**
     * 应用锐化
     */
    applySharpening(imageData, level) {
        const { data, width, height } = imageData;
        const output = new Uint8ClampedArray(data);

        // 简化的锐化算法
        const kernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];

        const factor = level;

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) {
                    let sum = 0;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                            const kidx = (ky + 1) * 3 + (kx + 1);
                            sum += data[idx] * kernel[kidx];
                        }
                    }

                    const idx = (y * width + x) * 4 + c;
                    output[idx] = Math.min(255, Math.max(0, sum * factor + data[idx] * (1 - factor)));
                }
            }
        }

        imageData.data.set(output);
        return imageData;
    }

    /**
     * 应用对比度增强
     */
    applyContrastEnhancement(imageData, boost) {
        const { data } = imageData;
        const factor = (259 * (boost + 255)) / (255 * (259 - boost));

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));
            data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128));
            data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128));
        }

        return imageData;
    }

    /**
     * 文档优化
     */
    applyDocumentOptimizations(ctx, width, height) {
        // 文档专用优化：增加对比度、锐化文本
        const imageData = ctx.getImageData(0, 0, width, height);

        // 二值化处理（简化版）
        for (let i = 0; i < imageData.data.length; i += 4) {
            const gray = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3;
            const binary = gray > 128 ? 255 : 0;

            // 只应用到高对比度区域（简化检测）
            if (gray < 64 || gray > 192) {
                imageData.data[i] = binary;
                imageData.data[i + 1] = binary;
                imageData.data[i + 2] = binary;
            }
        }

        ctx.putImageData(imageData, 0, 0);
    }

    /**
     * 辅助方法
     */
    loadImage(file) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const objectUrl = URL.createObjectURL(file);

            img.onload = () => {
                URL.revokeObjectURL(objectUrl);
                resolve(img);
            };

            img.onerror = () => {
                URL.revokeObjectURL(objectUrl);
                reject(new Error(`无法加载图片: ${file.name}`));
            };

            img.src = objectUrl;
        });
    }

    isLikelyIDDocument(filename) {
        const keywords = ['passport', 'id', 'identity', 'document', 'card', 'license'];
        const lowerName = filename.toLowerCase();
        return keywords.some(keyword => lowerName.includes(keyword));
    }

    isSkinColor(r, g, b) {
        // 简化的肤色检测
        return (r > 95 && g > 40 && b > 20 &&
                Math.max(r, g, b) - Math.min(r, g, b) > 15 &&
                Math.abs(r - g) > 15 && r > g && r > b);
    }

    estimateSharpness(imageData) {
        // 简化的清晰度估算
        const { data, width, height } = imageData;
        let sharpness = 0;

        for (let i = 0; i < data.length - 4; i += 4) {
            const diff = Math.abs(data[i] - data[i + 4]) +
                        Math.abs(data[i + 1] - data[i + 5]) +
                        Math.abs(data[i + 2] - data[i + 6]);
            sharpness += diff;
        }

        return sharpness / (width * height * 3);
    }

    enhanceTextRegions(imageData) {
        // 简化的文本区域增强
        const { data, width, height } = imageData;

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = (y * width + x) * 4;

                // 检测可能的文本区域（高对比度）
                const contrast = this.calculateLocalContrast(imageData, x, y);

                if (contrast > 50) {
                    // 增强对比度
                    data[idx] = Math.min(255, data[idx] * 1.2);
                    data[idx + 1] = Math.min(255, data[idx + 1] * 1.2);
                    data[idx + 2] = Math.min(255, data[idx + 2] * 1.2);
                }
            }
        }

        return imageData;
    }

    calculateLocalContrast(imageData, x, y) {
        const { data, width } = imageData;
        const idx = (y * width + x) * 4;

        if (x > 0 && x < width - 1 && y > 0 && y < imageData.height - 1) {
            const centerBrightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;

            let totalDiff = 0;
            let count = 0;

            for (let dy = -1; dy <= 1; dy++) {
                for (let dx = -1; dx <= 1; dx++) {
                    if (dx === 0 && dy === 0) continue;

                    const nIdx = ((y + dy) * width + (x + dx)) * 4;
                    const neighborBrightness = (data[nIdx] + data[nIdx + 1] + data[nIdx + 2]) / 3;
                    totalDiff += Math.abs(centerBrightness - neighborBrightness);
                    count++;
                }
            }

            return totalDiff / count;
        }

        return 0;
    }

    applyNoiseReduction(imageData) {
        // 简化的降噪处理
        const { data, width, height } = imageData;
        const output = new Uint8ClampedArray(data);

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) {
                    let sum = 0;
                    let count = 0;

                    // 简单的均值滤波
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const idx = ((y + dy) * width + (x + dx)) * 4 + c;
                            sum += data[idx];
                            count++;
                        }
                    }

                    const idx = (y * width + x) * 4 + c;
                    output[idx] = sum / count;
                }
            }
        }

        imageData.data.set(output);
        return imageData;
    }

    applyBrightnessAdjustment(imageData, adjustment) {
        const { data } = imageData;
        const factor = adjustment;

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, data[i] + factor * 255));
            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + factor * 255));
            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + factor * 255));
        }

        return imageData;
    }

    convertToOutputFormat(canvas, originalFile, config) {
        return new Promise((resolve) => {
            canvas.toBlob((blob) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1];

                    resolve({
                        name: originalFile.name,
                        originalName: originalFile.name,
                        mimeType: config.format || this.config.format,
                        base64: base64,
                        size: blob.size,
                        originalSize: originalFile.size,
                        compressionRatio: Math.round((1 - blob.size / originalFile.size) * 100),
                        dimensions: { width: canvas.width, height: canvas.height },
                        originalDimensions: { width: canvas.width, height: canvas.height }, // 需要获取原始尺寸
                        processingTime: 0, // 将在外部计算
                        success: true
                    });
                };
                reader.readAsDataURL(blob);
            }, config.format || this.config.format, config.quality || this.config.quality);
        });
    }

    postProcessResults(results, originalAnalysis) {
        // 验证所有结果
        const validatedResults = results.filter(result => this.validateAdvancedResult(result));

        // 优化压缩率统计
        this.optimizeCompressionStats(validatedResults, originalAnalysis);

        return validatedResults;
    }

    validateAdvancedResult(result) {
        if (!result || !result.base64 || !result.success) return false;

        // 检查压缩率是否合理
        if (result.compressionRatio < -10) { // 允许最多增大10%
            console.warn(`压缩率异常: ${result.name} (${result.compressionRatio}%)`);
            return false;
        }

        // 检查文件大小
        if (result.size > result.originalSize * 1.5) {
            console.warn(`输出文件过大: ${result.name}`);
            return false;
        }

        return true;
    }

    optimizeCompressionStats(results, analysis) {
        // 根据分析结果优化压缩统计
        results.forEach((result, index) => {
            const analysis = analysis[index];
            if (analysis && analysis.recommendedProcessing) {
                result.qualityOptimization = analysis.recommendedProcessing;
            }
        });
    }

    updateProcessingStats(results, totalTime) {
        this.processingStats.totalProcessed += results.length;

        if (results.length > 0) {
            const avgCompression = results.reduce((sum, r) => sum + r.compressionRatio, 0) / results.length;
            this.processingStats.averageCompressionRatio = avgCompression;
        }

        this.processingStats.totalTimeSaved += totalTime;

        console.log(`📊 处理统计更新 - 总计: ${this.processingStats.totalProcessed}, 平均压缩率: ${this.processingStats.averageCompressionRatio.toFixed(1)}%`);
    }

    fallbackToBasicProcessing(imageFile) {
        // 回退到基础处理
        console.log(`🔄 回退到基础处理: ${imageFile.name}`);

        // 这里可以调用基础的ImageProcessor
        if (window.ImageProcessor) {
            const basicProcessor = new window.ImageProcessor();
            return basicProcessor.compressImage(imageFile);
        }

        throw new Error('基础处理不可用');
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取高级处理统计
     */
    getAdvancedStats() {
        return {
            ...this.processingStats,
            config: this.config,
            activeProcesses: this.activeProcesses,
            queueLength: this.processQueue.length
        };
    }
}

// 全局暴露
window.AdvancedImageProcessor = AdvancedImageProcessor;

console.log('🖼️ AdvancedImageProcessor 系统已初始化');