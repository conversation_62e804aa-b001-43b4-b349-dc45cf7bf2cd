// 字段映射验证工具
// 用途：验证统一多模态处理器返回的数据能否正确映射到表单字段
// 依赖：form-mapper.js
// 技术栈：原生JavaScript
// 核心功能：字段存在性验证、数据类型检查、映射准确性验证

class FieldMappingValidator {
  constructor() {
    this.formMapper = null; // 延迟初始化，等待formMapper可用
    this.validationResults = {};
  }

  // 获取formMapper实例（延迟初始化）
  getFormMapper() {
    if (!this.formMapper) {
      this.formMapper = window.formMapper;
      if (!this.formMapper) {
        console.warn('FormMapper 还未初始化，使用备用验证方法');
        console.warn('确保 form-mapper.js 在 field-mapping-validator.js 之前加载');
        return null;
      }
    }
    return this.formMapper;
  }

  // 验证所有字段映射
  validateAllMappings() {
    console.log('🔍 开始验证字段映射...');

    // 检查当前是否在侧边栏环境
    if (window.location.pathname.includes('sidepanel.html')) {
      console.log('ℹ️ 当前在侧边栏环境，跳过字段映射验证（仅在MDAC网站上验证）');
      return {
        availableCount: 0,
        missingCount: 0,
        available: {},
        missing: {},
        totalFields: 21,
        isSidePanelEnvironment: true
      };
    }

    const formMapper = this.getFormMapper();
    if (!formMapper) {
      console.warn('FormMapper 不可用，跳过验证');
      return this.getBasicValidation();
    }

    const { available, missing } = formMapper.getAvailableFields();

    console.log(`✅ 可用字段 (${Object.keys(available).length}个):`, available);
    console.log(`❌ 缺失字段 (${Object.keys(missing).length}个):`, missing);

    return {
      availableCount: Object.keys(available).length,
      missingCount: Object.keys(missing).length,
      available,
      missing,
      totalFields: Object.keys(formMapper.fieldMapping).length
    };
  }

  // 基础验证（当formMapper不可用时）
  getBasicValidation() {
    // 使用MDAC网站原生字段ID进行基础验证（与form-mapper.js保持一致）
    const basicMapping = {
      name: '#name',
      passNo: '#passNo',
      dob: '#dob',
      nationality: '#nationality',
      sex: '#sex',
      passExpDte: '#passExpDte',
      email: '#email',
      confirmEmail: '#confirmEmail',
      region: '#region',
      mobile: '#mobile',
      arrDt: '#arrDt',
      depDt: '#depDt',
      vesselNm: '#vesselNm',
      trvlMode: '#trvlMode',
      embark: '#embark',
      accommodationStay: '#accommodationStay',
      accommodationAddress1: '#accommodationAddress1',
      accommodationAddress2: '#accommodationAddress2',
      accommodationState: '#accommodationState',
      accommodationCity: '#accommodationCity',
      accommodationPostcode: '#accommodationPostcode'
    };

    const available = {};
    const missing = {};

    for (const [fieldName, selector] of Object.entries(basicMapping)) {
      if (selector && document.querySelector(selector)) {
        available[fieldName] = selector;
      } else {
        missing[fieldName] = selector || '无选择器';
      }
    }

    console.log(`✅ 基础验证 - 可用字段 (${Object.keys(available).length}个):`, available);
    console.log(`❌ 基础验证 - 缺失字段 (${Object.keys(missing).length}个):`, missing);

    return {
      availableCount: Object.keys(available).length,
      missingCount: Object.keys(missing).length,
      available,
      missing,
      totalFields: Object.keys(basicMapping).length,
      isBasicValidation: true
    };
  }

  // 验证数据是否能成功填入表单
  validateDataMapping(data) {
    if (!data || typeof data !== 'object') {
      return {
        success: false,
        error: '数据无效',
        details: null
      };
    }

    const formMapper = this.getFormMapper();
    if (!formMapper) {
      return this.validateDataMappingBasic(data);
    }

    const results = {
      success: true,
      totalFields: 0,
      mappedFields: 0,
      skippedFields: 0,
      errorFields: 0,
      details: {}
    };

    for (const [fieldName, value] of Object.entries(data)) {
      results.totalFields++;

      if (value === null || value === undefined || value === '') {
        results.skippedFields++;
        results.details[fieldName] = { status: 'skipped', reason: '值为空' };
        continue;
      }

      // 检查字段是否可映射
      if (!formMapper.isFieldAvailable(fieldName)) {
        results.errorFields++;
        results.details[fieldName] = {
          status: 'error',
          reason: '表单中不存在对应字段',
          selector: formMapper.getFieldSelector(fieldName)
        };
        results.success = false;
        continue;
      }

      // 验证数据格式
      const validation = formMapper.validateFieldData(fieldName, value);
      if (!validation.valid) {
        results.errorFields++;
        results.details[fieldName] = {
          status: 'error',
          reason: `数据验证失败: ${validation.error}`,
          value: value
        };
        results.success = false;
        continue;
      }

      results.mappedFields++;
      results.details[fieldName] = {
        status: 'success',
        value: value,
        selector: formMapper.getFieldSelector(fieldName)
      };
    }

    return results;
  }

  // 基础数据验证（当formMapper不可用时）
  validateDataMappingBasic(data) {
    const basicMapping = {
      name: '#name',
      passNo: '#passNo',
      dob: '#dob',
      nationality: '#nationality',
      sex: '#sex',
      passExpDte: '#passExpDte',
      email: '#email',
      confirmEmail: '#confirmEmail',
      region: '#region',
      mobile: '#mobile',
      arrDt: '#arrDt',
      depDt: '#depDt',
      vesselNm: '#vesselNm',
      trvlMode: '#trvlMode',
      embark: '#embark',
      accommodationStay: '#accommodationStay',
      accommodationAddress1: '#accommodationAddress1',
      accommodationAddress2: '#accommodationAddress2',
      accommodationState: '#accommodationState',
      accommodationCity: '#accommodationCity',
      accommodationPostcode: '#accommodationPostcode'
    };

    const results = {
      success: true,
      totalFields: 0,
      mappedFields: 0,
      skippedFields: 0,
      errorFields: 0,
      details: {}
    };

    for (const [fieldName, value] of Object.entries(data)) {
      results.totalFields++;

      if (value === null || value === undefined || value === '') {
        results.skippedFields++;
        results.details[fieldName] = { status: 'skipped', reason: '值为空' };
        continue;
      }

      const selector = basicMapping[fieldName];
      if (!selector) {
        results.errorFields++;
        results.details[fieldName] = { status: 'error', reason: '字段映射不存在' };
        results.success = false;
        continue;
      }

      const element = document.querySelector(selector);
      if (!element) {
        results.errorFields++;
        results.details[fieldName] = { status: 'error', reason: '表单元素不存在', selector };
        results.success = false;
        continue;
      }

      results.mappedFields++;
      results.details[fieldName] = { status: 'success', element, selector };
    }

    return {
      success: results.mappedFields > 0,
      successRate: Math.round((results.mappedFields / results.totalFields) * 100),
      ...results,
      isBasicValidation: true
    };
  }

  // 生成映射验证报告
  generateValidationReport(data = null) {
    const mappingValidation = this.validateAllMappings();
    let dataValidation = null;

    if (data) {
      dataValidation = this.validateDataMapping(data);
    }

    const report = {
      timestamp: new Date().toISOString(),
      mapping: mappingValidation,
      data: dataValidation,
      recommendations: this.generateRecommendations(mappingValidation, dataValidation)
    };

    return report;
  }

  // 生成修复建议
  generateRecommendations(mappingValidation, dataValidation) {
    const recommendations = [];

    // 字段映射建议
    if (mappingValidation.missingCount > 0) {
      recommendations.push({
        type: 'mapping',
        priority: 'high',
        title: '修复缺失的字段映射',
        details: `${mappingValidation.missingCount}个字段无法映射到表单`,
        action: '更新form-mapper.js中的fieldMapping配置'
      });
    }

    // 数据验证建议
    if (dataValidation && dataValidation.errorFields > 0) {
      recommendations.push({
        type: 'data',
        priority: 'medium',
        title: '修复数据格式问题',
        details: `${dataValidation.errorFields}个字段数据格式不正确`,
        action: '检查统一多模态处理器的提示词，确保返回正确格式的数据'
      });
    }

    // 成功率建议
    if (dataValidation && dataValidation.totalFields > 0) {
      const successRate = (dataValidation.mappedFields / dataValidation.totalFields) * 100;
      if (successRate < 80) {
        recommendations.push({
          type: 'performance',
          priority: 'medium',
          title: '提升字段映射成功率',
          details: `当前成功率${successRate.toFixed(1)}%，建议目标90%以上`,
          action: '优化LLM提示词，提升数据提取准确性'
        });
      }
    }

    return recommendations;
  }

  // 实时监控字段映射状态
  startMappingMonitor() {
    const monitor = () => {
      const validation = this.validateAllMappings();

      if (validation.missingCount > 0) {
        console.warn(`⚠️ 字段映射监控: ${validation.missingCount}个字段缺失`);
      } else {
        console.log(`✅ 字段映射监控: 所有${validation.availableCount}个字段正常`);
      }
    };

    // 立即执行一次
    monitor();

    // 每30秒检查一次
    setInterval(monitor, 30000);
  }

  // 测试特定数据的映射效果
  testDataMapping(testData) {
    console.log('🧪 测试数据映射效果...');

    const validation = this.validateDataMapping(testData);

    console.log(`📊 映射结果:`);
    console.log(`  总字段数: ${validation.totalFields}`);
    console.log(`  成功映射: ${validation.mappedFields}`);
    console.log(`  跳过字段: ${validation.skippedFields}`);
    console.log(`  错误字段: ${validation.errorFields}`);
    console.log(`  成功率: ${((validation.mappedFields / validation.totalFields) * 100).toFixed(1)}%`);

    if (validation.errorFields > 0) {
      console.log('❌ 错误详情:');
      Object.entries(validation.details)
        .filter(([_, detail]) => detail.status === 'error')
        .forEach(([field, detail]) => {
          console.log(`  - ${field}: ${detail.reason}`);
        });
    }

    return validation;
  }

  // 创建字段映射可视化
  createMappingVisualization() {
    const { available, missing } = this.formMapper.getAvailableFields();

    const visualization = {
      available: Object.keys(available).map(field => ({
        field,
        selector: available[field],
        label: this.formMapper.getFieldLabel(field),
        element: document.querySelector(available[field])
      })),
      missing: Object.keys(missing).map(field => ({
        field,
        selector: missing[field],
        label: this.formMapper.getFieldLabel(field)
      }))
    };

    return visualization;
  }

  // 导出验证报告为JSON
  exportValidationReport(data = null) {
    const report = this.generateValidationReport(data);
    const json = JSON.stringify(report, null, 2);

    // 创建下载链接
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `field-mapping-validation-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    return report;
  }
}

// 全局暴露
window.FieldMappingValidator = FieldMappingValidator;

// 自动创建全局实例
if (typeof window !== 'undefined') {
  window.fieldMappingValidator = new FieldMappingValidator();

  // 延迟初始化函数，确保所有依赖加载完成
  function initFieldMappingValidator() {
    // 检查formMapper是否可用
    if (window.formMapper) {
      console.log('✅ FormMapper 已就绪，使用完整验证模式');
      window.fieldMappingValidator.validateAllMappings();
    } else {
      console.warn('⚠️ FormMapper 未就绪，使用基础验证模式');
      // 等待一段时间再试
      setTimeout(() => {
        if (window.formMapper) {
          console.log('✅ FormMapper 延迟加载成功，切换到完整验证模式');
        } else {
          console.warn('⚠️ FormMapper 仍然不可用，继续使用基础验证模式');
        }
        window.fieldMappingValidator.validateAllMappings();
      }, 2000);
    }
  }

  // 页面加载完成后自动验证
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initFieldMappingValidator);
  } else {
    // 如果页面已经加载完成，立即初始化
    setTimeout(initFieldMappingValidator, 500);
  }
}