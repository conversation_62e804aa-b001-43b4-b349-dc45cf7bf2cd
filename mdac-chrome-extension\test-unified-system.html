<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC 统一多模态系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .file-upload {
            border: 2px dashed #007bff;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        .file-upload.dragover {
            background: #e3f2fd;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .log-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-entry.info { color: #4CAF50; }
        .log-entry.warn { color: #FF9800; }
        .log-entry.error { color: #F44336; }
        .log-entry.debug { color: #2196F3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 MDAC 统一多模态系统测试</h1>
        <p>此页面用于测试和验证文件上传到API调用的完整流程。</p>

        <div class="test-section">
            <h3>📋 系统状态检查</h3>
            <div id="systemStatus">正在检查...</div>
            <button class="btn" onclick="checkSystemStatus()">重新检查</button>
        </div>

        <div class="test-section">
            <h3>📁 文件上传测试</h3>
            <div class="file-upload" id="fileUploadArea">
                <p>拖拽文件到此处或点击选择文件</p>
                <input type="file" id="fileInput" multiple accept="image/*,.pdf,.txt,.md,.json,.doc,.docx" style="display:none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
            </div>
            <div id="fileList"></div>
            <button class="btn" id="processBtn" onclick="startProcessing()" disabled>🚀 开始处理</button>
            <button class="btn" onclick="clearFiles()">🗑️ 清空文件</button>

            <div id="progressArea" style="display:none;">
                <h4>处理进度</h4>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div id="progressText">0%</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 处理结果</h3>
            <div id="resultArea">等待处理...</div>
        </div>

        <div class="test-section">
            <h3>📝 实时日志</h3>
            <div class="log-output" id="logOutput"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 模拟必要的全局对象 -->
    <script>
        // 模拟Chrome扩展环境
        window.chrome = {
            runtime: {
                id: 'test-extension-id'
            }
        };

        // 日志系统
        class TestLogger {
            constructor() {
                this.logs = [];
                this.maxLogs = 100;
            }

            log(level, message, data = null) {
                const timestamp = new Date().toISOString();
                const logEntry = {
                    timestamp,
                    level,
                    message,
                    data
                };

                this.logs.push(logEntry);
                if (this.logs.length > this.maxLogs) {
                    this.logs.shift();
                }

                this.displayLog(logEntry);
                console.log(`[${level.toUpperCase()}] ${message}`, data);
            }

            displayLog(logEntry) {
                const logOutput = document.getElementById('logOutput');
                const logDiv = document.createElement('div');
                logDiv.className = `log-entry ${logEntry.level}`;
                logDiv.innerHTML = `
                    <span style="color: #888;">[${logEntry.timestamp.split('T')[1].split('.')[0]}]</span>
                    <span style="color: ${this.getColorForLevel(logEntry.level)};">[${logEntry.level.toUpperCase()}]</span>
                    ${logEntry.message}
                    ${logEntry.data ? `<span style="color: #ccc;"> - ${JSON.stringify(logEntry.data)}</span>` : ''}
                `;
                logOutput.appendChild(logDiv);
                logOutput.scrollTop = logOutput.scrollHeight;
            }

            getColorForLevel(level) {
                const colors = {
                    info: '#4CAF50',
                    warn: '#FF9800',
                    error: '#F44336',
                    debug: '#2196F3'
                };
                return colors[level] || '#ccc';
            }

            clear() {
                this.logs = [];
                document.getElementById('logOutput').innerHTML = '';
            }
        }

        const testLogger = new TestLogger();

        // 模拟MDACExtension（增强版本，测试多模态处理）
        window.MDACExtension = class {
            constructor() {
                testLogger.log('info', 'MDACExtension 模拟实例已创建');
            }

            async handleAIParse() {
                testLogger.log('info', 'MDACExtension.handleAIParse 被调用');

                // 检查是否有文件需要处理
                const fileStatus = window.uiController?.getFileStatus();
                testLogger.log('info', '文件状态检查:', fileStatus);

                if (fileStatus?.shouldUseMultiModal) {
                    testLogger.log('info', '检测到文件，开始多模态处理...');

                    // 模拟多模态处理流程
                    const used = await window.uiController?.integrateWithExistingAIParse();
                    if (used) {
                        testLogger.log('info', '多模态处理完成，跳过文本处理');
                        return { success: true, message: '多模态处理完成' };
                    } else {
                        testLogger.log('info', '多模态处理器返回未使用，继续文本处理');
                    }
                }

                testLogger.log('info', '使用原有文本处理逻辑');
                return { success: true, message: '文本处理完成' };
            }

            applyFieldUpdates(data) {
                testLogger.log('info', 'MDACExtension.applyFieldUpdates 被调用', { fields: Object.keys(data) });
            }
        };

        // 模拟mdacExtension实例
        window.mdacExtension = new window.MDACExtension();

        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            const checks = [
                { name: 'MDACExtension', exists: !!window.MDACExtension },
                { name: 'mdacExtension', exists: !!window.mdacExtension },
                { name: 'ImageProcessor', exists: !!window.ImageProcessor },
                { name: 'FileProcessor', exists: !!window.FileProcessor },
                { name: 'UnifiedMultiModalProcessor', exists: !!window.UnifiedMultiModalProcessor },
                { name: 'UIController', exists: !!window.UIController },
                { name: 'uiController', exists: !!window.uiController }
            ];

            let html = '<div class="test-results">';
            checks.forEach(check => {
                const className = check.exists ? 'success' : 'error';
                const icon = check.exists ? '✅' : '❌';
                html += `<div class="test-result ${className}">${icon} ${check.name}: ${check.exists ? '已加载' : '未加载'}</div>`;
            });
            html += '</div>';
            statusDiv.innerHTML = html;

            testLogger.log('info', '系统状态检查完成', checks);
        }

        // 文件上传处理
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');
        let uploadedFiles = [];

        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            uploadedFiles = Array.from(files);
            testLogger.log('info', '文件已选择', { count: uploadedFiles.length, files: uploadedFiles.map(f => f.name) });

            updateFileList();
            document.getElementById('processBtn').disabled = uploadedFiles.length === 0;
        }

        function updateFileList() {
            const fileListDiv = document.getElementById('fileList');
            if (uploadedFiles.length === 0) {
                fileListDiv.innerHTML = '<p>未选择文件</p>';
                return;
            }

            let html = '<h4>已选择的文件:</h4><ul>';
            uploadedFiles.forEach(file => {
                html += `<li>${file.name} (${formatFileSize(file.size)})</li>`;
            });
            html += '</ul>';
            fileListDiv.innerHTML = html;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function clearFiles() {
            uploadedFiles = [];
            updateFileList();
            document.getElementById('processBtn').disabled = true;
            document.getElementById('resultArea').innerHTML = '等待处理...';
            testLogger.log('info', '文件已清空');
        }

        async function startProcessing() {
            if (uploadedFiles.length === 0) {
                testLogger.log('warn', '没有文件需要处理');
                return;
            }

            const processBtn = document.getElementById('processBtn');
            const progressArea = document.getElementById('progressArea');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const resultArea = document.getElementById('resultArea');

            processBtn.disabled = true;
            progressArea.style.display = 'block';
            resultArea.innerHTML = '正在处理...';

            testLogger.log('info', '开始处理文件', { count: uploadedFiles.length });

            try {
                // 模拟处理进度
                for (let i = 0; i <= 100; i += 10) {
                    progressBar.style.width = i + '%';
                    progressText.textContent = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                // 模拟处理结果
                const result = {
                    success: true,
                    message: '处理完成',
                    filesProcessed: uploadedFiles.length,
                    timestamp: new Date().toISOString(),
                    data: {
                        name: '测试用户',
                        passportNo: 'TEST123456',
                        nationality: '中国',
                        email: '<EMAIL>'
                    }
                };

                resultArea.innerHTML = `
                    <div class="test-result success">
                        <h4>✅ 处理成功</h4>
                        <p>文件数量: ${result.filesProcessed}</p>
                        <p>处理时间: ${result.timestamp}</p>
                        <p>提取的数据:</p>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    </div>
                `;

                testLogger.log('info', '处理完成', result);

            } catch (error) {
                resultArea.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ 处理失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                testLogger.log('error', '处理失败', error);
            } finally {
                processBtn.disabled = false;
                progressArea.style.display = 'none';
            }
        }

        function clearLog() {
            testLogger.clear();
        }

        // 页面加载完成后检查系统状态
        window.addEventListener('load', () => {
            testLogger.log('info', '测试页面已加载');
            checkSystemStatus();
        });
    </script>

    <!-- 加载实际的系统组件 -->
    <script src="./unified/ImageProcessor.js"></script>
    <script src="./unified/FileProcessor.js"></script>
    <script src="./unified/UnifiedMultiModalProcessor.js"></script>
    <script src="./unified/UIController.js"></script>
    <script src="./unified/integration-patch.js"></script>
</body>
</html>