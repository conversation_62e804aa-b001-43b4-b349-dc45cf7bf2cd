<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射准确性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .test-card {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }

        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover { background: #0056b3; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th { background: #f8f9fa; font-weight: 600; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-skipped { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 MDAC字段映射准确性测试</h1>

        <div class="test-card">
            <h2>📋 测试说明</h2>
            <p>此页面用于测试统一多模态处理器返回的数据是否能准确映射到MDAC表单字段。</p>
            <p><strong>重要</strong>：此测试页面需要在Chrome扩展环境中运行，因为它依赖于sidepanel.html中的DOM结构。</p>
        </div>

        <div class="test-card">
            <h2>🧪 模拟测试数据</h2>
            <button onclick="testWithSampleData()">测试示例数据</button>
            <button onclick="testFieldMappingOnly()">仅测试字段映射</button>
            <button onclick="exportValidationReport()">导出验证报告</button>

            <div id="testResults"></div>
        </div>

        <div class="test-card">
            <h2>📊 字段映射状态</h2>
            <button onclick="showMappingStatus()">显示映射状态</button>

            <div id="mappingStatus"></div>
        </div>

        <div class="test-card">
            <h2>🎯 自定义测试数据</h2>
            <textarea id="customData" rows="10" placeholder="输入JSON格式的测试数据...">
{
  "name": "LI MING",
  "passNo": "G12345678",
  "dob": "15/01/1990",
  "nationality": "CHN",
  "sex": "1",
  "passExpDte": "15/01/2030",
  "email": "<EMAIL>",
  "region": "86",
  "mobile": "13812345678",
  "arrDt": "01/08/2025",
  "depDt": "07/08/2025",
  "vesselNm": "MH123",
  "trvlMode": "1",
  "accommodationStay": "01",
  "accommodationAddress1": "Hotel KL City Center",
  "accommodationState": "14",
  "accommodationPostcode": "50000"
}
            </textarea>
            <br>
            <button onclick="testCustomData()">测试自定义数据</button>

            <div id="customResults"></div>
        </div>
    </div>

    <script>
        // 模拟form-mapper（实际测试需要在扩展环境中）
        const mockFormMapper = {
            fieldMapping: {
                name: '#passengerName',
                passNo: '#passportNo',
                dob: '#birthDate',
                nationality: '#nationality',
                sex: '#gender',
                passExpDte: '#passportExpiry',
                email: '#email',
                confirmEmail: '#email',
                region: '#phoneRegion',
                mobile: '#phoneNumber',
                arrDt: '#arrivalDate',
                depDt: '#departureDate',
                vesselNm: '#flightNo',
                trvlMode: '#travelMode',
                embark: null,
                accommodationStay: '#accommodationType',
                accommodationAddress1: '#address1',
                accommodationAddress2: null,
                accommodationState: '#state',
                accommodationCity: null,
                accommodationPostcode: '#postcode'
            },

            getFieldSelector(fieldName) {
                return this.fieldMapping[fieldName] || null;
            },

            isFieldAvailable(fieldName) {
                const selector = this.getFieldSelector(fieldName);
                return selector !== null;
            },

            getAvailableFields() {
                const available = {};
                const missing = {};

                for (const [fieldName, selector] of Object.entries(this.fieldMapping)) {
                    if (selector) {
                        available[fieldName] = selector;
                    } else {
                        missing[fieldName] = '无选择器';
                    }
                }

                return { available, missing };
            }
        };

        // 测试示例数据
        function testWithSampleData() {
            const sampleData = {
                name: "LI MING",
                passNo: "G12345678",
                dob: "15/01/1990",
                nationality: "CHN",
                sex: "1",
                passExpDte: "15/01/2030",
                email: "<EMAIL>",
                confirmEmail: "<EMAIL>",
                region: "86",
                mobile: "13812345678",
                arrDt: "01/08/2025",
                depDt: "07/08/2025",
                vesselNm: "MH123",
                trvlMode: "1",
                embark: "CHN",
                accommodationStay: "01",
                accommodationAddress1: "Hotel KL City Center",
                accommodationState: "14",
                accommodationCity: "KUALA LUMPUR",
                accommodationPostcode: "50000"
            };

            const result = validateDataMapping(sampleData);
            displayTestResults('testResults', result, sampleData);
        }

        // 仅测试字段映射
        function testFieldMappingOnly() {
            const { available, missing } = mockFormMapper.getAvailableFields();

            const result = {
                availableCount: Object.keys(available).length,
                missingCount: Object.keys(missing).length,
                available,
                missing,
                totalFields: Object.keys(mockFormMapper.fieldMapping).length
            };

            displayMappingResults('testResults', result);
        }

        // 测试自定义数据
        function testCustomData() {
            try {
                const customText = document.getElementById('customData').value.trim();
                const customData = JSON.parse(customText);

                const result = validateDataMapping(customData);
                displayTestResults('customResults', result, customData);
            } catch (error) {
                displayError('customResults', '无效的JSON格式: ' + error.message);
            }
        }

        // 显示映射状态
        function showMappingStatus() {
            const { available, missing } = mockFormMapper.getAvailableFields();

            let html = '<h3>📈 字段映射统计</h3>';
            html += `<p>总字段数: ${Object.keys(mockFormMapper.fieldMapping).length}</p>`;
            html += `<p>可用字段: ${Object.keys(available).length}</p>`;
            html += `<p>缺失字段: ${Object.keys(missing).length}</p>`;

            html += '<h4>✅ 可用字段</h4>';
            html += '<table><tr><th>字段名</th><th>选择器</th><th>状态</th></tr>';
            for (const [field, selector] of Object.entries(available)) {
                html += `<tr><td>${field}</td><td>${selector}</td><td class="status-success">✅ 可用</td></tr>`;
            }
            html += '</table>';

            if (Object.keys(missing).length > 0) {
                html += '<h4>❌ 缺失字段</h4>';
                html += '<table><tr><th>字段名</th><th>原因</th><th>状态</th></tr>';
                for (const [field, reason] of Object.entries(missing)) {
                    html += `<tr><td>${field}</td><td>${reason}</td><td class="status-error">❌ 缺失</td></tr>`;
                }
                html += '</table>';
            }

            document.getElementById('mappingStatus').innerHTML = html;
        }

        // 验证数据映射
        function validateDataMapping(data) {
            const results = {
                success: true,
                totalFields: 0,
                mappedFields: 0,
                skippedFields: 0,
                errorFields: 0,
                details: {}
            };

            for (const [fieldName, value] of Object.entries(data)) {
                results.totalFields++;

                if (value === null || value === undefined || value === '') {
                    results.skippedFields++;
                    results.details[fieldName] = { status: 'skipped', reason: '值为空' };
                    continue;
                }

                if (!mockFormMapper.isFieldAvailable(fieldName)) {
                    results.errorFields++;
                    results.details[fieldName] = {
                        status: 'error',
                        reason: '表单中不存在对应字段',
                        selector: mockFormMapper.getFieldSelector(fieldName)
                    };
                    results.success = false;
                    continue;
                }

                results.mappedFields++;
                results.details[fieldName] = {
                    status: 'success',
                    value: value,
                    selector: mockFormMapper.getFieldSelector(fieldName)
                };
            }

            return results;
        }

        // 显示测试结果
        function displayTestResults(containerId, result, originalData = null) {
            const successRate = result.totalFields > 0 ?
                ((result.mappedFields / result.totalFields) * 100).toFixed(1) : 0;

            let html = `<div class="test-result ${result.success ? 'success' : 'error'}">`;
            html += `📊 验证结果概要:\n`;
            html += `总字段数: ${result.totalFields}\n`;
            html += `成功映射: ${result.mappedFields}\n`;
            html += `跳过字段: ${result.skippedFields}\n`;
            html += `错误字段: ${result.errorFields}\n`;
            html += `成功率: ${successRate}%\n`;
            html += `整体状态: ${result.success ? '✅ 成功' : '❌ 存在问题'}\n`;
            html += `</div>`;

            // 详细信息表格
            html += '<h4>📋 详细字段状态</h4>';
            html += '<table><tr><th>字段名</th><th>值</th><th>选择器</th><th>状态</th></tr>';

            for (const [field, detail] of Object.entries(result.details)) {
                const statusClass = detail.status === 'success' ? 'status-success' :
                                   detail.status === 'skipped' ? 'status-skipped' : 'status-error';
                const statusIcon = detail.status === 'success' ? '✅' :
                                  detail.status === 'skipped' ? '⏭️' : '❌';

                html += `<tr>`;
                html += `<td>${field}</td>`;
                html += `<td>${detail.value || '-'}</td>`;
                html += `<td>${detail.selector || '-'}</td>`;
                html += `<td class="${statusClass}">${statusIcon} ${detail.reason || detail.status}</td>`;
                html += `</tr>`;
            }
            html += '</table>';

            document.getElementById(containerId).innerHTML = html;
        }

        // 显示映射结果
        function displayMappingResults(containerId, result) {
            let html = `<div class="test-result success">`;
            html += `📊 字段映射统计:\n`;
            html += `总字段数: ${result.totalFields}\n`;
            html += `可用字段: ${result.availableCount}\n`;
            html += `缺失字段: ${result.missingCount}\n`;
            html += `可用率: ${((result.availableCount / result.totalFields) * 100).toFixed(1)}%\n`;
            html += `</div>`;

            document.getElementById(containerId).innerHTML = html;
        }

        // 显示错误
        function displayError(containerId, message) {
            const html = `<div class="test-result error">❌ 错误: ${message}</div>`;
            document.getElementById(containerId).innerHTML = html;
        }

        // 导出验证报告
        function exportValidationReport() {
            const report = {
                timestamp: new Date().toISOString(),
                testEnvironment: 'Mock (需要在Chrome扩展环境中进行完整测试)',
                fieldMapping: mockFormMapper.fieldMapping,
                availableFields: mockFormMapper.getAvailableFields(),
                note: '此报告基于模拟环境生成，实际结果可能有所不同'
            };

            const json = JSON.stringify(report, null, 2);
            const blob = new Blob([json], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `field-mapping-validation-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后显示初始状态
        window.addEventListener('load', () => {
            testFieldMappingOnly();
        });
    </script>
</body>
</html>