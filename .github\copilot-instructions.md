# MDAC Script Generator - AI Coding Instructions

## Project Overview
This is a **Malaysian Digital Arrival Card (MDAC) automation tool** that generates JavaScript scripts to auto-fill immigration forms. The project consists of:
- `MDAC_AI_Generator.html` - Single-page web application for script generation
- `MDAC web field.md` - Comprehensive field mapping documentation

## Architecture Pattern: Script Generation Tool

### Core Workflow
1. **AI Input Processing**: Users input traveler info in natural language → Gemini API parses → structured data
2. **Form Mapping**: Structured data → MDAC field mapping → JavaScript auto-fill script
3. **Script Execution**: Generated script runs in browser console on official MDAC website

### Key Components

#### Data Flow Chain
```
Natural Language Input → Gemini AI → JSON Data → Form Mapping → JavaScript Script → MDAC Website
```

#### Critical Field Mappings (lines 1242-1258)
- `name` → `#name` (uppercase, 60 chars max)
- `passNo` → `#passNo` (alphanumeric, 12 chars)
- `region` → `#region` (pure numbers: "60" not "+60")
- `accommodationState` → `#accommodationState` (triggers city cascade)

#### Cascade Dependencies
State selection → City loading (3s delay) → Postcode auto-fill. Handle with:
```javascript
await wait(3000); // City loading wait
// Smart city selection based on address keywords
```

## Development Patterns

### Form Field Architecture
- **Personal Info**: Direct field mapping with validation
- **Travel Info**: Date formatting (DD/MM/YYYY), dropdown selections
- **Accommodation**: Cascading state→city→postcode with async handling

### AI Integration Pattern
```javascript
// Gemini API call structure (lines 1431-1500)
const response = await fetch(GEMINI_API_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }]
    })
});
```

### Script Generation Template (lines 1633-2022)
Generated scripts use:
- `fillField(selector, value, isSelect)` pattern
- Event dispatching for form validation
- Retry logic for dynamic content
- Success/failure tracking

## Critical Implementation Details

### Phone Region Code Format
**IMPORTANT**: MDAC expects pure numbers (`"60"`), not international format (`"+60"`). See phone options (lines 908-1103).

### Date Handling
All dates must be DD/MM/YYYY format. The system auto-converts from various input formats.

### City Selection Intelligence (lines 1941-2022)
Uses keyword matching from address:
```javascript
const cityMapping = {
    'legoland': 'JOHOR BAHRU',
    'kl': 'KUALA LUMPUR',
    // 300+ mappings in documentation
};
```

### Persistent Data Pattern (lines 1305-1330)
Email and phone persist in localStorage for user convenience across sessions.

## Development Workflow

### Adding New Field Mappings
1. Update `fieldMapping` object (line 1341)
2. Add validation in `validateFormData()` (line 1266)
3. Update AI prompt template if needed
4. Test cascade behavior for dependent fields

### Debugging Generated Scripts
Scripts include extensive console logging:
- `✅` for successful fills
- `❌` for failures  
- `📊` for completion statistics

### Testing Strategy
1. Use `fillSampleData()` for quick testing
2. Test on actual MDAC website: https://imigresen-online.imi.gov.my/mdac/main?registerMain
3. Verify cascade timing (state→city loads take ~3 seconds)

## Integration Points

### External Dependencies
- **Gemini API**: `gemini-2.5-flash-lite-preview-06-17` model
- **MDAC Website**: Official Malaysian immigration form
- **Browser Console**: Script execution environment

### Security Considerations
- API key embedded (for demo - consider environment variables)
- Generated scripts run with full page access
- No server-side storage - all client-side processing

## Common Issues & Debugging

### Field Selector Mismatches
**CRITICAL**: Generated scripts often fail due to incorrect field selectors. The most common issues have been identified and fixed:

```javascript
// ❌ Wrong selectors that were causing failures (NOW FIXED):
fillField('#accommodation', value);  // Fixed to: '#accommodationStay'
fillField('#address', value);        // Fixed to: '#accommodationAddress1'  
fillField('#postcode', value);       // Fixed to: '#accommodationPostcode'
fillField('#state', value);          // Fixed to: '#accommodationState'
fillField('#city', value);           // Fixed to: '#accommodationCity'
```

**Root Cause**: The script generator was using simplified field names instead of the full MDAC website field IDs.

**Always verify selectors match the actual MDAC website fields as documented in `MDAC web field.md`:**

| Generator Field | MDAC Website Field | Description |
|---|---|---|
| `#accommodation` | `#accommodationStay` | 住宿类型选择 |
| `#address` | `#accommodationAddress1` | 住宿地址第一行 |
| `#postcode` | `#accommodationPostcode` | 邮政编码 |
| `#state` | `#accommodationState` | 州属选择 |
| `#city` | `#accommodationCity` | 城市选择 |

### City Selection Timeout Issues
City loading frequently times out (5000ms limit). Common error pattern:
```
⏳ 等待城市列表加载...
⚠️ 城市列表加载超时 (5000ms)
❌ 城市选择失败或超时，需要手动选择
```

### Debugging Generated Scripts
Scripts include console logging patterns:
- `✅ 填充 #field: value` - Successful fills
- `⚠️ 字段不存在或值为空: #field` - Missing/empty fields
- `⏸️ 跳过空字段` - Intentionally skipped fields
- `🏛️ 州属将从 "" 更改为 "XX"` - State updates
- `❌ city: 未选择（需要手动选择）` - City selection failed

### Field Mapping Verification Process
Before deploying generated scripts:
1. Inspect actual form field IDs using browser DevTools on MDAC website
2. Test field selectors in browser console: `document.querySelector('#fieldId')`  
3. Compare with field mappings in `MDAC web field.md` documentation
4. Update script generator field mappings if website structure changes

**Recent Fix (Jan 2025)**: Updated all accommodation-related field selectors to match actual MDAC website structure.

## Common Patterns

### Error Handling with Field Validation
```javascript
function fillField(selector, value, isSelect = false) {
    const element = document.querySelector(selector);
    if (!element) {
        console.log(`⚠️ 字段不存在或值为空: ${selector}`);
        return false;
    }
    // Fill logic...
}
```

### Async Wait Pattern
```javascript
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
await wait(3000); // City loading requires longer delays
```

### Field Mapping Verification
Before modifying field mappings, always check current MDAC website structure:
1. Inspect actual form field IDs using browser DevTools
2. Test field selectors in browser console: `document.querySelector('#fieldId')`
3. Update both the generator form and the script template

When modifying this project, always test on the live MDAC website and verify field selectors match the actual DOM structure.
