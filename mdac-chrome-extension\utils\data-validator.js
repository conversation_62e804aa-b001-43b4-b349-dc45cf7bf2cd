// 数据验证工具
class DataValidator {
    constructor() {
        // 必填字段
        this.requiredFields = [
            'name', 'passNo', 'dob', 'nationality', 'sex', 
            'email', 'mobile', 'arrDt', 'depDt', 'accommodationAddress1'
        ];
        
        // 字段验证规则
        this.validationRules = {
            name: {
                required: true,
                pattern: /^[A-Z\s]+$/,
                maxLength: 60,
                message: '姓名必须是大写英文字母，最多60个字符'
            },
            passNo: {
                required: true,
                pattern: /^[A-Z0-9]{6,12}$/,
                message: '护照号码必须是6-12位字母数字组合'
            },
            dob: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '出生日期格式必须为DD/MM/YYYY'
            },
            nationality: {
                required: true,
                pattern: /^[A-Z]{3}$/,
                message: '国籍代码必须是3位大写字母'
            },
            sex: {
                required: true,
                pattern: /^[12]$/,
                message: '性别必须是1(男)或2(女)'
            },
            passExpDte: {
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '护照到期日格式必须为DD/MM/YYYY'
            },
            email: {
                required: true,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '邮箱格式不正确'
            },
            confirmEmail: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '确认邮箱格式不正确'
            },
            region: {
                pattern: /^\d{1,3}$/,
                message: '电话区号必须是1-3位数字'
            },
            mobile: {
                required: true,
                pattern: /^\d{7,15}$/,
                message: '手机号码必须是7-15位数字'
            },
            arrDt: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '到达日期格式必须为DD/MM/YYYY'
            },
            depDt: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '离开日期格式必须为DD/MM/YYYY'
            },
            vesselNm: {
                pattern: /^[A-Z0-9\s]{2,20}$/,
                message: '航班号格式不正确'
            },
            accommodationAddress1: {
                required: true,
                minLength: 5,
                maxLength: 100,
                message: '住宿地址至少5个字符，最多100个字符'
            },
            accommodationPostcode: {
                pattern: /^\d{5}$/,
                message: '邮编必须是5位数字'
            }
        };
    }
    
    // 验证单个字段
    validateField(fieldName, value) {
        const rule = this.validationRules[fieldName];
        if (!rule) {
            return { valid: true };
        }
        
        // 类型检查和转换
        const stringValue = (value === null || value === undefined) ? '' : String(value);
        
        // 检查必填字段
        if (rule.required && (!stringValue || stringValue.trim() === '')) {
            return {
                valid: false,
                error: `${this.getFieldLabel(fieldName)}是必填项`
            };
        }
        
        // 如果值为空且不是必填，跳过其他验证
        if (!stringValue || stringValue.trim() === '') {
            return { valid: true };
        }
        
        const trimmedValue = stringValue.trim();
        
        // 检查正则表达式
        if (rule.pattern && !rule.pattern.test(trimmedValue)) {
            return {
                valid: false,
                error: rule.message
            };
        }
        
        // 检查最小长度
        if (rule.minLength && trimmedValue.length < rule.minLength) {
            return {
                valid: false,
                error: `${this.getFieldLabel(fieldName)}至少需要${rule.minLength}个字符`
            };
        }
        
        // 检查最大长度
        if (rule.maxLength && trimmedValue.length > rule.maxLength) {
            return {
                valid: false,
                error: `${this.getFieldLabel(fieldName)}最多${rule.maxLength}个字符`
            };
        }
        
        return { valid: true };
    }
    
    // 验证所有数据
    validateAllData(data) {
        const errors = [];
        const warnings = [];
        
        // 验证必填字段
        for (const fieldName of this.requiredFields) {
            const value = data[fieldName];
            const stringValue = (value === null || value === undefined) ? '' : String(value);
            if (!stringValue || stringValue.trim() === '') {
                errors.push(`${this.getFieldLabel(fieldName)}是必填项`);
                continue;
            }
            
            const validation = this.validateField(fieldName, value);
            if (!validation.valid) {
                errors.push(validation.error);
            }
        }
        
        // 验证可选字段
        for (const [fieldName, value] of Object.entries(data)) {
            if (!this.requiredFields.includes(fieldName) && value) {
                const validation = this.validateField(fieldName, value);
                if (!validation.valid) {
                    errors.push(validation.error);
                }
            }
        }
        
        // 特殊验证逻辑
        const specialValidation = this.performSpecialValidation(data);
        errors.push(...specialValidation.errors);
        warnings.push(...specialValidation.warnings);
        
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    // 特殊验证逻辑
    performSpecialValidation(data) {
        const errors = [];
        const warnings = [];
        
        // 验证邮箱一致性
        if (data.email && data.confirmEmail && data.email !== data.confirmEmail) {
            errors.push('邮箱和确认邮箱不一致');
        }
        
        // 验证日期逻辑
        if (data.dob && data.passExpDte) {
            const dobDate = this.parseDate(data.dob);
            const expDate = this.parseDate(data.passExpDte);
            if (dobDate && expDate && expDate <= dobDate) {
                errors.push('护照到期日不能早于或等于出生日期');
            }
        }
        
        if (data.arrDt && data.depDt) {
            const arrDate = this.parseDate(data.arrDt);
            const depDate = this.parseDate(data.depDt);
            if (arrDate && depDate && depDate <= arrDate) {
                errors.push('离开日期必须晚于到达日期');
            }
        }
        
        // 验证年龄
        if (data.dob) {
            const age = this.calculateAge(data.dob);
            if (age < 0 || age > 150) {
                errors.push('出生日期不合理');
            } else if (age < 18) {
                warnings.push('未成年人可能需要特殊手续');
            }
        }
        
        // 验证护照有效期
        if (data.passExpDte && data.arrDt) {
            const expDate = this.parseDate(data.passExpDte);
            const arrDate = this.parseDate(data.arrDt);
            if (expDate && arrDate) {
                const monthsUntilExpiry = (expDate.getTime() - arrDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
                if (monthsUntilExpiry < 6) {
                    warnings.push('护照有效期少于6个月，可能影响入境');
                }
            }
        }
        
        // 验证停留时间
        if (data.arrDt && data.depDt) {
            const stayDays = this.calculateStayDays(data.arrDt, data.depDt);
            if (stayDays > 90) {
                warnings.push('停留时间超过90天，可能需要申请签证');
            }
        }
        
        return { errors, warnings };
    }
    
    // 解析日期
    parseDate(dateStr) {
        if (!dateStr || !/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
            return null;
        }
        
        const [day, month, year] = dateStr.split('/').map(Number);
        return new Date(year, month - 1, day);
    }
    
    // 计算年龄
    calculateAge(birthDateStr) {
        const birthDate = this.parseDate(birthDateStr);
        if (!birthDate) return -1;
        
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        return age;
    }
    
    // 计算停留天数
    calculateStayDays(arrivalStr, departureStr) {
        const arrDate = this.parseDate(arrivalStr);
        const depDate = this.parseDate(departureStr);
        
        if (!arrDate || !depDate) return 0;
        
        const timeDiff = depDate.getTime() - arrDate.getTime();
        return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    }
    
    // 获取字段标签
    getFieldLabel(fieldName) {
        const labels = {
            name: '姓名',
            passNo: '护照号码',
            dob: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passExpDte: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            region: '电话区号',
            mobile: '手机号码',
            arrDt: '到达日期',
            depDt: '离开日期',
            vesselNm: '航班号',
            trvlMode: '交通方式',
            embark: '登船港',
            accommodationStay: '住宿类型',
            accommodationAddress1: '住宿地址',
            accommodationAddress2: '地址第二行',
            accommodationState: '州属',
            accommodationCity: '城市',
            accommodationPostcode: '邮编'
        };
        
        return labels[fieldName] || fieldName;
    }
    
    // 清理和格式化数据
    cleanAndFormatData(data) {
        const cleaned = {};
        
        for (const [key, value] of Object.entries(data)) {
            if (value !== null && value !== undefined && value !== '') {
                const stringValue = String(value);
                let cleanedValue = stringValue.trim();
                
                // 特殊格式化
                switch (key) {
                    case 'name':
                        cleanedValue = cleanedValue.toUpperCase();
                        break;
                    case 'passNo':
                        cleanedValue = cleanedValue.toUpperCase().replace(/[^A-Z0-9]/g, '');
                        break;
                    case 'nationality':
                    case 'embark':
                        cleanedValue = cleanedValue.toUpperCase();
                        break;
                    case 'email':
                    case 'confirmEmail':
                        cleanedValue = cleanedValue.toLowerCase();
                        break;
                    case 'mobile':
                        cleanedValue = cleanedValue.replace(/[^\d]/g, '');
                        break;
                    case 'region':
                        cleanedValue = cleanedValue.replace(/[^\d]/g, '');
                        break;
                }
                
                cleaned[key] = cleanedValue;
            }
        }
        
        return cleaned;
    }
    
    // 生成验证报告
    generateValidationReport(data) {
        const validation = this.validateAllData(data);
        
        let report = '📋 数据验证报告\n\n';
        
        if (validation.valid) {
            report += '✅ 所有数据验证通过！\n\n';
        } else {
            report += `❌ 发现 ${validation.errors.length} 个错误：\n`;
            validation.errors.forEach((error, index) => {
                report += `${index + 1}. ${error}\n`;
            });
            report += '\n';
        }
        
        if (validation.warnings.length > 0) {
            report += `⚠️ ${validation.warnings.length} 个警告：\n`;
            validation.warnings.forEach((warning, index) => {
                report += `${index + 1}. ${warning}\n`;
            });
            report += '\n';
        }
        
        // 统计信息
        const filledFields = Object.values(data).filter(v => {
            if (v === null || v === undefined) return false;
            const stringValue = String(v);
            return stringValue.trim() !== '';
        }).length;
        const totalFields = Object.keys(data).length;
        report += `📊 字段完整度: ${filledFields}/${totalFields} (${Math.round(filledFields/totalFields*100)}%)\n`;
        
        return {
            report,
            validation
        };
    }
}

// 创建全局实例
window.dataValidator = new DataValidator();
