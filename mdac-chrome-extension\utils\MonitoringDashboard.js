// 性能监控仪表板 - 实时性能指标收集和展示
// 用途：收集、分析、展示MDAC扩展的性能指标
// 依赖：Chrome Extension APIs, Performance API, 各模块统计接口
// 技术栈：原生JavaScript + 实时数据可视化
// 核心功能：性能指标收集、实时监控、异常告警、趋势分析
// 重要：提供全面的性能洞察，支持数据驱动优化

class MonitoringDashboard {
    constructor() {
        // 性能数据存储
        this.performanceData = {
            apiCalls: [],
            moduleLoads: [],
            memoryUsage: [],
            cacheStats: [],
            userInteractions: [],
            errors: []
        };

        // 监控配置
        this.monitoringConfig = {
            maxDataPoints: 1000,           // 最大数据点数量
            aggregationInterval: 60000,    // 数据聚合间隔（1分钟）
            alertThresholds: {
                apiResponseTime: 8000,     // API响应时间告警阈值（8秒）
                memoryUsage: 50 * 1024 * 1024, // 内存使用告警阈值（50MB）
                errorRate: 0.05,           // 错误率告警阈值（5%）
                moduleLoadTime: 2000       // 模块加载时间告警阈值（2秒）
            },
            retentionPeriod: 7 * 24 * 60 * 60 * 1000 // 数据保留期（7天）
        };

        // 实时监控状态
        this.monitoringStatus = {
            isActive: true,
            startTime: Date.now(),
            dataPoints: 0,
            lastUpdate: null,
            activeAlerts: []
        };

        // 聚合统计
        this.aggregatedStats = {
            hourly: new Map(),
            daily: new Map(),
            overall: {
                totalApiCalls: 0,
                averageApiResponseTime: 0,
                totalErrors: 0,
                averageMemoryUsage: 0,
                cacheHitRate: 0,
                userSatisfaction: 0
            }
        };

        // 仪表板配置
        this.dashboardConfig = {
            updateInterval: 5000,        // 仪表板更新间隔（5秒）
            displayMetrics: [
                'api_performance',
                'memory_usage',
                'cache_efficiency',
                'user_behavior',
                'error_tracking'
            ],
            enableRealTimeUpdates: true,
            enableAlerts: true
        };

        // 告警系统
        this.alertSystem = {
            alerts: [],
            maxAlerts: 100,
            severityLevels: ['info', 'warning', 'error', 'critical'],
            notificationCallbacks: []
        };

        this.init();
    }

    init() {
        console.log('📊 MonitoringDashboard 初始化...');

        try {
            // 启动性能监控
            this.startPerformanceMonitoring();

            // 设置数据收集器
            this.setupDataCollectors();

            // 启动实时更新
            this.startRealTimeUpdates();

            // 设置告警系统
            this.setupAlertSystem();

            // 加载历史数据
            this.loadHistoricalData();

            console.log('✅ MonitoringDashboard 初始化完成');
        } catch (error) {
            console.error('❌ MonitoringDashboard 初始化失败:', error);
        }
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        // API性能监控
        this.monitorAPIPerformance();

        // 内存使用监控
        this.monitorMemoryUsage();

        // 用户交互监控
        this.monitorUserInteractions();

        // 错误监控
        this.monitorErrors();

        console.log('🔍 性能监控已启动');
    }

    /**
     * 监控API性能
     */
    monitorAPIPerformance() {
        // 拦截fetch请求以监控API性能
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            const method = args[1]?.method || 'GET';

            try {
                const response = await originalFetch.apply(window, args);
                const endTime = performance.now();
                const duration = endTime - startTime;

                // 记录API调用性能
                this.recordAPICall({
                    url: url.toString(),
                    method,
                    status: response.status,
                    duration,
                    timestamp: Date.now(),
                    success: response.ok
                });

                return response;
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;

                // 记录失败的API调用
                this.recordAPICall({
                    url: url.toString(),
                    method,
                    status: 0,
                    duration,
                    timestamp: Date.now(),
                    success: false,
                    error: error.message
                });

                throw error;
            }
        };

        console.log('🌐 API性能监控已设置');
    }

    /**
     * 记录API调用
     */
    recordAPICall(apiData) {
        this.performanceData.apiCalls.push(apiData);
        this.monitoringStatus.dataPoints++;
        this.monitoringStatus.lastUpdate = Date.now();

        // 检查告警阈值
        this.checkAPIAlertThresholds(apiData);

        // 限制数据点数量
        if (this.performanceData.apiCalls.length > this.monitoringConfig.maxDataPoints) {
            this.performanceData.apiCalls.shift();
        }

        console.log(`📡 API调用记录: ${apiData.url} - ${apiData.duration.toFixed(2)}ms`);
    }

    /**
     * 检查API告警阈值
     */
    checkAPIAlertThresholds(apiData) {
        const thresholds = this.monitoringConfig.alertThresholds;

        // 响应时间告警
        if (apiData.duration > thresholds.apiResponseTime) {
            this.createAlert('API响应时间过慢', {
                type: 'api_performance',
                severity: 'warning',
                details: {
                    url: apiData.url,
                    duration: apiData.duration,
                    threshold: thresholds.apiResponseTime
                }
            });
        }

        // 错误率告警
        if (!apiData.success) {
            this.createAlert('API调用失败', {
                type: 'api_error',
                severity: 'error',
                details: {
                    url: apiData.url,
                    status: apiData.status,
                    error: apiData.error
                }
            });
        }
    }

    /**
     * 监控内存使用
     */
    monitorMemoryUsage() {
        if (!performance.memory) {
            console.warn('⚠️ 当前环境不支持内存监控API');
            return;
        }

        // 定期内存检查
        this.memoryCheckInterval = setInterval(() => {
            const memoryData = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };

            this.recordMemoryUsage(memoryData);
        }, 10000); // 每10秒检查一次

        console.log('💾 内存使用监控已启动');
    }

    /**
     * 记录内存使用
     */
    recordMemoryUsage(memoryData) {
        this.performanceData.memoryUsage.push(memoryData);

        // 转换为MB
        const usedMemoryMB = memoryData.usedJSHeapSize / (1024 * 1024);

        // 检查内存告警阈值
        this.checkMemoryAlertThresholds(usedMemoryMB);

        // 限制数据点数量
        if (this.performanceData.memoryUsage.length > this.monitoringConfig.maxDataPoints) {
            this.performanceData.memoryUsage.shift();
        }

        console.log(`💾 内存使用: ${usedMemoryMB.toFixed(2)}MB`);
    }

    /**
     * 检查内存告警阈值
     */
    checkMemoryAlertThresholds(usedMemoryMB) {
        const threshold = this.monitoringConfig.alertThresholds.memoryUsage / (1024 * 1024);

        if (usedMemoryMB > threshold) {
            this.createAlert('内存使用过高', {
                type: 'memory_usage',
                severity: 'warning',
                details: {
                    currentUsage: usedMemoryMB,
                    threshold: threshold,
                    percentage: (usedMemoryMB / threshold * 100).toFixed(1)
                }
            });
        }
    }

    /**
     * 监控用户交互
     */
    monitorUserInteractions() {
        // 监听用户交互事件
        const interactionEvents = ['click', 'input', 'change', 'submit'];

        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.recordUserInteraction({
                    type: eventType,
                    target: event.target.tagName,
                    timestamp: Date.now(),
                    coordinates: event.type === 'click' ? { x: event.clientX, y: event.clientY } : null
                });
            }, true);
        });

        console.log('👆 用户交互监控已设置');
    }

    /**
     * 记录用户交互
     */
    recordUserInteraction(interactionData) {
        this.performanceData.userInteractions.push(interactionData);

        // 限制数据点数量
        if (this.performanceData.userInteractions.length > this.monitoringConfig.maxDataPoints) {
            this.performanceData.userInteractions.shift();
        }
    }

    /**
     * 监控错误
     */
    monitorErrors() {
        // 监听JavaScript错误
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: Date.now()
            });
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: Date.now()
            });
        });

        console.log('❌ 错误监控已设置');
    }

    /**
     * 记录错误
     */
    recordError(errorData) {
        this.performanceData.errors.push(errorData);
        this.monitoringStatus.dataPoints++;

        // 检查错误率告警阈值
        this.checkErrorAlertThresholds();

        // 限制数据点数量
        if (this.performanceData.errors.length > this.monitoringConfig.maxDataPoints) {
            this.performanceData.errors.shift();
        }

        console.error('📋 错误记录:', errorData);
    }

    /**
     * 检查错误率告警阈值
     */
    checkErrorAlertThresholds() {
        const recentErrors = this.performanceData.errors.filter(error => {
            return Date.now() - error.timestamp < 60000; // 最近1分钟
        });

        const errorRate = recentErrors.length / 60; // 每分钟错误数
        const threshold = this.monitoringConfig.alertThresholds.errorRate * 60; // 转换为每分钟

        if (errorRate > threshold) {
            this.createAlert('错误率过高', {
                type: 'error_rate',
                severity: 'error',
                details: {
                    currentRate: errorRate,
                    threshold: threshold,
                    recentErrors: recentErrors.length
                }
            });
        }
    }

    /**
     * 设置数据收集器
     */
    setupDataCollectors() {
        // 收集各模块的统计信息
        this.collectModuleStats();

        // 收集缓存统计
        this.collectCacheStats();

        console.log('📊 数据收集器已设置');
    }

    /**
     * 收集模块统计信息
     */
    collectModuleStats() {
        setInterval(() => {
            // ModuleLoader统计
            if (window.ModuleLoader) {
                const moduleStats = window.ModuleLoader.getLoadStats();
                this.recordModuleStats('module_loader', moduleStats);
            }

            // PredictiveCacheManager统计
            if (window.PredictiveCacheManager) {
                const cacheStats = window.PredictiveCacheManager.getCacheStats();
                this.recordCacheStats('predictive_cache', cacheStats);
            }

            // MemoryLeakDetector统计
            if (window.MemoryLeakDetector) {
                const leakStats = window.MemoryLeakDetector.getLeakDetectionStatus();
                this.recordMemoryStats('memory_leak_detector', leakStats);
            }
        }, 30000); // 每30秒收集一次
    }

    /**
     * 记录模块统计
     */
    recordModuleStats(moduleName, stats) {
        const moduleData = {
            module: moduleName,
            stats: stats,
            timestamp: Date.now()
        };

        this.performanceData.moduleLoads.push(moduleData);

        // 检查模块加载时间告警
        if (stats.averageLoadTime > this.monitoringConfig.alertThresholds.moduleLoadTime) {
            this.createAlert('模块加载时间过慢', {
                type: 'module_performance',
                severity: 'warning',
                details: {
                    module: moduleName,
                    loadTime: stats.averageLoadTime,
                    threshold: this.monitoringConfig.alertThresholds.moduleLoadTime
                }
            });
        }
    }

    /**
     * 收集缓存统计
     */
    collectCacheStats() {
        // 从各个缓存系统收集统计信息
        setInterval(() => {
            const cacheStats = {
                timestamp: Date.now(),
                moduleCache: window.ModuleLoader ? window.ModuleLoader.getLoadStats() : null,
                predictiveCache: window.PredictiveCacheManager ? window.PredictiveCacheManager.getCacheStats() : null,
                chromeStorage: null // 需要单独实现
            };

            this.recordCacheStats('system_cache', cacheStats);
        }, 60000); // 每分钟收集一次
    }

    /**
     * 记录缓存统计
     */
    recordCacheStats(cacheName, stats) {
        const cacheData = {
            name: cacheName,
            stats: stats,
            timestamp: Date.now()
        };

        this.performanceData.cacheStats.push(cacheData);

        // 限制数据点数量
        if (this.performanceData.cacheStats.length > this.monitoringConfig.maxDataPoints) {
            this.performanceData.cacheStats.shift();
        }
    }

    /**
     * 记录内存统计
     */
    recordMemoryStats(source, stats) {
        const memoryData = {
            source: source,
            stats: stats,
            timestamp: Date.now()
        };

        // 这里可以专门处理内存统计
        console.log(`💾 内存统计更新: ${source}`, stats);
    }

    /**
     * 启动实时更新
     */
    startRealTimeUpdates() {
        if (!this.dashboardConfig.enableRealTimeUpdates) return;

        this.realTimeInterval = setInterval(() => {
            this.updateDashboard();
            this.aggregateData();
        }, this.dashboardConfig.updateInterval);

        console.log(`🔄 实时更新已启动，间隔: ${this.dashboardConfig.updateInterval}ms`);
    }

    /**
     * 更新仪表板
     */
    updateDashboard() {
        if (!this.isDashboardVisible()) return;

        const currentMetrics = this.generateCurrentMetrics();
        this.renderDashboard(currentMetrics);
    }

    /**
     * 生成当前指标
     */
    generateCurrentMetrics() {
        const now = Date.now();
        const last5Minutes = now - 5 * 60 * 1000;

        return {
            apiPerformance: this.calculateAPIPerformance(last5Minutes),
            memoryUsage: this.calculateMemoryUsage(),
            cacheEfficiency: this.calculateCacheEfficiency(),
            userBehavior: this.calculateUserBehavior(last5Minutes),
            errorTracking: this.calculateErrorTracking(last5Minutes),
            systemHealth: this.calculateSystemHealth(),
            timestamp: now
        };
    }

    /**
     * 计算API性能指标
     */
    calculateAPIPerformance(timeRange) {
        const recentAPICalls = this.performanceData.apiCalls.filter(call => call.timestamp > timeRange);

        if (recentAPICalls.length === 0) {
            return { averageResponseTime: 0, successRate: 0, callCount: 0 };
        }

        const totalCalls = recentAPICalls.length;
        const successfulCalls = recentAPICalls.filter(call => call.success).length;
        const averageResponseTime = recentAPICalls.reduce((sum, call) => sum + call.duration, 0) / totalCalls;

        return {
            averageResponseTime: averageResponseTime,
            successRate: successfulCalls / totalCalls,
            callCount: totalCalls
        };
    }

    /**
     * 计算内存使用指标
     */
    calculateMemoryUsage() {
        if (!performance.memory) {
            return { currentUsage: 0, peakUsage: 0, trend: 'stable' };
        }

        const currentUsage = performance.memory.usedJSHeapSize / (1024 * 1024);
        const recentMemory = this.performanceData.memoryUsage.slice(-10);
        const peakUsage = Math.max(...recentMemory.map(m => m.usedJSHeapSize / (1024 * 1024)));

        // 计算趋势
        const trend = this.calculateMemoryTrend(recentMemory);

        return {
            currentUsage: currentUsage,
            peakUsage: peakUsage,
            trend: trend
        };
    }

    /**
     * 计算内存趋势
     */
    calculateMemoryTrend(memoryData) {
        if (memoryData.length < 3) return 'stable';

        const recent = memoryData.slice(-3);
        const first = recent[0].usedJSHeapSize;
        const last = recent[recent.length - 1].usedJSHeapSize;
        const change = (last - first) / first;

        if (change > 0.1) return 'increasing';
        if (change < -0.1) return 'decreasing';
        return 'stable';
    }

    /**
     * 计算缓存效率指标
     */
    calculateCacheEfficiency() {
        const recentCacheStats = this.performanceData.cacheStats.slice(-5);

        if (recentCacheStats.length === 0) {
            return { hitRate: 0, efficiency: 0 };
        }

        // 简化的缓存效率计算
        let totalHitRate = 0;
        let validStats = 0;

        for (const cacheStat of recentCacheStats) {
            if (cacheStat.stats && cacheStat.stats.cacheHitRate !== undefined) {
                totalHitRate += cacheStat.stats.cacheHitRate;
                validStats++;
            }
        }

        const averageHitRate = validStats > 0 ? totalHitRate / validStats : 0;

        return {
            hitRate: averageHitRate,
            efficiency: Math.min(averageHitRate * 100, 100)
        };
    }

    /**
     * 计算用户行为指标
     */
    calculateUserBehavior(timeRange) {
        const recentInteractions = this.performanceData.userInteractions.filter(
            interaction => interaction.timestamp > timeRange
        );

        const interactionCount = recentInteractions.length;
        const interactionTypes = {};

        recentInteractions.forEach(interaction => {
            interactionTypes[interaction.type] = (interactionTypes[interaction.type] || 0) + 1;
        });

        return {
            interactionCount: interactionCount,
            interactionTypes: interactionTypes,
            activityLevel: this.calculateActivityLevel(interactionCount)
        };
    }

    /**
     * 计算活动级别
     */
    calculateActivityLevel(interactionCount) {
        if (interactionCount > 50) return 'high';
        if (interactionCount > 20) return 'medium';
        return 'low';
    }

    /**
     * 计算错误跟踪指标
     */
    calculateErrorTracking(timeRange) {
        const recentErrors = this.performanceData.errors.filter(
            error => error.timestamp > timeRange
        );

        const errorCount = recentErrors.length;
        const errorTypes = {};

        recentErrors.forEach(error => {
            errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
        });

        return {
            errorCount: errorCount,
            errorTypes: errorTypes,
            errorRate: this.calculateErrorRate(recentErrors.length, timeRange)
        };
    }

    /**
     * 计算错误率
     */
    calculateErrorRate(errorCount, timeRange) {
        const timeSpanMinutes = (Date.now() - timeRange) / (1000 * 60);
        return errorCount / timeSpanMinutes; // 错误数/分钟
    }

    /**
     * 计算系统健康状态
     */
    calculateSystemHealth() {
        const now = Date.now();
        const lastHour = now - 60 * 60 * 1000;

        const apiPerformance = this.calculateAPIPerformance(lastHour);
        const errorTracking = this.calculateErrorTracking(lastHour);
        const memoryUsage = this.calculateMemoryUsage();

        // 综合健康评分（0-100）
        let healthScore = 100;

        // API性能影响
        if (apiPerformance.averageResponseTime > 5000) healthScore -= 30;
        else if (apiPerformance.averageResponseTime > 3000) healthScore -= 20;
        else if (apiPerformance.averageResponseTime > 1000) healthScore -= 10;

        // 错误率影响
        if (errorTracking.errorRate > 0.1) healthScore -= 40;
        else if (errorTracking.errorRate > 0.05) healthScore -= 25;
        else if (errorTracking.errorRate > 0.02) healthScore -= 15;

        // 内存使用影响
        if (memoryUsage.currentUsage > 80) healthScore -= 20;
        else if (memoryUsage.currentUsage > 60) healthScore -= 10;

        // 确保分数在0-100范围内
        healthScore = Math.max(0, Math.min(100, healthScore));

        let healthStatus = 'excellent';
        if (healthScore < 70) healthStatus = 'warning';
        if (healthScore < 50) healthStatus = 'critical';

        return {
            score: Math.round(healthScore),
            status: healthStatus,
            lastCalculated: now
        };
    }

    /**
     * 聚合数据
     */
    aggregateData() {
        const now = Date.now();
        const currentHour = Math.floor(now / (60 * 60 * 1000));
        const currentDay = Math.floor(now / (24 * 60 * 60 * 1000));

        // 聚合小时数据
        this.aggregateHourlyData(currentHour);

        // 聚合日数据
        this.aggregateDailyData(currentDay);

        // 更新总体统计
        this.updateOverallStats();
    }

    /**
     * 聚合小时数据
     */
    aggregateHourlyData(hourKey) {
        const hourStart = hourKey * 60 * 60 * 1000;
        const hourEnd = hourStart + 60 * 60 * 1000;

        const hourData = this.aggregateTimeRangeData(hourStart, hourEnd);
        this.aggregatedStats.hourly.set(hourKey, hourData);

        // 限制历史数据数量
        if (this.aggregatedStats.hourly.size > 168) { // 保留7天的小时数据
            const oldestHour = Math.min(...this.aggregatedStats.hourly.keys());
            this.aggregatedStats.hourly.delete(oldestHour);
        }
    }

    /**
     * 聚合日数据
     */
    aggregateDailyData(dayKey) {
        const dayStart = dayKey * 24 * 60 * 60 * 1000;
        const dayEnd = dayStart + 24 * 60 * 60 * 1000;

        const dayData = this.aggregateTimeRangeData(dayStart, dayEnd);
        this.aggregatedStats.daily.set(dayKey, dayData);

        // 限制历史数据数量
        if (this.aggregatedStats.daily.size > 30) { // 保留30天的日数据
            const oldestDay = Math.min(...this.aggregatedStats.daily.keys());
            this.aggregatedStats.daily.delete(oldestDay);
        }
    }

    /**
     * 聚合时间范围数据
     */
    aggregateTimeRangeData(startTime, endTime) {
        const rangeData = {
            apiCalls: this.performanceData.apiCalls.filter(call => call.timestamp >= startTime && call.timestamp < endTime),
            errors: this.performanceData.errors.filter(error => error.timestamp >= startTime && error.timestamp < endTime),
            memoryUsage: this.performanceData.memoryUsage.filter(memory => memory.timestamp >= startTime && memory.timestamp < endTime),
            userInteractions: this.performanceData.userInteractions.filter(interaction => interaction.timestamp >= startTime && interaction.timestamp < endTime)
        };

        return this.calculateAggregatedMetrics(rangeData);
    }

    /**
     * 计算聚合指标
     */
    calculateAggregatedMetrics(data) {
        const apiMetrics = this.calculateAPIMetrics(data.apiCalls);
        const errorMetrics = this.calculateErrorMetrics(data.errors);
        const memoryMetrics = this.calculateMemoryMetrics(data.memoryUsage);
        const userMetrics = this.calculateUserMetrics(data.userInteractions);

        return {
            timeRange: {
                start: Math.min(...data.apiCalls.map(c => c.timestamp), ...data.errors.map(e => e.timestamp)),
                end: Math.max(...data.apiCalls.map(c => c.timestamp), ...data.errors.map(e => e.timestamp))
            },
            api: apiMetrics,
            errors: errorMetrics,
            memory: memoryMetrics,
            user: userMetrics
        };
    }

    /**
     * 计算API指标
     */
    calculateAPIMetrics(apiCalls) {
        if (apiCalls.length === 0) {
            return { total: 0, successRate: 0, averageResponseTime: 0 };
        }

        const total = apiCalls.length;
        const successful = apiCalls.filter(call => call.success).length;
        const averageResponseTime = apiCalls.reduce((sum, call) => sum + call.duration, 0) / total;

        return {
            total,
            successRate: successful / total,
            averageResponseTime
        };
    }

    /**
     * 计算错误指标
     */
    calculateErrorMetrics(errors) {
        if (errors.length === 0) {
            return { total: 0, rate: 0, types: {} };
        }

        const total = errors.length;
        const types = {};

        errors.forEach(error => {
            types[error.type] = (types[error.type] || 0) + 1;
        });

        return {
            total,
            rate: total / (60 * 60 * 1000), // 错误数/小时
            types
        };
    }

    /**
     * 计算内存指标
     */
    calculateMemoryMetrics(memoryUsage) {
        if (memoryUsage.length === 0) {
            return { average: 0, peak: 0, trend: 'stable' };
        }

        const usageValues = memoryUsage.map(m => m.usedJSHeapSize / (1024 * 1024));
        const average = usageValues.reduce((sum, val) => sum + val, 0) / usageValues.length;
        const peak = Math.max(...usageValues);

        // 简化趋势计算
        const trend = this.calculateMemoryTrend(memoryUsage);

        return {
            average,
            peak,
            trend
        };
    }

    /**
     * 计算用户指标
     */
    calculateUserMetrics(userInteractions) {
        if (userInteractions.length === 0) {
            return { total: 0, activityLevel: 'low', interactionTypes: {} };
        }

        const total = userInteractions.length;
        const types = {};

        userInteractions.forEach(interaction => {
            types[interaction.type] = (types[interaction.type] || 0) + 1;
        });

        const activityLevel = this.calculateActivityLevel(total);

        return {
            total,
            activityLevel,
            interactionTypes: types
        };
    }

    /**
     * 更新总体统计
     */
    updateOverallStats() {
        const allDays = Array.from(this.aggregatedStats.daily.values());

        if (allDays.length === 0) return;

        // 计算总体API统计
        const totalApiCalls = allDays.reduce((sum, day) => sum + day.api.total, 0);
        const totalSuccessfulApiCalls = allDays.reduce((sum, day) => sum + (day.api.total * day.api.successRate), 0);
        const averageApiResponseTime = allDays.reduce((sum, day) => sum + day.api.averageResponseTime, 0) / allDays.length;

        // 计算总体错误统计
        const totalErrors = allDays.reduce((sum, day) => sum + day.errors.total, 0);

        // 计算总体内存统计
        const averageMemoryUsage = allDays.reduce((sum, day) => sum + day.memory.average, 0) / allDays.length;

        // 计算缓存命中率
        const cacheHitRate = this.calculateOverallCacheHitRate();

        this.aggregatedStats.overall = {
            totalApiCalls,
            averageApiResponseTime,
            totalErrors,
            averageMemoryUsage,
            cacheHitRate,
            successRate: totalApiCalls > 0 ? totalSuccessfulApiCalls / totalApiCalls : 0
        };
    }

    /**
     * 计算总体缓存命中率
     */
    calculateOverallCacheHitRate() {
        // 简化实现
        return 0.85; // 默认值，可以根据实际数据计算
    }

    /**
     * 设置告警系统
     */
    setupAlertSystem() {
        if (!this.dashboardConfig.enableAlerts) return;

        // 设置告警检查
        this.alertCheckInterval = setInterval(() => {
            this.processAlerts();
        }, 30000); // 每30秒处理一次告警

        console.log('🚨 告警系统已设置');
    }

    /**
     * 创建告警
     */
    createAlert(message, alertData) {
        const alert = {
            id: Date.now() + Math.random(),
            message,
            type: alertData.type,
            severity: alertData.severity,
            details: alertData.details,
            timestamp: Date.now(),
            acknowledged: false
        };

        this.alertSystem.alerts.push(alert);

        // 限制告警数量
        if (this.alertSystem.alerts.length > this.alertSystem.maxAlerts) {
            this.alertSystem.alerts.shift();
        }

        console.log(`🚨 告警创建 [${alertData.severity}]: ${message}`);

        // 触发告警事件
        const event = new CustomEvent('performanceAlert', { detail: alert });
        document.dispatchEvent(event);
    }

    /**
     * 处理告警
     */
    processAlerts() {
        const unacknowledgedAlerts = this.alertSystem.alerts.filter(alert => !alert.acknowledged);

        unacknowledgedAlerts.forEach(alert => {
            // 根据告警类型和严重程度处理
            this.handleAlert(alert);
        });
    }

    /**
     * 处理单个告警
     */
    handleAlert(alert) {
        switch (alert.type) {
            case 'api_performance':
                this.handleAPIPerformanceAlert(alert);
                break;
            case 'memory_usage':
                this.handleMemoryUsageAlert(alert);
                break;
            case 'error_rate':
                this.handleErrorRateAlert(alert);
                break;
            default:
                console.log(`📋 处理告警: ${alert.message}`);
        }
    }

    /**
     * 处理API性能告警
     */
    handleAPIPerformanceAlert(alert) {
        console.log(`⚡ API性能告警处理: ${alert.details.duration}ms`);
        // 可以触发自动优化措施
    }

    /**
     * 处理内存使用告警
     */
    handleMemoryUsageAlert(alert) {
        console.log(`💾 内存使用告警处理: ${alert.details.currentUsage}MB`);
        // 可以触发内存清理
        if (window.MemoryLeakDetector) {
            window.MemoryLeakDetector.performAutomaticCleanup(alert.details.currentUsage);
        }
    }

    /**
     * 处理错误率告警
     */
    handleErrorRateAlert(alert) {
        console.log(`❌ 错误率告警处理: ${alert.details.currentRate} errors/min`);
        // 可以触发错误日志分析
    }

    /**
     * 加载历史数据
     */
    async loadHistoricalData() {
        try {
            const result = await chrome.storage.local.get(['mdac_performance_data']);
            const historicalData = result.mdac_performance_data;

            if (historicalData) {
                // 恢复历史数据（只恢复最近7天的数据）
                const cutoffTime = Date.now() - this.monitoringConfig.retentionPeriod;

                for (const [key, data] of Object.entries(historicalData)) {
                    if (this.performanceData[key]) {
                        const filteredData = data.filter(item => item.timestamp > cutoffTime);
                        this.performanceData[key] = filteredData;
                    }
                }

                console.log(`📈 历史数据已加载 (${Object.keys(historicalData).length} 个数据类型)`);
            }
        } catch (error) {
            console.error('❌ 历史数据加载失败:', error);
        }
    }

    /**
     * 保存数据
     */
    async saveData() {
        try {
            const dataToSave = {
                apiCalls: this.performanceData.apiCalls,
                errors: this.performanceData.errors,
                memoryUsage: this.performanceData.memoryUsage,
                userInteractions: this.performanceData.userInteractions,
                cacheStats: this.performanceData.cacheStats,
                moduleLoads: this.performanceData.moduleLoads,
                savedAt: Date.now()
            };

            await chrome.storage.local.set({
                'mdac_performance_data': dataToSave
            });

            console.log('💾 性能数据已保存');
        } catch (error) {
            console.error('❌ 性能数据保存失败:', error);
        }
    }

    /**
     * 渲染仪表板
     */
    renderDashboard(metrics) {
        // 这里可以实现实际的仪表板渲染逻辑
        // 由于是在扩展环境中，可以创建DOM元素或发送消息给popup页面

        console.log('📊 仪表板更新:', {
            systemHealth: metrics.systemHealth,
            apiPerformance: metrics.apiPerformance,
            memoryUsage: metrics.memoryUsage,
            cacheEfficiency: metrics.cacheEfficiency
        });

        // 触发仪表板更新事件
        const event = new CustomEvent('dashboardUpdate', { detail: metrics });
        document.dispatchEvent(event);
    }

    /**
     * 检查仪表板是否可见
     */
    isDashboardVisible() {
        // 在扩展环境中，需要检查popup是否打开
        // 这里简化处理，始终返回true
        return true;
    }

    /**
     * 获取当前性能指标
     */
    getCurrentMetrics() {
        return this.generateCurrentMetrics();
    }

    /**
     * 获取历史趋势
     */
    getHistoricalTrends(timeRange = 24 * 60 * 60 * 1000) {
        const now = Date.now();
        const startTime = now - timeRange;

        const trends = {
            apiPerformance: this.getAPITrends(startTime, now),
            memoryUsage: this.getMemoryTrends(startTime, now),
            errorRate: this.getErrorTrends(startTime, now),
            userActivity: this.getUserActivityTrends(startTime, now)
        };

        return trends;
    }

    /**
     * 获取API趋势
     */
    getAPITrends(startTime, endTime) {
        const relevantData = this.performanceData.apiCalls.filter(call =>
            call.timestamp >= startTime && call.timestamp <= endTime
        );

        return {
            responseTime: relevantData.map(call => ({ x: call.timestamp, y: call.duration })),
            successRate: relevantData.map(call => ({ x: call.timestamp, y: call.success ? 1 : 0 })),
            callVolume: this.aggregateByTime(relevantData, 'timestamp', 300000) // 5分钟聚合
        };
    }

    /**
     * 获取内存趋势
     */
    getMemoryTrends(startTime, endTime) {
        const relevantData = this.performanceData.memoryUsage.filter(memory =>
            memory.timestamp >= startTime && memory.timestamp <= endTime
        );

        return {
            usage: relevantData.map(memory => ({
                x: memory.timestamp,
                y: memory.usedJSHeapSize / (1024 * 1024)
            })),
            peakUsage: Math.max(...relevantData.map(m => m.usedJSHeapSize / (1024 * 1024)))
        };
    }

    /**
     * 获取错误趋势
     */
    getErrorTrends(startTime, endTime) {
        const relevantData = this.performanceData.errors.filter(error =>
            error.timestamp >= startTime && error.timestamp <= endTime
        );

        return {
            volume: this.aggregateByTime(relevantData, 'timestamp', 300000), // 5分钟聚合
            types: this.aggregateByField(relevantData, 'type'),
            rate: relevantData.length / ((endTime - startTime) / (60 * 1000)) // 错误数/分钟
        };
    }

    /**
     * 获取用户活动趋势
     */
    getUserActivityTrends(startTime, endTime) {
        const relevantData = this.performanceData.userInteractions.filter(interaction =>
            interaction.timestamp >= startTime && interaction.timestamp <= endTime
        );

        return {
            activityVolume: this.aggregateByTime(relevantData, 'timestamp', 300000), // 5分钟聚合
            interactionTypes: this.aggregateByField(relevantData, 'type')
        };
    }

    /**
     * 按时间聚合数据
     */
    aggregateByTime(data, timeField, interval) {
        const aggregated = {};

        data.forEach(item => {
            const timeKey = Math.floor(item[timeField] / interval) * interval;
            aggregated[timeKey] = (aggregated[timeKey] || 0) + 1;
        });

        return Object.entries(aggregated).map(([time, count]) => ({
            x: parseInt(time),
            y: count
        }));
    }

    /**
     * 按字段聚合数据
     */
    aggregateByField(data, field) {
        const aggregated = {};

        data.forEach(item => {
            const fieldValue = item[field];
            aggregated[fieldValue] = (aggregated[fieldValue] || 0) + 1;
        });

        return aggregated;
    }

    /**
     * 设置告警系统
     */
    setupAlertSystem() {
        // 已在初始化中设置
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport(timeRange = 24 * 60 * 60 * 1000) {
        const trends = this.getHistoricalTrends(timeRange);
        const currentMetrics = this.getCurrentMetrics();
        const overallStats = this.aggregatedStats.overall;

        return {
            generatedAt: Date.now(),
            timeRange: timeRange,
            summary: {
                systemHealth: currentMetrics.systemHealth,
                averageApiResponseTime: overallStats.averageApiResponseTime,
                errorRate: overallStats.totalErrors / Math.max(overallStats.totalApiCalls, 1),
                averageMemoryUsage: overallStats.averageMemoryUsage,
                cacheHitRate: overallStats.cacheHitRate
            },
            trends: trends,
            recommendations: this.generateRecommendations(currentMetrics, trends),
            alerts: this.getRecentAlerts()
        };
    }

    /**
     * 生成优化建议
     */
    generateRecommendations(currentMetrics, trends) {
        const recommendations = [];

        // API性能建议
        if (currentMetrics.apiPerformance.averageResponseTime > 3000) {
            recommendations.push({
                type: 'api_performance',
                priority: 'high',
                message: 'API响应时间较长，建议优化网络请求或增加缓存策略',
                actionable: true
            });
        }

        // 内存使用建议
        if (currentMetrics.memoryUsage.currentUsage > 60) {
            recommendations.push({
                type: 'memory_usage',
                priority: 'medium',
                message: '内存使用较高，建议检查资源释放情况',
                actionable: true
            });
        }

        // 错误率建议
        if (currentMetrics.errorTracking.errorRate > 0.05) {
            recommendations.push({
                type: 'error_rate',
                priority: 'high',
                message: '错误率较高，建议检查错误日志和异常处理',
                actionable: true
            });
        }

        return recommendations;
    }

    /**
     * 获取最近的告警
     */
    getRecentAlerts() {
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        return this.alertSystem.alerts.filter(alert => alert.timestamp > oneHourAgo);
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            monitoring: this.monitoringStatus,
            currentMetrics: this.getCurrentMetrics(),
            alerts: {
                active: this.alertSystem.alerts.filter(a => !a.acknowledged).length,
                total: this.alertSystem.alerts.length
            },
            performance: this.aggregatedStats.overall
        };
    }

    /**
     * 数据聚合和清理
     */
    aggregateData() {
        // 已在实时更新中实现
    }

    /**
     * 清理过期数据
     */
    cleanupExpiredData() {
        const cutoffTime = Date.now() - this.monitoringConfig.retentionPeriod;

        // 清理各类型过期数据
        for (const [key, data] of Object.entries(this.performanceData)) {
            if (Array.isArray(data)) {
                this.performanceData[key] = data.filter(item => item.timestamp > cutoffTime);
            }
        }

        // 清理聚合数据
        for (const [hourKey, hourData] of this.aggregatedStats.hourly.entries()) {
            if (hourData.timeRange.end < cutoffTime) {
                this.aggregatedStats.hourly.delete(hourKey);
            }
        }

        for (const [dayKey, dayData] of this.aggregatedStats.daily.entries()) {
            if (dayData.timeRange.end < cutoffTime) {
                this.aggregatedStats.daily.delete(dayKey);
            }
        }

        console.log('🧹 过期性能数据已清理');
    }

    /**
     * 启动定时保存
     */
    startAutoSave() {
        // 每5分钟保存一次数据
        setInterval(() => {
            this.saveData();
        }, 5 * 60 * 1000);

        // 页面卸载时保存数据
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });

        console.log('💾 自动保存已启动');
    }

    /**
     * 内存清理
     */
    cleanup() {
        // 停止监控
        if (this.memoryCheckInterval) {
            clearInterval(this.memoryCheckInterval);
        }

        if (this.realTimeInterval) {
            clearInterval(this.realTimeInterval);
        }

        if (this.alertCheckInterval) {
            clearInterval(this.alertCheckInterval);
        }

        // 保存当前数据
        this.saveData();

        // 清理过期数据
        this.cleanupExpiredData();

        console.log('🧹 MonitoringDashboard 清理完成');
    }
}

/**
 * 全局性能监控仪表板实例
 */
window.MonitoringDashboard = new MonitoringDashboard();

// 便捷的监控函数
window.getPerformanceMetrics = function() {
    return window.MonitoringDashboard.getCurrentMetrics();
};

window.getPerformanceReport = function(timeRange) {
    return window.MonitoringDashboard.getPerformanceReport(timeRange);
};

window.getSystemStatus = function() {
    return window.MonitoringDashboard.getSystemStatus();
};

console.log('✅ MonitoringDashboard 系统已初始化');