<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC表单清除测试</title>
</head>
<body>
    <h1>MDAC表单清除测试</h1>
    <p>这个页面用于测试表单清除功能。</p>

    <div style="padding: 20px; border: 1px solid #ccc; margin: 20px;">
        <h3>模拟MDAC表单字段</h3>
        <p>请在MDAC官网页面打开扩展侧边栏，然后点击清空按钮测试功能。</p>

        <div style="margin: 10px 0;">
            <label>姓名: </label>
            <input type="text" id="name" placeholder="测试姓名" value="测试姓名">
        </div>

        <div style="margin: 10px 0;">
            <label>护照号: </label>
            <input type="text" id="passNo" placeholder="测试护照号" value="TEST123456">
        </div>

        <div style="margin: 10px 0;">
            <label>邮箱: </label>
            <input type="email" id="email" placeholder="测试邮箱" value="<EMAIL>">
        </div>

        <div style="margin: 10px 0;">
            <label>手机: </label>
            <input type="text" id="mobile" placeholder="测试手机" value="123456789">
        </div>

        <div style="margin: 10px 0;">
            <label>到达日期: </label>
            <input type="date" id="arrDt" value="2025-08-01">
        </div>
    </div>

    <div style="margin: 20px; padding: 10px; background-color: #f0f0f0;">
        <h4>测试步骤：</h4>
        <ol>
            <li>打开 <a href="https://imigresen-online.imi.gov.my/mdac/main?registerMain" target="_blank">MDAC官网</a></li>
            <li>在MDAC页面点击扩展图标打开侧边栏</li>
            <li>在MDAC页面填写一些表单字段</li>
            <li>在侧边栏中点击"🗑️ 清空"按钮</li>
            <li>观察MDAC页面的表单字段是否被清除（锁定的字段不会被清除）</li>
            <li>查看浏览器开发者工具的控制台日志</li>
        </ol>
    </div>

    <div id="debugInfo" style="margin: 20px; padding: 10px; background-color: #e8f4fd; font-family: monospace;">
        <h4>调试信息：</h4>
        <div id="debugContent">等待调试信息...</div>
    </div>

    <script>
        // 监听来自content script的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'debug') {
                const debugContent = document.getElementById('debugContent');
                debugContent.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + event.data.message;
            }
        });

        // 模拟MDAC页面的一些基本功能
        console.log('测试页面已加载');
    </script>
</body>
</html>