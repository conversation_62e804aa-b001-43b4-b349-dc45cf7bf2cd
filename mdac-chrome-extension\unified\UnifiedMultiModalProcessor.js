// 统一多模态处理器 - 核心处理器
// 用途：统一处理文字、图片、文件输入，一次性发送给豆包API
// 依赖：ImageProcessor.js, FileProcessor.js, AdvancedImageProcessor.js, performance-monitor.js（可选）
// 技术栈：火山引擎豆包模型 doubao-seed-1-6-flash-250828 (OpenAI兼容)
// 核心功能：多模态输入统一、并行处理、一次性API调用、结果解析、实时性能监控
// 重要：集成所有性能优化模块，提供统一的性能指标收集

class UnifiedMultiModalProcessor {
  static _instance = null;
  static getInstance(config = {}) {
    if (!UnifiedMultiModalProcessor._instance) {
      UnifiedMultiModalProcessor._instance = new UnifiedMultiModalProcessor(config);
    }
    return UnifiedMultiModalProcessor._instance;
  }

  constructor(config = {}) {
    // 配置管理 - 支持外部配置注入
    this.config = {
      // 默认配置（内部使用）
      apiKey: config.apiKey || '47a1d437-af1e-4833-abc9-82a97235e236',
      model: config.model || 'doubao-seed-1-6-flash-250828',
      apiUrl: config.apiUrl || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      // API参数配置
      temperature: config.temperature || 0.3,
      top_p: config.top_p || 0.2,
      max_tokens: config.max_tokens || 16000,
      stream: config.stream || false,
      // 重试配置
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      // 性能配置
      enableMonitoring: config.enableMonitoring !== false,
      enableCompressionStats: config.enableCompressionStats !== false,
      enableRealTimeMetrics: config.enableRealTimeMetrics !== false,
      // 缓存配置
      enableCache: config.enableCache !== false,
      cacheTTL: config.cacheTTL || 5 * 60 * 1000, // 5分钟默认缓存时间
      // 高级处理配置
      enableAdvancedImageProcessing: config.enableAdvancedImageProcessing !== false,
      enableMemoryOptimization: config.enableMemoryOptimization !== false,
      enableProgressiveProcessing: config.enableProgressiveProcessing !== false
    };

    // 初始化缓存
    this.requestCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      total: 0
    };

    // 性能统计
    this.performanceMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      averageProcessingTime: 0,
      totalTokensUsed: 0,
      compressionRatio: 0,
      cacheHitRate: 0,
      peakMemoryUsage: 0,
      startTime: Date.now()
    };

    // 实时监控数据
    this.realTimeMetrics = {
      currentRequests: 0,
      queueLength: 0,
      memoryUsage: 0,
      processingRate: 0,
      errorRate: 0,
      lastUpdate: Date.now()
    };

    // 初始化子模块
    this.imageProcessor = null;
    this.fileProcessor = null;
    this.advancedImageProcessor = null;
    this.performanceMonitor = null;

    this.initSubModules();
  }

  // 初始化子模块 - 增强版本，支持高级图片处理
  initSubModules() {
    try {
      console.log('🔧 初始化统一多模态处理器子模块...');

      // 基础图片处理器
      if (window.ImageProcessor) {
        this.imageProcessor = new ImageProcessor(this.config);
        console.log('✅ 基础图片处理器初始化完成');
      } else {
        console.warn('⚠️ ImageProcessor 不可用');
      }

      // 文件处理器
      if (window.FileProcessor) {
        this.fileProcessor = new FileProcessor(this.config);
        console.log('✅ 文件处理器初始化完成');
      } else {
        console.warn('⚠️ FileProcessor 不可用');
      }

      // 高级图片处理器
      if (window.AdvancedImageProcessor && this.config.enableAdvancedImageProcessing) {
        this.advancedImageProcessor = new AdvancedImageProcessor({
          optimizeForOCR: true,
          documentMode: 'auto',
          maxSize: 1200,
          quality: 0.85,
          enableEnhancement: true
        });
        console.log('✅ 高级图片处理器初始化完成');
      } else {
        console.log('ℹ️ 高级图片处理器未启用或不可用');
      }

      // 性能监控器
      if (this.config.enableMonitoring) {
        this.performanceMonitor = this.getPerformanceMonitor();
        if (this.performanceMonitor) {
          console.log('✅ 性能监控器已连接');
        }
      }

      // 启动实时指标收集
      if (this.config.enableRealTimeMetrics) {
        this.startRealTimeMetricsCollection();
      }

      // 启动内存监控
      if (this.config.enableMemoryOptimization) {
        this.startMemoryMonitoring();
      }

      console.log('✅ 统一多模态处理器子模块初始化完成');

    } catch (error) {
      console.error('❌ 子模块初始化失败:', error);
    }
  }

  // 获取性能监控器
  getPerformanceMonitor() {
    if (!this.performanceMonitor && window.performanceMonitor) {
      this.performanceMonitor = window.performanceMonitor;
    }
    if (!this.performanceMonitor && window.MonitoringDashboard) {
      this.performanceMonitor = window.MonitoringDashboard;
    }
    return this.performanceMonitor;
  }

  // 启动实时指标收集
  startRealTimeMetricsCollection() {
    console.log('📊 启动实时性能指标收集...');

    this.metricsInterval = setInterval(() => {
      this.collectRealTimeMetrics();
    }, 5000); // 每5秒收集一次

    console.log('✅ 实时性能指标收集已启动');
  }

  // 启动内存监控
  startMemoryMonitoring() {
    if (!performance.memory) {
      console.warn('⚠️ 当前环境不支持内存监控API');
      return;
    }

    this.memoryInterval = setInterval(() => {
      const currentMemory = performance.memory.usedJSHeapSize / (1024 * 1024);
      this.realTimeMetrics.memoryUsage = currentMemory;

      if (currentMemory > this.performanceMetrics.peakMemoryUsage) {
        this.performanceMetrics.peakMemoryUsage = currentMemory;
      }

      // 内存警告
      if (currentMemory > 100) { // 超过100MB
        console.warn(`🚨 内存使用警告: ${currentMemory.toFixed(2)}MB`);
      }
    }, 10000); // 每10秒检查一次

    console.log('💾 内存监控已启动');
  }

  // 收集实时指标
  collectRealTimeMetrics() {
    try {
      const now = Date.now();
      const timeSinceStart = now - this.performanceMetrics.startTime;

      // 计算处理速率（文件/分钟）
      const processingRate = timeSinceStart > 0
        ? (this.performanceMetrics.totalRequests / timeSinceStart) * 60000
        : 0;

      // 计算错误率
      const errorRate = this.performanceMetrics.totalRequests > 0
        ? (this.performanceMetrics.failedRequests / this.performanceMetrics.totalRequests) * 100
        : 0;

      // 更新实时指标
      this.realTimeMetrics = {
        currentRequests: this.realTimeMetrics.currentRequests,
        queueLength: this.realTimeMetrics.queueLength,
        memoryUsage: this.realTimeMetrics.memoryUsage,
        processingRate: Math.round(processingRate * 100) / 100,
        errorRate: Math.round(errorRate * 100) / 100,
        lastUpdate: now
      };

      // 发送到监控系统
      if (this.performanceMonitor && this.performanceMonitor.recordAPICall) {
        this.performanceMonitor.recordAPICall({
          url: 'unified-processor-metrics',
          method: 'METRICS',
          status: 200,
          duration: 0,
          timestamp: now,
          success: true,
          metrics: this.realTimeMetrics
        });
      }

      // 触发指标更新事件
      const event = new CustomEvent('unifiedProcessorMetrics', {
        detail: this.realTimeMetrics
      });
      document.dispatchEvent(event);

    } catch (error) {
      console.error('实时指标收集失败:', error);
    }
  }

  // 统一多模态输入处理入口（增强版本 - 集成实时性能指标）
  async processMultiModalInput(inputs) {
    const { textInput, images, files, systemPrompt, sessionId } = inputs;
    const monitor = this.getPerformanceMonitor();
    const stepName = '多模态处理';
    const requestId = sessionId || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const overallStart = performance.now(); // 修复：定义总体开始时间

    // 更新实时指标
    this.realTimeMetrics.currentRequests++;
    this.performanceMetrics.totalRequests++;

    monitor?.startStep(requestId, stepName, '统一处理所有输入');

    try {
      // 第一阶段：并行处理所有输入
      const processingStart = performance.now();
      const [processedImages, processedFiles] = await Promise.all([
        this.processImages(images || [], sessionId),
        this.processFiles(files || [], sessionId)
      ]);

      monitor?.recordSubStep(requestId, stepName, '输入处理',
        Math.round(performance.now() - processingStart),
        `图片:${processedImages.length}, 文件:${processedFiles.processedFiles?.length || 0}`
      );

      // 更新处理统计 (已注释 - 避免调用不存在的方法)
      // this.updateProcessingStats({
      //   processingTime: Math.round(performance.now() - processingStart),
      //   imagesProcessed: processedImages.length,
      //   filesProcessed: processedFiles.processedFiles?.length || 0
      // });

      // 第二阶段：构建统一消息体
      const buildStart = performance.now();
      const messageContent = this.buildUnifiedContent({
        textInput,
        processedImages,
        processedFiles,
        systemPrompt
      });

      monitor?.recordSubStep(requestId, stepName, '消息构建',
        Math.round(performance.now() - buildStart),
        `内容块:${messageContent.length}`
      );

      // 第三阶段：一次性API调用
      const apiResult = await this.callDoubaoAPI({
        messages: [{ role: 'user', content: messageContent }]
      }, sessionId);

      // 第四阶段：解析和格式化结果
      const parseStart = performance.now();
      const finalResult = this.parseAPIResponse(apiResult, {
        imageStats: this.imageProcessor.getCompressionStats(processedImages),
        fileStats: this.fileProcessor.getProcessingStats(processedFiles.processedFiles),
        inputSummary: {
          textLength: textInput?.length || 0,
          imageCount: processedImages.length,
          fileCount: processedFiles.processedFiles?.length || 0
        }
      });

      monitor?.recordSubStep(requestId, stepName, '结果解析',
        Math.round(performance.now() - parseStart)
      );

      monitor?.endStep(requestId, stepName, {
        success: true,
        totalInputs: (images?.length || 0) + (files?.length || 0) + (textInput ? 1 : 0)
      });

      return finalResult;

    } catch (error) {
      monitor?.endStep(requestId, stepName, {
        success: false,
        error: error.message
      });

      // 更新失败统计
      this.performanceMetrics.failedRequests++;
      this.realTimeMetrics.currentRequests--;

      throw error;
    } finally {
      // 更新实时指标
      this.realTimeMetrics.currentRequests--;
      const totalTime = Math.round(performance.now() - overallStart);

      // 更新性能统计 (已注释 - 避免调用不存在的方法)
      // this.updatePerformanceMetrics(totalTime, true);

      // 发送最终指标 (已注释 - 避免调用不存在的方法)
      // this.sendFinalMetrics(requestId, totalTime);
    }
  }

  // 处理图片输入（增强版本 - 支持高级图片处理）
  async processImages(images, sessionId) {
    if (!images || images.length === 0) return [];

    const monitor = this.getPerformanceMonitor();
    monitor?.recordSubStep(sessionId || 'multimodal', '图片处理', '开始压缩', 0, `${images.length}张图片`);

    // 使用高级图片处理器（如果可用且启用）
    if (this.advancedImageProcessor && this.config.enableAdvancedImageProcessing) {
      try {
        console.log('🖼️ 使用高级图片处理器处理图片...');
        const results = await this.advancedImageProcessor.processImagesAdvanced(images);

        // 更新压缩统计 (已注释 - 避免调用不存在的方法)
        // this.updateCompressionStats(results);

        return results;
      } catch (error) {
        console.warn('高级图片处理失败，回退到基础处理器:', error);
      }
    }

    // 回退到基础图片处理器
    return await this.imageProcessor.processImages(images);
  }

  // 处理文件输入（增强版本 - 支持智能批处理）
  async processFiles(files, sessionId) {
    if (!files || files.length === 0) {
      return { extractedText: '', convertedImages: [], processedFiles: [] };
    }

    const monitor = this.getPerformanceMonitor();
    monitor?.recordSubStep(sessionId || 'multimodal', '文件处理', '开始转换', 0, `${files.length}个文件`);

    // 使用智能批处理（如果可用）
    if (window.FileProcessorInstance && this.config.enableProgressiveProcessing) {
      try {
        console.log('📁 使用智能文件批处理器...');
        const results = await window.FileProcessorInstance.processFiles(files, {
          onProgress: (progress) => {
            console.log(`📊 文件处理进度: ${progress.processed}/${progress.total}, 当前: ${progress.currentFile}`);
          },
          memoryAware: this.config.enableMemoryOptimization,
          enableCache: this.config.enableCache
        });

        return results;
      } catch (error) {
        console.warn('智能文件处理失败，回退到基础处理器:', error);
      }
    }

    // 回退到基础文件处理器
    return await this.fileProcessor.processFiles(files);
  }

  // 构建统一消息内容
  buildUnifiedContent({ textInput, processedImages, processedFiles, systemPrompt }) {
    const content = [];

    // 1. 添加系统提示词和用户文字输入
    const combinedText = this.buildCombinedTextPrompt({
      systemPrompt,
      userInput: textInput,
      extractedText: processedFiles.extractedText
    });

    content.push({
      type: 'text',
      text: combinedText
    });

    // 2. 添加处理后的图片（原始图片 + 文件转换的图片）
    const allImages = [...processedImages];
    if (processedFiles.convertedImages && processedFiles.convertedImages.length > 0) {
      allImages.push(...processedFiles.convertedImages);
    }

    allImages.forEach((img, index) => {
      content.push({
        type: 'image_url',
        image_url: {
          url: `data:${img.mimeType};base64,${img.base64}`
        }
      });
    });

    return content;
  }

  // 构建综合文本提示词（简化版本 - 减少思考时间）
  buildCombinedTextPrompt({ systemPrompt, userInput, extractedText }) {
    let prompt = '';

    // 系统提示词（如果有）
    if (systemPrompt) {
      prompt += `${systemPrompt}\n\n`;
    }

    // 简化的MDAC提取提示词
    prompt += this.getMDACExtractionPrompt() + '\n\n';

    // 用户输入文字（精简格式）
    if (userInput && userInput.trim()) {
      prompt += `输入文本：${userInput.trim()}\n\n`;
    }

    // 文件提取的文本内容（精简格式）
    if (extractedText && extractedText.trim()) {
      prompt += `文档内容：${extractedText.trim()}\n\n`;
    }

    // 简化的处理指令
    prompt += `提取上述信息中的MDAC表单字段，返回JSON格式。只返回JSON数据。`;

    return prompt;
  }

  // 获取MDAC字段提取提示词（通用识别版本 - 从任何文档中提取个人信息）
  getMDACExtractionPrompt() {
    return `你是智能信息提取助手，能够从任何类型的文档、图片、证件中提取个人相关信息，并将其转换为MDAC表单格式。

【识别范围】：
- 身份证件：护照、身份证、驾驶证、签证等
- 旅行文档：机票、酒店预订单、行程单等
- 个人文档：简历、名片、申请表、收据(含个人信息)等
- 任何包含姓名、日期、联系方式、地址的文档

【核心要求】：
1. 智能识别并提取任何可用的个人信息
2. 灵活匹配各种表述方式和格式
3. 只返回有效的JSON数据，无解释文本
4. 严格使用MDAC原生字段ID
5. 确保JSON语法完全正确

【智能识别规则】：
• 姓名识别：中文姓名自动转换为英文大写格式
• 日期智能转换：各种日期格式统一为DD/MM/YYYY
• 性别智能识别：男/M/Male→MALE，女/F/Female→FEMALE
• 国籍智能转换：识别国家名称并转换为标准代码
• 联系方式提取：电话、邮箱、地址等
• 旅行信息推断：从机票、酒店预订中提取日期和地点

【MDAC字段映射】：
{
  "name": "姓名(自动转换为英文大写)",
  "passNo": "护照号/证件号码",
  "dob": "出生日期(任何格式自动转换)",
  "passExpDte": "护照/证件有效期",
  "nationality": "国籍(智能转换为标准代码)",
  "sex": "性别(智能识别转换)",
  "email": "电子邮箱地址",
  "confirmEmail": "确认邮箱(通常与email相同)",
  "region": "电话区号(如86、60等数字)",
  "mobile": "手机/电话号码",
  "arrDt": "到达/入境日期",
  "depDt": "出发/离境日期",
  "vesselNm": "航班号/车次/船舶号",
  "trvlMode": "交通方式(飞机→AIR,陆路→LAND,海路→SEA)",
  "embark": "出发地/最后登船港",
  "accommodationStay": "住宿类型(酒店→HOTEL,居住→RESIDENCE,其他→OTHERS)",
  "accommodationAddress1": "住宿/居住地址",
  "accommodationAddress2": "地址补充信息",
  "accommodationState": "州/省份",
  "accommodationCity": "城市",
  "accommodationPostcode": "邮政编码"
}

【输出要求】：
- 只返回JSON对象，不要任何解释文字
- 只包含从实际文档中识别出的信息
- 找不到的字段设为null
- 确保JSON格式正确

现在请分析以下文档内容并提取个人信息：`;
  }

  // 调用豆包API（带重试机制和流式处理）
  async callDoubaoAPI(requestBody, sessionId) {
    const monitor = this.getPerformanceMonitor();
    const stepName = 'API调用';

    if (this.config.enableMonitoring) {
      monitor?.startStep(sessionId || 'api', stepName, '豆包API调用');
    }

    const body = {
      model: this.config.model,
      ...requestBody,
      temperature: this.config.temperature,
      top_p: this.config.top_p,
      max_tokens: this.config.max_tokens,
      stream: this.config.stream
    };

    // 检查缓存
    if (this.config.enableCache) {
      const cacheKey = this.generateCacheKey(body);
      const cached = this.getCachedResponse(cacheKey);
      if (cached) {
        if (this.config.enableMonitoring) {
          monitor?.recordSubStep(sessionId || 'api', stepName, '缓存命中', 0, '使用缓存数据');
        }
        console.log('✅ 缓存命中，跳过API调用');
        return cached;
      }
    }

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      const apiStart = performance.now();

      try {
        if (this.config.stream) {
          // 流式响应处理
          return await this.handleStreamingResponse(body, sessionId, stepName, apiStart, attempt);
        } else {
          // 标准响应处理
          return await this.handleStandardResponse(body, sessionId, stepName, apiStart, attempt);
        }

      } catch (error) {
        // 网络错误或其他异常
        if (attempt < this.config.maxRetries && this.isRetryableError(error)) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
          if (this.config.enableMonitoring) {
            monitor?.recordSubStep(sessionId || 'api', stepName, `等待重试`, 0, `${delay}ms后重试: ${error.message}`);
          }
          await this.delay(delay);
          continue;
        }

        if (this.config.enableMonitoring) {
          monitor?.endStep(sessionId || 'api', stepName, { success: false, error: error.message, retries: attempt - 1 });
        }
        throw error;
      }
    }
  }

  // 处理标准响应
  async handleStandardResponse(body, sessionId, stepName, apiStart, attempt) {
    const monitor = this.getPerformanceMonitor();

    const response = await fetch(this.config.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(body)
    });

    const networkTime = Math.round(performance.now() - apiStart);
    if (this.config.enableMonitoring) {
      monitor?.recordSubStep(sessionId || 'api', stepName, `网络请求(尝试${attempt})`, networkTime, `状态:${response.status}`);
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = `豆包API请求失败: ${response.status} ${response.statusText}. ${errorData.error?.message || JSON.stringify(errorData)}`;

      // 如果是可重试的错误码，且不是最后一次尝试
      if (this.isRetryableError(response.status) && attempt < this.config.maxRetries) {
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1); // 指数退避
        if (this.config.enableMonitoring) {
          monitor?.recordSubStep(sessionId || 'api', stepName, `等待重试`, 0, `${delay}ms后重试`);
        }
        await this.delay(delay);
        return null; // 继续重试
      }

      throw new Error(errorMessage);
    }

    const parseStart = performance.now();
    const data = await response.json();
    if (this.config.enableMonitoring) {
      monitor?.recordSubStep(sessionId || 'api', stepName, '响应解析', Math.round(performance.now() - parseStart));
    }

    // 缓存成功响应
    if (this.config.enableCache) {
      const cacheKey = this.generateCacheKey(body);
      this.setCachedResponse(cacheKey, data);
    }

    if (this.config.enableMonitoring) {
      monitor?.endStep(sessionId || 'api', stepName, { success: true, retries: attempt - 1 });
    }
    return data;
  }

  // 缓存相关方法 - 智能缓存系统
  generateCacheKey(requestBody) {
    // 智能缓存键生成 - 基于内容相似性而非完全匹配
    const keyData = {
      model: requestBody.model,
      // 对消息内容进行智能归一化，提高缓存命中率
      messages: this.normalizeMessagesForCache(requestBody.messages),
      temperature: requestBody.temperature,
      top_p: requestBody.top_p,
      max_tokens: requestBody.max_tokens
    };
    return JSON.stringify(keyData);
  }

  // 智能消息归一化 - 提高缓存命中率
  normalizeMessagesForCache(messages) {
    if (!messages || !Array.isArray(messages)) return messages;

    return messages.map(message => {
      if (message.role === 'user' && message.content) {
        // 提取文本内容进行归一化
        const textContent = this.extractTextFromMessage(message.content);
        if (textContent) {
          // 对文本内容进行智能归一化
          const normalizedText = this.smartNormalizeText(textContent);
          return {
            ...message,
            content: normalizedText
          };
        }
      }
      return message;
    });
  }

  // 从消息内容中提取文本
  extractTextFromMessage(content) {
    if (typeof content === 'string') return content;
    if (Array.isArray(content)) {
      // 多模态内容，提取文本部分
      const textParts = content.filter(item => item.type === 'text').map(item => item.text);
      return textParts.join('\n');
    }
    return '';
  }

  // 智能文本归一化 - 提高相似文本的缓存匹配
  smartNormalizeText(text) {
    if (!text) return text;

    // 1. 标准化日期格式
    text = text.replace(/(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/g, '$2/$3/$1');
    text = text.replace(/(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})/g, '$1/$2/$3');

    // 2. 标准化电话号码
    text = text.replace(/(\+?\d{1,3})[\s\-\.]?(\d{3,4})[\s\-\.]?(\d{4})/g, '$1$2$3');

    // 3. 标准化护照号码（移除空格）
    text = text.replace(/([A-Za-z])\s+(\d+)/g, '$1$2');

    // 4. 标准化姓名格式
    text = text.replace(/([\u4e00-\u9fff]+)\s+([\u4e00-\u9fff]+)/g, '$1$2');

    // 5. 转换为小写（提高匹配度）
    text = text.toLowerCase();

    // 6. 移除多余空格
    text = text.replace(/\s+/g, ' ').trim();

    return text;
  }

  // 智能缓存检索 - 支持模糊匹配
  getCachedResponse(cacheKey) {
    // 首先尝试精确匹配
    const cached = this.requestCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.config.cacheTTL) {
      this.cacheStats.hits++;
      this.cacheStats.total++;
      console.log(`✅ 精确缓存命中 (${this.cacheStats.hits}/${this.cacheStats.total})`);
      return cached.data;
    }

    // 尝试模糊匹配（基于文本相似度）
    if (this.config.enableFuzzyCache) {
      const fuzzyMatch = this.findFuzzyCacheMatch(cacheKey);
      if (fuzzyMatch) {
        this.cacheStats.hits++;
        this.cacheStats.total++;
        this.cacheStats.fuzzyHits = (this.cacheStats.fuzzyHits || 0) + 1;
        console.log(`✅ 模糊缓存命中 (${this.cacheStats.fuzzyHits}次模糊匹配)`);
        return fuzzyMatch.data;
      }
    }

    this.cacheStats.misses++;
    this.cacheStats.total++;
    return null;
  }

  // 模糊缓存匹配
  findFuzzyCacheMatch(targetKey) {
    try {
      const targetData = JSON.parse(targetKey);
      const targetText = this.extractTextFromMessage(targetData.messages?.[0]?.content || '');

      if (!targetText) return null;

      let bestMatch = null;
      let bestScore = 0.7; // 最低相似度阈值

      for (const [cacheKey, cached] of this.requestCache.entries()) {
        if (Date.now() - cached.timestamp >= this.config.cacheTTL) continue;

        try {
          const cacheData = JSON.parse(cacheKey);
          const cacheText = this.extractTextFromMessage(cacheData.messages?.[0]?.content || '');

          if (!cacheText) continue;

          // 计算文本相似度
          const similarity = this.calculateTextSimilarity(targetText, cacheText);

          if (similarity > bestScore) {
            bestScore = similarity;
            bestMatch = cached;
          }
        } catch (e) {
          continue;
        }
      }

      if (bestMatch) {
        console.log(`🔍 模糊匹配成功，相似度: ${(bestScore * 100).toFixed(1)}%`);
      }

      return bestMatch;
    } catch (e) {
      return null;
    }
  }

  // 计算文本相似度（简单实现）
  calculateTextSimilarity(text1, text2) {
    if (!text1 || !text2) return 0;

    // 使用Jaccard相似度算法
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  setCachedResponse(cacheKey, data) {
    const cacheEntry = {
      data: data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.requestCache.set(cacheKey, cacheEntry);

    // 智能缓存清理 - 基于LRU和访问频率
    this.intelligentCacheCleanup();

    console.log(`💾 缓存已保存 (${this.requestCache.size}个缓存项)`);
  }

  // 智能缓存清理
  intelligentCacheCleanup() {
    const maxSize = this.config.maxCacheSize || 100;

    if (this.requestCache.size <= maxSize) return;

    // 转换为数组并排序（按最后访问时间和访问频率）
    const entries = Array.from(this.requestCache.entries());
    entries.sort((a, b) => {
      const entryA = a[1];
      const entryB = b[1];

      // 优先清理长时间未访问的
      const timeDiff = entryA.lastAccessed - entryB.lastAccessed;
      if (timeDiff !== 0) return timeDiff;

      // 其次清理访问频率低的
      return entryA.accessCount - entryB.accessCount;
    });

    // 删除最老的10%条目
    const removeCount = Math.ceil(entries.length * 0.1);
    for (let i = 0; i < removeCount; i++) {
      this.requestCache.delete(entries[i][0]);
    }

    console.log(`🧹 智能缓存清理完成，删除了${removeCount}个条目`);
  }

  // 获取缓存统计 - 增强版
  getCacheStats() {
    return {
      hits: this.cacheStats.hits,
      misses: this.cacheStats.misses,
      total: this.cacheStats.total,
      fuzzyHits: this.cacheStats.fuzzyHits || 0,
      hitRate: this.cacheStats.total > 0 ? Math.round((this.cacheStats.hits / this.cacheStats.total) * 100) : 0,
      fuzzyHitRate: this.cacheStats.total > 0 ? Math.round(((this.cacheStats.fuzzyHits || 0) / this.cacheStats.total) * 100) : 0,
      cacheSize: this.requestCache.size,
      maxCacheSize: this.config.maxCacheSize || 100,
      cacheUtilization: Math.round((this.requestCache.size / (this.config.maxCacheSize || 100)) * 100)
    };
  }

  // 清理过期缓存
  cleanupExpiredCache() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, value] of this.requestCache.entries()) {
      if (now - value.timestamp > this.config.cacheTTL) {
        this.requestCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 清理了${cleaned}个过期缓存项`);
    }
  }

  // 处理流式响应
  async handleStreamingResponse(body, sessionId, stepName, apiStart, attempt) {
    const monitor = this.getPerformanceMonitor();

    const response = await fetch(this.config.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(body)
    });

    const networkTime = Math.round(performance.now() - apiStart);
    if (this.config.enableMonitoring) {
      monitor?.recordSubStep(sessionId || 'api', stepName, `流式请求(尝试${attempt})`, networkTime, `状态:${response.status}`);
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = `豆包API流式请求失败: ${response.status} ${response.statusText}. ${errorData.error?.message || JSON.stringify(errorData)}`;

      // 如果是可重试的错误码，且不是最后一次尝试
      if (this.isRetryableError(response.status) && attempt < this.config.maxRetries) {
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        if (this.config.enableMonitoring) {
          monitor?.recordSubStep(sessionId || 'api', stepName, `等待重试`, 0, `${delay}ms后重试`);
        }
        await this.delay(delay);
        return null; // 继续重试
      }

      throw new Error(errorMessage);
    }

    // 流式解析响应
    const parseStart = performance.now();
    const result = await this.parseStreamingResponse(response, sessionId, stepName);
    if (this.config.enableMonitoring) {
      monitor?.recordSubStep(sessionId || 'api', stepName, '流式解析', Math.round(performance.now() - parseStart));
    }

    // 缓存成功响应（流式响应也缓存）
    if (this.config.enableCache && result) {
      const cacheKey = this.generateCacheKey(body);
      this.setCachedResponse(cacheKey, result);
    }

    if (this.config.enableMonitoring) {
      monitor?.endStep(sessionId || 'api', stepName, { success: true, retries: attempt - 1, streaming: true });
    }
    return result;
  }

  // 解析流式响应
  async parseStreamingResponse(response, sessionId, stepName) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let buffer = ''; // 用于处理不完整的JSON行

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 确保按完整行处理
        const lines = buffer.split('\n');
        // 保留最后一行（可能不完整）到缓冲区
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine.startsWith('data: ')) {
            const data = trimmedLine.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content || '';
              fullContent += content;

              // 实时日志（可选）
              if (content && this.config.enableMonitoring) {
                console.log(`📝 流式响应接收: ${content.length}字符`);
              }
            } catch (e) {
              // 记录解析错误但不中断流处理
              if (this.config.enableMonitoring) {
                console.warn(`⚠️ 流式数据解析失败: ${data}`, e.message);
              }
            }
          }
        }
      }

      // 处理缓冲区中剩余的数据
      if (buffer.trim()) {
        const trimmedLine = buffer.trim();
        if (trimmedLine.startsWith('data: ')) {
          const data = trimmedLine.slice(6);
          if (data !== '[DONE]') {
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content || '';
              fullContent += content;
            } catch (e) {
              if (this.config.enableMonitoring) {
                console.warn(`⚠️ 缓冲区数据解析失败: ${data}`, e.message);
              }
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 解析最终的完整内容
    if (!fullContent.trim()) {
      throw new Error('流式响应为空');
    }

    return {
      choices: [{
        message: {
          content: fullContent
        }
      }]
    };
  }

  // 判断是否为可重试的错误
  isRetryableError(errorOrStatus) {
    if (typeof errorOrStatus === 'number') {
      // HTTP状态码
      return [408, 429, 500, 502, 503, 504].includes(errorOrStatus);
    } else {
      // 错误对象
      const errorMessage = errorOrStatus.message || errorOrStatus.toString();
      return errorMessage.includes('network') ||
             errorMessage.includes('timeout') ||
             errorMessage.includes('fetch') ||
             errorMessage.includes('Failed to fetch');
    }
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 解析API响应
  parseAPIResponse(apiResponse, metadata) {
    const rawContent = apiResponse?.choices?.[0]?.message?.content;

    if (!rawContent) {
      throw new Error('API响应为空');
    }

    // 尝试提取JSON
    let extractedData = null;
    let jsonParseErrors = [];

    // 记录原始响应用于调试
    console.log(`📥 原始API响应内容长度: ${rawContent.length}字符`);
    console.log(`📄 原始响应前200字符: ${rawContent.substring(0, 200)}`);
    console.log(`📄 原始响应后200字符: ${rawContent.substring(Math.max(0, rawContent.length - 200))}`);

    try {
      // 首先尝试直接解析
      extractedData = JSON.parse(rawContent);
    } catch (parseError) {
      jsonParseErrors.push(`直接解析失败: ${parseError.message}`);

      // 尝试定位具体的错误位置
      const errorPos = parseError.message.match(/position (\d+)/);
      if (errorPos) {
        const pos = parseInt(errorPos[1]);
        const start = Math.max(0, pos - 50);
        const end = Math.min(rawContent.length, pos + 50);
        console.error(`🐛 JSON错误位置上下文: "${rawContent.substring(start, end)}"`);
        console.error(`📍 错误位置: ${pos} 字符`);
      }

      // 尝试从响应中提取JSON部分 - 更宽松的匹配
      const jsonMatches = rawContent.match(/\{[\s\S]*\}/g);
      if (jsonMatches) {
        for (let i = 0; i < jsonMatches.length; i++) {
          try {
            extractedData = JSON.parse(jsonMatches[i]);
            console.log(`✅ 使用第${i+1}个JSON匹配成功`);
            break;
          } catch (matchError) {
            jsonParseErrors.push(`匹配${i+1}失败: ${matchError.message}`);
          }
        }
      }

      // 如果还是失败，尝试修复常见的JSON格式问题
      if (!extractedData) {
        try {
          // 首先尝试修复常见的JSON语法错误
          let fixedJson = this.fixCommonJsonErrors(rawContent);

          try {
            extractedData = JSON.parse(fixedJson);
            console.log('✅ 通过修复常见JSON错误解析成功');
          } catch (fixError1) {
            console.log('🔧 常见错误修复失败，尝试不完整JSON修复...');

            // 尝试修复不完整的JSON（添加缺失的括号）
            fixedJson = fixedJson.trim();

            // 如果缺少结尾的括号，尝试添加
            const openBraces = (fixedJson.match(/\{/g) || []).length;
            const closeBraces = (fixedJson.match(/\}/g) || []).length;

            if (openBraces > closeBraces) {
              // 尝试找到最后一个未闭合的位置
              const lastValidJson = this.findLastValidJsonPosition(fixedJson);
              if (lastValidJson) {
                extractedData = JSON.parse(lastValidJson);
                console.log('✅ 通过修复不完整JSON解析成功');
              }
            }
          }
        } catch (fixError) {
          jsonParseErrors.push(`修复尝试失败: ${fixError.message}`);
        }
      }

      // 最后的兜底方案：尝试从文本中提取MDAC字段
      if (!extractedData) {
        console.log('🔍 尝试从文本中提取MDAC字段作为兜底方案...');
        try {
          extractedData = this.extractMDACFieldsFromText(rawContent);
          if (extractedData) {
            console.log('✅ 通过文本提取获得MDAC字段');
          }
        } catch (textExtractionError) {
          jsonParseErrors.push(`文本提取失败: ${textExtractionError.message}`);
        }
      }

      // 记录所有解析失败的原因
      if (!extractedData) {
        console.error('所有JSON解析尝试失败:');
        jsonParseErrors.forEach(error => console.error(`  - ${error}`));
        console.warn('原始响应内容:', rawContent);
      }
    }

    // 检查是否所有字段都是null
    let hasValidData = false;
    if (extractedData && typeof extractedData === 'object') {
      hasValidData = Object.values(extractedData).some(value => value !== null && value !== undefined && value !== '');
    }

    // 记录数据有效性状态
    if (extractedData && !hasValidData) {
      console.warn('⚠️ AI解析成功但未找到有效数据 - 文件可能不包含MDAC相关信息');
    }

    return {
      success: !!extractedData,
      data: extractedData,
      rawResponse: rawContent,
      hasValidData: hasValidData, // 新增字段指示是否有有效数据
      metadata: {
        ...metadata,
        processingTime: performance.now(),
        apiModel: this.config.model,
        responseLength: rawContent.length,
        hasValidData: hasValidData,
        jsonParseErrors: extractedData ? undefined : jsonParseErrors
      }
    };
  }

  // 验证处理结果
  validateResult(result) {
    if (!result || !result.success) return false;

    // 检查是否有提取到的数据
    if (result.data && typeof result.data === 'object') {
      const fieldCount = Object.keys(result.data).length;
      return fieldCount > 0;
    }

    // 检查是否至少有原始响应
    return result.rawResponse && result.rawResponse.length > 0;
  }

  // 设置API密钥（支持动态配置更新）
  setApiKey(apiKey) {
    this.config.apiKey = apiKey;
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  // 获取当前配置
  getConfig() {
    return { ...this.config };
  }

  // 获取处理器状态
  getStatus() {
    return {
      apiUrl: this.config.apiUrl,
      model: this.config.model,
      config: this.config,
      hasImageProcessor: !!this.imageProcessor,
      hasFileProcessor: !!this.fileProcessor,
      hasPerformanceMonitor: !!this.performanceMonitor
    };
  }

  // 找到最后一个有效的JSON位置（用于修复不完整的JSON）
  findLastValidJsonPosition(str) {
    let depth = 0;
    let inString = false;
    let escapeNext = false;
    let lastValidPos = -1;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === '\\' && inString) {
        escapeNext = true;
        continue;
      }

      if (char === '"' && !inString) {
        inString = true;
      } else if (char === '"' && inString) {
        inString = false;
      } else if (!inString) {
        if (char === '{') {
          depth++;
        } else if (char === '}') {
          depth--;
          if (depth === 0) {
            lastValidPos = i + 1;
          }
        }
      }
    }

    // 如果找到有效的JSON结尾，返回截取的部分
    if (lastValidPos > 0) {
      return str.substring(0, lastValidPos);
    }

    // 如果没有找到完整的对象，尝试找到最后一个完整的键值对
    if (depth > 0) {
      // 找到最后一个逗号或开头的位置
      const lastComma = str.lastIndexOf(',');
      const lastColon = str.lastIndexOf(':');

      if (lastComma > 0 && lastComma > lastColon) {
        // 尝试在逗号后结束
        return str.substring(0, lastComma + 1) + '}';
      }
    }

    return null;
  }

  // 从文本中提取MDAC字段（当JSON解析失败时的兜底方案）
  extractMDACFieldsFromText(text) {
    console.log('🔍 开始从文本中提取MDAC字段...');

    const fields = {};

    // 1. 提取姓名 - 匹配中文姓名或英文姓名 (使用MDAC原生字段名)
    const namePatterns = [
      /姓名[:：\s]*([\u4e00-\u9fff·]+(?:[\u4e00-\u9fff·\s]+[\u4e00-\u9fff·]+)?)/i,
      /姓名[:：\s]*([A-Za-z]+(?:\s+[A-Za-z]+)*)/i,
      /name[:：\s]*([A-Za-z]+(?:\s+[A-Za-z]+)*)/i,
      /姓名\s*是\s*([\u4e00-\u9fff·]+(?:[\u4e00-\u9fff·\s]+[\u4e00-\u9fff·]+)?)/i
    ];

    for (const pattern of namePatterns) {
      const match = text.match(pattern);
      if (match) {
        fields.name = match[1].trim().toUpperCase(); // 使用MDAC原生字段名
        console.log(`✅ 提取到姓名: ${fields.name}`);
        break;
      }
    }

    // 2. 提取护照号码 (使用MDAC原生字段名)
    const passportPatterns = [
      /护照(?:号码)?[:：\s]*([A-Za-z]\d{6,9})/i,
      /passport(?:\s+number)?[:：\s]*([A-Za-z]\d{6,9})/i,
      /护照号[:：\s]*([A-Za-z]\d{6,9})/i
    ];

    for (const pattern of passportPatterns) {
      const match = text.match(pattern);
      if (match) {
        fields.passNo = match[1].trim().toUpperCase(); // 使用MDAC原生字段名
        console.log(`✅ 提取到护照号码: ${fields.passNo}`);
        break;
      }
    }

    // 3. 提取出生日期 (使用MDAC原生字段名)
    const birthDatePatterns = [
      /出生日期[:：\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/,
      /生日[:：\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/,
      /出生年月[:：\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/,
      /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})\s*出生/
    ];

    for (const pattern of birthDatePatterns) {
      const match = text.match(pattern);
      if (match) {
        fields.dob = this.normalizeDate(match[1]); // 使用MDAC原生字段名
        console.log(`✅ 提取到出生日期: ${fields.dob}`);
        break;
      }
    }

    // 4. 提取性别 (使用MDAC原生字段名和标准值)
    const genderPatterns = [
      /性别[:：\s]*(男|女|MALE|FEMALE|M|F)/i,
      /(\u7537|\u5973)/,
      /性别\s*是\s*(男|女)/i
    ];

    for (const pattern of genderPatterns) {
      const match = text.match(pattern);
      if (match) {
        const gender = match[1].toLowerCase();
        if (gender.includes('男') || gender === 'm' || gender === 'male') {
          fields.sex = 'MALE'; // 使用MDAC原生字段名和标准值
          console.log(`✅ 提取到性别: MALE`);
          break;
        } else if (gender.includes('女') || gender === 'f' || gender === 'female') {
          fields.sex = 'FEMALE'; // 使用MDAC原生字段名和标准值
          console.log(`✅ 提取到性别: FEMALE`);
          break;
        }
      }
    }

    // 5. 提取国籍 (使用MDAC原生字段名)
    const nationalityPatterns = [
      /国籍[:：\s]*(中国|美国|英国|新加坡|马来西亚|CHN|USA|GBR|SGP|MYS)/i,
      /(\u4e2d\u56fd|\u7f8e\u56fd|\u82f1\u56fd|\u65b0\u52a0\u5761|\u9a6c\u6765\u897f\u4e9a)/
    ];

    for (const pattern of nationalityPatterns) {
      const match = text.match(pattern);
      if (match) {
        fields.nationality = this.normalizeNationality(match[1]); // 使用MDAC原生字段名
        console.log(`✅ 提取到国籍: ${fields.nationality}`);
        break;
      }
    }

    // 6. 提取邮箱 (使用MDAC原生字段名)
    const emailPattern = /[\w\.-]+@[\w\.-]+\.\w+/;
    const emailMatch = text.match(emailPattern);
    if (emailMatch) {
      fields.email = emailMatch[0].toLowerCase(); // 使用MDAC原生字段名
      console.log(`✅ 提取到邮箱: ${fields.email}`);
    }

    // 7. 提取手机号码 (使用MDAC原生字段名)
    const phonePattern = /(\+?\d{1,3})?[-.\s]?(\d{1,4})[-.\s]?(\d{1,4})[-.\s]?(\d{4,})/;
    const phoneMatch = text.match(phonePattern);
    if (phoneMatch) {
      fields.mobile = phoneMatch[0].replace(/[-.\s]/g, ''); // 使用MDAC原生字段名
      console.log(`✅ 提取到手机号码: ${fields.mobile}`);
    }

    // 8. 提取到达日期 (使用MDAC原生字段名)
    const arrivalDatePatterns = [
      /到达日期[:：\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/,
      /到达[:：\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/,
      /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})\s*到达/
    ];

    for (const pattern of arrivalDatePatterns) {
      const match = text.match(pattern);
      if (match) {
        fields.arrDt = this.normalizeDate(match[1]); // 使用MDAC原生字段名
        console.log(`✅ 提取到到达日期: ${fields.arrDt}`);
        break;
      }
    }

    // 9. 提取航班号 (使用MDAC原生字段名)
    const flightPattern = /航班(?:号)?[:：\s]*([A-Za-z]{2}\d{1,4})/i;
    const flightMatch = text.match(flightPattern);
    if (flightMatch) {
      fields.vesselNm = flightMatch[1].toUpperCase(); // 使用MDAC原生字段名
      console.log(`✅ 提取到航班号: ${fields.vesselNm}`);
    }

    console.log(`📊 文本提取完成，共提取到 ${Object.keys(fields).length} 个字段`);
    return Object.keys(fields).length > 0 ? fields : null;
  }

  // 标准化日期格式
  normalizeDate(dateStr) {
    const match = dateStr.match(/(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{2,4})/);
    if (match) {
      const day = match[1].padStart(2, '0');
      const month = match[2].padStart(2, '0');
      const year = match[3].length === 2 ? '20' + match[3] : match[3];
      return `${day}/${month}/${year}`;
    }
    return dateStr;
  }

  // 标准化国籍代码
  normalizeNationality(nationality) {
    const mapping = {
      '中国': 'CHN', 'CHN': 'CHN',
      '美国': 'USA', 'USA': 'USA',
      '英国': 'GBR', 'GBR': 'GBR',
      '新加坡': 'SGP', 'SGP': 'SGP',
      '马来西亚': 'MYS', 'MYS': 'MYS'
    };
    return mapping[nationality] || nationality;
  }

  // 修复常见的JSON语法错误
  fixCommonJsonErrors(str) {
    let fixed = str;

    console.log(`🔧 开始修复JSON语法错误...`);

    // 1. 修复属性名引号问题
    // 将 { name: "value" } 改为 { "name": "value" }
    fixed = fixed.replace(/\{\s*(\w+)\s*:/g, '{ "$1":');
    fixed = fixed.replace(/,\s*(\w+)\s*:/g, ', "$1":');

    // 2. 修复尾随逗号
    fixed = fixed.replace(/,\s*}/g, '}');
    fixed = fixed.replace(/,\s*]/g, ']');

    // 3. 修复未闭合的字符串
    const lines = fixed.split('\n');
    let inString = false;
    let escapeNext = false;
    let result = [];

    for (let line of lines) {
      let newLine = '';
      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (escapeNext) {
          escapeNext = false;
          newLine += char;
          continue;
        }

        if (char === '\\') {
          escapeNext = true;
          newLine += char;
          continue;
        }

        if (char === '"' && !inString) {
          inString = true;
          newLine += char;
        } else if (char === '"' && inString) {
          inString = false;
          newLine += char;
        } else {
          newLine += char;
        }
      }
      result.push(newLine);
    }

    fixed = result.join('\n');

    // 如果字符串未闭合，尝试修复
    if (inString) {
      // 找到最后一个引号并确保字符串闭合
      const lastQuote = fixed.lastIndexOf('"');
      if (lastQuote !== -1) {
        const afterQuote = fixed.substring(lastQuote + 1);
        if (afterQuote.includes('}') || afterQuote.includes(',')) {
          // 字符串应该在引号处结束
          fixed = fixed.substring(0, lastQuote + 1) + '"}';
        }
      }
    }

    // 4. 修复缺失的冒号
    fixed = fixed.replace(/"\s*(\w+)\s"/g, '"$1":');

    console.log(`🔧 JSON修复完成，长度变化: ${str.length} -> ${fixed.length}`);

    return fixed;
  }

  // 获取实时性能指标
  getRealTimeMetrics() {
    return {
      ...this.realTimeMetrics,
      performanceMetrics: { ...this.performanceMetrics },
      cacheStats: { ...this.cacheStats }
    };
  }

  // 获取性能统计报告
  getPerformanceReport() {
    const runtime = Date.now() - this.performanceMetrics.startTime;
    const successRate = this.performanceMetrics.totalRequests > 0
      ? (this.performanceMetrics.successfulRequests / this.performanceMetrics.totalRequests) * 100
      : 0;

    return {
      runtime: Math.round(runtime / 1000), // 秒
      totalRequests: this.performanceMetrics.totalRequests,
      successfulRequests: this.performanceMetrics.successfulRequests,
      failedRequests: this.performanceMetrics.failedRequests,
      successRate: Math.round(successRate * 100) / 100,
      averageResponseTime: Math.round(this.performanceMetrics.averageResponseTime),
      averageProcessingTime: Math.round(this.performanceMetrics.averageProcessingTime),
      totalTokensUsed: this.performanceMetrics.totalTokensUsed,
      compressionRatio: Math.round(this.performanceMetrics.compressionRatio * 100) / 100,
      cacheHitRate: Math.round(this.performanceMetrics.cacheHitRate * 100) / 100,
      peakMemoryUsage: Math.round(this.performanceMetrics.peakMemoryUsage * 100) / 100,
      realTimeMetrics: { ...this.realTimeMetrics },
      cacheStats: { ...this.cacheStats }
    };
  }
}

// 全局暴露
window.UnifiedMultiModalProcessor = UnifiedMultiModalProcessor;

// 便捷函数
window.getUnifiedProcessorMetrics = function() {
  if (window.UnifiedMultiModalProcessor?._instance) {
    return window.UnifiedMultiModalProcessor._instance.getRealTimeMetrics();
  }
  return null;
};

window.getUnifiedProcessorReport = function() {
  if (window.UnifiedMultiModalProcessor?._instance) {
    return window.UnifiedMultiModalProcessor._instance.getPerformanceReport();
  }
  return null;
};

window.cleanupUnifiedProcessor = function() {
  if (window.UnifiedMultiModalProcessor?._instance) {
    // 清理定时器
    if (window.UnifiedMultiModalProcessor._instance.metricsInterval) {
      clearInterval(window.UnifiedMultiModalProcessor._instance.metricsInterval);
    }
    if (window.UnifiedMultiModalProcessor._instance.memoryInterval) {
      clearInterval(window.UnifiedMultiModalProcessor._instance.memoryInterval);
    }
  }
};

// 监听页面卸载事件
window.addEventListener('beforeunload', () => {
  window.cleanupUnifiedProcessor();
});
