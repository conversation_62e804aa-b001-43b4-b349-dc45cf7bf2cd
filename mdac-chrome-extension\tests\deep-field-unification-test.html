<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC深度字段统一测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .mapped { color: #28a745; }
        .unmapped { color: #dc3545; }
        .transformed { color: #007bff; }
        .button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .result-section {
            display: none;
            margin-top: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .test-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 MDAC深度字段统一测试</h1>

        <!-- 系统状态检查 -->
        <div class="test-section">
            <h3>🔍 系统组件状态检查</h3>
            <div id="systemStatus">
                <p>正在检查系统组件状态...</p>
            </div>
        </div>

        <!-- 测试数据展示 -->
        <div class="test-section">
            <h3>📋 测试数据样例</h3>
            <p><strong>AI返回的原始数据（14个字段）：</strong></p>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>值</th>
                        <th>数据类型</th>
                    </tr>
                </thead>
                <tbody id="originalDataBody">
                    <tr>
                        <td>name</td>
                        <td>LIMING</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>passNo</td>
                        <td>G12345678</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>dob</td>
                        <td>01/01/1990</td>
                        <td>日期</td>
                    </tr>
                    <tr>
                        <td>passExpDte</td>
                        <td>01/01/2026</td>
                        <td>日期</td>
                    </tr>
                    <tr>
                        <td>nationality</td>
                        <td>CHN</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>sex</td>
                        <td>MALE</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>email</td>
                        <td><EMAIL></td>
                        <td>邮箱</td>
                    </tr>
                    <tr>
                        <td>region</td>
                        <td>60</td>
                        <td>区号</td>
                    </tr>
                    <tr>
                        <td>mobile</td>
                        <td>123456789</td>
                        <td>电话</td>
                    </tr>
                    <tr>
                        <td>arrDt</td>
                        <td>01/08/2025</td>
                        <td>日期</td>
                    </tr>
                    <tr>
                        <td>depDt</td>
                        <td>07/08/2025</td>
                        <td>日期</td>
                    </tr>
                    <tr>
                        <td>vesselNm</td>
                        <td>MH123</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>accommodationStay</td>
                        <td>HOTEL</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>accommodationAddress1</td>
                        <td>Hotel KL City Center</td>
                        <td>字符串</td>
                    </tr>
                    <tr>
                        <td>accommodationPostcode</td>
                        <td>50000</td>
                        <td>字符串</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 测试操作 -->
        <div class="test-section">
            <h3>🧪 深度统一测试</h3>
            <button class="button btn-primary" onclick="testFieldConstants()">测试字段常量</button>
            <button class="button btn-primary" onclick="testFieldMapping()">测试字段映射</button>
            <button class="button btn-primary" onclick="testUnifiedTransformer()">测试统一转换器</button>
            <button class="button btn-secondary" onclick="runFullIntegrationTest()">完整集成测试</button>
            <button class="button btn-secondary" onclick="clearResults()">清除结果</button>

            <div id="testResults" class="result-section">
                <h3>测试结果</h3>
                <div id="resultContent"></div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="test-section">
            <h3>📊 测试统计</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="passedTests">0</div>
                    <div class="stat-label">通过测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="failedTests">0</div>
                    <div class="stat-label">失败测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="transformRatio">0%</div>
                    <div class="stat-label">转换成功率</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="test-log" id="testLog"></div>
        </div>
    </div>

    <!-- 加载测试依赖 -->
    <script src="../utils/field-constants.js"></script>
    <script src="../utils/field-mapping-config.js"></script>
    <script src="../utils/unified-field-transformer.js"></script>

    <script>
        // 测试统计数据
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            transformRatio: 0
        };

        // 模拟AI返回数据
        const sampleAIData = {
            name: 'LIMING',
            passportNo: 'G12345678',
            birthDate: '01/01/1990',
            passportExpiry: '01/01/2026',
            nationality: 'CHN',
            gender: 'MALE',
            email: '<EMAIL>',
            mobile: '60123456789',
            arrivalDate: '01/08/2025',
            departureDate: '07/08/2025',
            flightNo: 'MH123',
            accommodationType: 'HOTEL',
            address1: 'Hotel KL City Center',
            postcode: '50000'
        };

        // 日志函数
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('testLog');
            const colorMap = {
                'info': '#e2e8f0',
                'success': '#68d391',
                'warning': '#fbb6ce',
                'error': '#fc8181'
            };
            logDiv.innerHTML += `<div style="color: ${colorMap[level]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            document.getElementById('transformRatio').textContent = testStats.transformRatio + '%';

            const progress = testStats.total > 0 ? (testStats.passed / testStats.total) * 100 : 0;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 显示测试结果
        function showResults(title, results, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');

            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = `<div class="test-section ${type}"><h4>${title}</h4>${results}</div>`;
        }

        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let statusHTML = '<div class="stats-grid">';

            // 检查字段常量
            const constantsLoaded = typeof MDACFieldConstants !== 'undefined';
            statusHTML += `
                <div class="stat-card">
                    <div class="stat-value" style="color: ${constantsLoaded ? '#28a745' : '#dc3545'}">
                        ${constantsLoaded ? '✅' : '❌'}
                    </div>
                    <div class="stat-label">字段常量</div>
                </div>
            `;

            // 检查字段映射配置
            const mappingLoaded = typeof MDACFieldMappingConfig !== 'undefined';
            statusHTML += `
                <div class="stat-card">
                    <div class="stat-value" style="color: ${mappingLoaded ? '#28a745' : '#dc3545'}">
                        ${mappingLoaded ? '✅' : '❌'}
                    </div>
                    <div class="stat-label">映射配置</div>
                </div>
            `;

            // 检查统一转换器
            const transformerLoaded = typeof UnifiedFieldTransformer !== 'undefined';
            statusHTML += `
                <div class="stat-card">
                    <div class="stat-value" style="color: ${transformerLoaded ? '#28a745' : '#dc3545'}">
                        ${transformerLoaded ? '✅' : '❌'}
                    </div>
                    <div class="stat-label">统一转换器</div>
                </div>
            `;

            statusHTML += '</div>';
            statusDiv.innerHTML = statusHTML;

            log(`系统状态检查完成 - 常量: ${constantsLoaded}, 映射: ${mappingLoaded}, 转换器: ${transformerLoaded}`);
        }

        // 测试字段常量
        function testFieldConstants() {
            testStats.total++;
            log('开始测试字段常量...');

            try {
                if (typeof MDACFieldConstants === 'undefined') {
                    throw new Error('MDACFieldConstants 未定义');
                }

                const constants = new MDACFieldConstants();

                // 测试标准字段
                const standardFields = constants.getAllStandardFields();
                log(`✅ 获取到 ${standardFields.length} 个标准字段`);

                // 测试字段映射
                const mappedField = constants.getStandardFieldName('passengerName');
                log(`✅ passengerName 映射到: ${mappedField}`);

                // 测试字段类型
                const fieldType = constants.getFieldType('name');
                log(`✅ name 字段类型: ${fieldType}`);

                // 测试必填字段
                const isRequired = constants.isRequiredField('name');
                log(`✅ name 是否必填: ${isRequired}`);

                testStats.passed++;
                showResults('✅ 字段常量测试通过', `
                    <p>标准字段数量: ${standardFields.length}</p>
                    <p>兼容性映射数量: ${Object.keys(constants.COMPATIBILITY_MAPPING).length}</p>
                    <p>必填字段数量: ${constants.REQUIRED_FIELDS.length}</p>
                `, 'success');

            } catch (error) {
                testStats.failed++;
                log(`❌ 字段常量测试失败: ${error.message}`, 'error');
                showResults('❌ 字段常量测试失败', `<p>${error.message}</p>`, 'error');
            }

            updateStats();
        }

        // 测试字段映射
        function testFieldMapping() {
            testStats.total++;
            log('开始测试字段映射...');

            try {
                if (typeof MDACFieldMappingConfig === 'undefined') {
                    throw new Error('MDACFieldMappingConfig 未定义');
                }

                const config = new MDACFieldMappingConfig();

                // 测试AI到MDAC转换
                const transformedData = config.transformAIToMDAC(sampleAIData);
                log(`✅ AI数据转换完成，转换字段数: ${Object.keys(transformedData).length}`);

                // 测试数据验证
                const validation = config.validateDataIntegrity(transformedData);
                log(`✅ 数据验证完成，错误: ${validation.errors.length}, 警告: ${validation.warnings.length}`);

                testStats.transformRatio = Math.round((Object.keys(transformedData).length / Object.keys(sampleAIData).length) * 100);

                testStats.passed++;
                showResults('✅ 字段映射测试通过', `
                    <p>原始字段数: ${Object.keys(sampleAIData).length}</p>
                    <p>转换字段数: ${Object.keys(transformedData).length}</p>
                    <p>转换成功率: ${testStats.transformRatio}%</p>
                    <p>验证错误: ${validation.errors.length}</p>
                    <p>验证警告: ${validation.warnings.length}</p>
                `, 'success');

            } catch (error) {
                testStats.failed++;
                log(`❌ 字段映射测试失败: ${error.message}`, 'error');
                showResults('❌ 字段映射测试失败', `<p>${error.message}</p>`, 'error');
            }

            updateStats();
        }

        // 测试统一转换器
        function testUnifiedTransformer() {
            testStats.total++;
            log('开始测试统一转换器...');

            try {
                if (typeof UnifiedFieldTransformer === 'undefined') {
                    throw new Error('UnifiedFieldTransformer 未定义');
                }

                const transformer = new UnifiedFieldTransformer();

                // 检查转换器状态
                const status = transformer.getStatus();
                log(`✅ 转换器状态: ${JSON.stringify(status)}`);

                // 测试AI到MDAC转换
                const transformResult = transformer.transformAIToMDAC(sampleAIData);
                log(`✅ 统一转换完成，成功: ${transformResult.success}`);

                if (transformResult.success) {
                    testStats.transformRatio = Math.round(transformResult.stats.transformRatio * 100);
                    log(`✅ 转换统计: ${JSON.stringify(transformResult.stats)}`);

                    testStats.passed++;
                    showResults('✅ 统一转换器测试通过', `
                        <p>转换成功: ✅</p>
                        <p>原始字段数: ${transformResult.stats.sourceFieldCount}</p>
                        <p>目标字段数: ${transformResult.stats.targetFieldCount}</p>
                        <p>转换比率: ${testStats.transformRatio}%</p>
                        <p>转换耗时: ${transformResult.stats.transformTime}ms</p>
                    `, 'success');
                } else {
                    throw new Error(transformResult.error);
                }

            } catch (error) {
                testStats.failed++;
                log(`❌ 统一转换器测试失败: ${error.message}`, 'error');
                showResults('❌ 统一转换器测试失败', `<p>${error.message}</p>`, 'error');
            }

            updateStats();
        }

        // 完整集成测试
        function runFullIntegrationTest() {
            testStats.total++;
            log('开始完整集成测试...');

            try {
                let results = [];

                // 1. 检查所有组件
                const componentsOk = typeof MDACFieldConstants !== 'undefined' &&
                                   typeof MDACFieldMappingConfig !== 'undefined' &&
                                   typeof UnifiedFieldTransformer !== 'undefined';

                if (!componentsOk) {
                    throw new Error('必要组件未完全加载');
                }

                results.push('✅ 组件加载检查通过');

                // 2. 测试完整转换流程
                const transformer = new UnifiedFieldTransformer();
                const transformResult = transformer.transformAIToMDAC(sampleAIData);

                if (!transformResult.success) {
                    throw new Error(`转换失败: ${transformResult.error}`);
                }

                results.push(`✅ 完整转换流程通过 (${transformResult.stats.targetFieldCount}/${transformResult.stats.sourceFieldCount})`);

                // 3. 测试数据验证
                const constants = new MDACFieldConstants();
                const requiredFields = constants.REQUIRED_FIELDS;
                const missingFields = requiredFields.filter(field => !transformResult.data[field]);

                if (missingFields.length > 0) {
                    results.push(`⚠️ 缺少必填字段: ${missingFields.join(', ')}`);
                } else {
                    results.push('✅ 必填字段检查通过');
                }

                // 4. 测试反向转换
                const reverseResult = transformer.transformMDACToAI(transformResult.data);
                if (reverseResult.success) {
                    results.push('✅ 反向转换测试通过');
                } else {
                    results.push(`⚠️ 反向转换失败: ${reverseResult.error}`);
                }

                testStats.transformRatio = Math.round(transformResult.stats.transformRatio * 100);
                testStats.passed++;

                showResults('✅ 完整集成测试通过', `
                    <div style="max-height: 200px; overflow-y: auto;">
                        ${results.map(r => `<p>${r}</p>`).join('')}
                    </div>
                    <hr>
                    <p>最终转换成功率: ${testStats.transformRatio}%</p>
                    <p>总测试时间: ${transformResult.stats.transformTime}ms</p>
                `, 'success');

            } catch (error) {
                testStats.failed++;
                log(`❌ 完整集成测试失败: ${error.message}`, 'error');
                showResults('❌ 完整集成测试失败', `<p>${error.message}</p>`, 'error');
            }

            updateStats();
        }

        // 清除结果
        function clearResults() {
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('testLog').innerHTML = '';

            testStats = { total: 0, passed: 0, failed: 0, transformRatio: 0 };
            updateStats();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('MDAC深度字段统一测试页面已加载');
            checkSystemStatus();
            updateStats();
        });
    </script>
</body>
</html>