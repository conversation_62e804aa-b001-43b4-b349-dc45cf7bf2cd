# MDAC 网站表单字段分析文档

## 网站信息
- **网站标题**: Malaysia Digital Arrival Card (MDAC)
- **URL**: https://imigresen-online.imi.gov.my/mdac/main?registerMain
- **所属机构**: Jabatan Imigresen Malaysia (马来西亚移民局)

---

## 一、个人信息 (Personal Information)

### 文本输入字段

#### 1. 姓名 (Name)
- **字段ID**: `name`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 60字符
- **限制**: 仅字母字符，不允许粘贴
- **CSS类**: `form-control uppercase name`

#### 2. 护照号码 (Passport No.)
- **字段ID**: `passNo`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 12字符
- **限制**: 数字和字符组合，不允许粘贴
- **CSS类**: `form-control uppercase passNo`

#### 3. 出生日期 (Date of Birth)
- **字段ID**: `dob`
- **字段类型**: 日期选择器 (日历控件)
- **必填**: 是 (*)
- **格式**: DD/MM/YYYY
- **属性**: 只读 (readonly)
- **占位符**: "DD/MM/YYYY"

#### 4. 护照有效期 (Date of Passport Expiry)
- **字段ID**: `passExpDte`
- **字段类型**: 日期选择器 (日历控件)
- **必填**: 是 (*)
- **格式**: DD/MM/YYYY
- **属性**: 只读 (readonly)
- **占位符**: "DD/MM/YYYY"

#### 5. 电子邮箱 (Email Address)
- **字段ID**: `email`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 100字符
- **验证**: 邮箱格式验证，不允许粘贴
- **CSS类**: `form-control email`

#### 6. 确认电子邮箱 (Confirm Email Address)
- **字段ID**: `confirmEmail`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 100字符
- **验证**: 与邮箱字段匹配验证，不允许复制粘贴
- **CSS类**: `form-control confirmEmail`

#### 7. 手机号码 (Mobile No.)
- **字段ID**: `mobile`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 12字符
- **限制**: 数字和特殊字符，不允许粘贴
- **CSS类**: `form-control uppercase mobile`

### 下拉菜单字段

#### 8. 国籍/公民身份 (Nationality / Citizenship)
- **字段ID**: `nationality`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **选项数量**: 约280个国家/地区
- **CSS类**: `form-control uppercase nationality`

**主要选项包括**:
- AFG - AFGHANISTAN
- ALB - ALBANIA  
- DZA - ALGERIA
- ASM - AMERICAN SAMOA
- AND - ANDORRA
- AGO - ANGOLA
- AIA - ANGUILLA
- ATA - ANTARCTICA
- ATG - ANTIGUA AND BARBUDA
- ARG - ARGENTINA
- ARM - ARMENIA
- ABW - ARUBA
- AUS - AUSTRALIA
- AUT - AUSTRIA
- AZE - AZERBAIJAN
- BHS - BAHAMAS
- BHR - BAHRAIN
- BGD - BANGLADESH
- BRB - BARBADOS
- BLR - BELARUS
- BEL - BELGIUM
- BLZ - BELIZE
- BEN - BENIN
- BMU - BERMUDA
- BTN - BHUTAN
- BOL - BOLIVIA
- BIH - BOSNIA AND HERZEGOVINA
- BWA - BOTSWANA
- BRA - BRAZIL
- BRN - BRUNEI DARUSSALAM
- BGR - BULGARIA
- BFA - BURKINA FASO
- BDI - BURUNDI
- KHM - CAMBODIA
- CMR - CAMEROON
- CAN - CANADA
- CHN - CHINA
- CUB - CUBA
- CYP - CYPRUS
- CZE - CZECH REPUBLIC
- DNK - DENMARK
- EGY - EGYPT
- FIN - FINLAND
- FRA - FRANCE
- DEU - GERMANY
- GRC - GREECE
- HKG - HONG KONG
- HUN - HUNGARY
- ISL - ICELAND
- IND - INDIA
- IDN - INDONESIA
- IRN - IRAN
- IRQ - IRAQ
- IRL - IRELAND
- ISR - ISRAEL
- ITA - ITALY
- JAM - JAMAICA
- JPN - JAPAN
- JOR - JORDAN
- KAZ - KAZAKHSTAN
- KEN - KENYA
- KOR - REPUBLIC OF KOREA
- KWT - KUWAIT
- LAO - LAO PDR
- LBN - LEBANON
- MAC - MACAU
- MYS - MALAYSIA
- MDV - MALDIVES
- MEX - MEXICO
- MMR - MYANMAR
- NPL - NEPAL
- NLD - NETHERLANDS
- NZL - NEW ZEALAND
- NGA - NIGERIA
- NOR - NORWAY
- PAK - PAKISTAN
- PHL - PHILIPPINES
- POL - POLAND
- PRT - PORTUGAL
- QAT - QATAR
- RUS - RUSSIA
- SAU - SAUDI ARABIA
- SGP - SINGAPORE
- ZAF - SOUTH AFRICA
- ESP - SPAIN
- LKA - SRI LANKA
- SWE - SWEDEN
- CHE - SWITZERLAND
- TWN - TAIWAN
- THA - THAILAND
- TUR - TURKEY
- ARE - UNITED ARAB EMIRATES
- GBR - UNITED KINGDOM
- USA - USA
- VNM - VIETNAM
- ZWE - ZIMBABWE
（以及其他国家/地区）

#### 9. 性别 (Sex)
- **字段ID**: `sex`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase sex`

**选项**:
- 空选项: "Please Choose"
- 值 "1": MALE
- 值 "2": FEMALE

#### 10. 国家/地区代码 (Country / Region Code)
- **字段ID**: `region`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase region`

**主要选项包括**:
- ( 1 ) CANADA
- ( 1 ) UNITED STATES
- ( 1242 ) BAHAMAS
- ( 1246 ) BARBADOS
- ( 1264 ) ANGUILLA
- ( 1268 ) ANTIGUA AND BARBUDA
- ( 20 ) EGYPT
- ( 30 ) GREECE
- ( 31 ) NETHERLANDS
- ( 32 ) BELGIUM
- ( 33 ) FRANCE
- ( 34 ) SPAIN
- ( 36 ) HUNGARY
- ( 39 ) ITALY
- ( 40 ) ROMANIA
- ( 41 ) SWITZERLAND
- ( 43 ) AUSTRIA
- ( 44 ) UNITED KINGDOM
- ( 45 ) DENMARK
- ( 46 ) SWEDEN
- ( 47 ) NORWAY
- ( 48 ) POLAND
- ( 49 ) GERMANY
- ( 60 ) MALAYSIA
- ( 61 ) AUSTRALIA
- ( 62 ) INDONESIA
- ( 63 ) PHILIPPINES
- ( 64 ) NEW ZEALAND
- ( 65 ) SINGAPORE
- ( 66 ) THAILAND
- ( 81 ) JAPAN
- ( 82 ) SOUTH KOREA
- ( 84 ) VIETNAM
- ( 86 ) CHINA
- ( 90 ) TURKEY
- ( 91 ) INDIA
- ( 92 ) PAKISTAN
- ( 94 ) SRI LANKA
- ( 95 ) MYANMAR
- （以及其他国家代码）

---

## 二、旅行信息 (Traveling Information)

### 重要提示
**注意**: 您的旅行必须在3天内（包括提交日期）

### 日历控件字段

#### 11. 到达日期 (Date of Arrival)
- **字段ID**: `arrDt`
- **字段类型**: 日期选择器 (日历控件)
- **必填**: 是 (*)
- **格式**: DD/MM/YYYY
- **属性**: 只读 (readonly)
- **占位符**: "DD/MM/YYYY"

#### 12. 出发日期 (Date of Departure)
- **字段ID**: `depDt`
- **字段类型**: 日期选择器 (日历控件)
- **必填**: 是 (*)
- **格式**: DD/MM/YYYY
- **属性**: 只读 (readonly)
- **占位符**: "DD/MM/YYYY"

### 文本输入字段

#### 13. 航班/船舶/交通工具号码 (Flight / Vessel / Transportation No.)
- **字段ID**: `vesselNm`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 30字符
- **限制**: 数字、字符和住宿相关特殊字符，不允许粘贴
- **CSS类**: `form-control uppercase`

### 下拉菜单字段

#### 14. 旅行方式 (Mode of Travel)
- **字段ID**: `trvlMode`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase trvlMode`

**选项**:
- 空选项: "Please Choose"
- 值 "1": AIR (空运)
- 值 "2": LAND (陆运)
- 值 "3": SEA (海运)

#### 15. 到达马来西亚前的最后登船港 (Last Port of Embarkation before Malaysia)
- **字段ID**: `embark`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase embark`
- **选项**: 与国籍字段相同的国家/地区列表（约280个选项）

### 住宿信息字段

#### 16. 住宿类型 (Accommodation of Stay)
- **字段ID**: `accommodationStay`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase`

**选项**:
- 空选项: "Please Choose"
- 值 "01": HOTEL/MOTEL/REST HOUSE
- 值 "02": RESIDENCE OF FRIENDS/RELATIVES
- 值 "99": OTHERS

#### 17. 地址（在马来西亚）第一行 (Address In Malaysia - Line 1)
- **字段ID**: `accommodationAddress1`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 100字符
- **限制**: 仅字母数字字符，不允许粘贴
- **验证**: 地址验证函数
- **CSS类**: `form-control uppercase`

#### 18. 地址（在马来西亚）第二行 (Address In Malaysia - Line 2)
- **字段ID**: `accommodationAddress2`
- **字段类型**: 文本输入框
- **必填**: 否
- **最大长度**: 100字符
- **限制**: 仅字母数字字符，不允许粘贴
- **验证**: 地址验证函数
- **CSS类**: `form-control uppercase`

#### 19. 州属 (State)
- **字段ID**: `accommodationState`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase`
- **功能**: 选择后会触发城市列表更新

**选项**:
- 空选项: "Please Choose"
- 值 "01": JOHOR
- 值 "02": KEDAH
- 值 "03": KELANTAN
- 值 "04": MELAKA
- 值 "05": NEGERI SEMBILAN
- 值 "06": PAHANG
- 值 "07": PULAU PINANG
- 值 "08": PERAK
- 值 "09": PERLIS
- 值 "10": SELANGOR
- 值 "11": TERENGGANU
- 值 "12": SABAH
- 值 "13": SARAWAK
- 值 "14": WP KUALA LUMPUR
- 值 "15": WP LABUAN
- 值 "16": WP PUTRAJAYA

#### 20. 城市 (City)
- **字段ID**: `accommodationCity`
- **字段类型**: 下拉选择框
- **必填**: 是 (*)
- **CSS类**: `form-control uppercase accommodationCity`
- **功能**: 选择后会触发邮政编码更新
- **选项**: 根据所选州属动态加载

#### 21. 邮政编码 (Postcode)
- **字段ID**: `accommodationPostcode`
- **字段类型**: 文本输入框
- **必填**: 是 (*)
- **最大长度**: 5字符
- **限制**: 仅数字和特殊字符
- **CSS类**: `form-control uppercase accommodationPostcode`

---

## 三、提交按钮和控制按钮

#### 22. 提交按钮 (Submit)
- **字段ID**: `submit`
- **字段类型**: 提交按钮
- **CSS类**: `btn btn-primary toggle-loading`
- **功能**: 验证表单并提交
- **值**: "Submit"

#### 23. 重置按钮 (Reset)
- **字段ID**: `reset`
- **字段类型**: 提交按钮
- **CSS类**: `btn btn-info toggle-loading`
- **功能**: 重置搜索/清空表单
- **值**: "reset"

---

## 四、隐藏字段和安全字段

#### 24. 当前语言
- **字段名**: `hdCurrLang`
- **字段ID**: `hdCurrLang`
- **字段类型**: 隐藏字段
- **值**: "ms" (马来语)

#### 25. MDAC签证国家
- **字段名**: `mdacVisaCountry`
- **字段ID**: `mdacVisaCountry`
- **字段类型**: 隐藏字段
- **值**: "ALL"

#### 26. 源页面安全令牌
- **字段名**: `_sourcePage`
- **字段类型**: 隐藏字段
- **值**: 动态生成的安全令牌

#### 27. 指纹安全令牌
- **字段名**: `__fp`
- **字段类型**: 隐藏字段
- **值**: 动态生成的安全指纹

---

## 五、表单验证和限制

### JavaScript验证函数
1. `isCharacterKey(event)` - 仅允许字符输入
2. `isNumberCharacterKey(event)` - 允许数字和字符输入
3. `isNumberSpecialKey(event)` - 允许数字和特殊字符输入
4. `isNumberCharacterKeyAccom(event)` - 住宿相关的字符输入限制
5. `checkEmail()` - 邮箱格式验证
6. `validateEmail()` - 邮箱匹配验证
7. `validateAddress(id)` - 地址验证
8. `validateSubmit()` - 提交前总体验证

### 动态功能
1. `showAccommodation(value)` - 根据国籍显示住宿选项
2. `retrieveCountryPhone(value)` - 根据国籍获取电话代码
3. `retrieveRefCity(value)` - 根据州属获取城市列表
4. `retrievePostcode(value)` - 根据城市获取邮政编码

---

## 六、表单提交信息

- **表单方法**: POST
- **表单名称**: `permohonan`
- **提交地址**: `/mdac/register`
- **表单验证**: 客户端JavaScript验证 + 服务器端验证

---

## 七、技术特性

### 响应式设计
- 使用Bootstrap框架
- 支持移动设备和桌面设备
- 使用col-md-*类进行响应式布局

### 安全特性
1. 防止粘贴 (onpaste="return false")
2. 防止复制 (oncopy="return false")  
3. 字符长度限制
4. 输入类型限制
5. 安全令牌验证
6. 服务器端验证

### 用户体验
1. 实时表单验证
2. 日历控件用于日期选择
3. 级联下拉菜单（州属->城市->邮政编码）
4. 加载动画效果
5. 错误提示和验证反馈

---

*文档生成时间: 2025年1月18日*  
*数据来源: https://imigresen-online.imi.gov.my/mdac/main?registerMain*

---

## 八、各州属城市列表详细信息

### 01 - JOHOR (柔佛州)
**城市数量**: 67个城市

**城市列表**:
- JOHOR
- ASAHAN, JOHOR
- AYER BALOI
- AYER HITAM
- BAKRI
- BATU ANAM
- BATU PAHAT
- BEKOK
- BENUT
- BUKIT GAMBIR
- BUKIT PASIR
- CHAAH
- ENDAU
- GELANG PATAH
- GEMAS
- GERSEK
- G. TAIB ANDAK
- JEMENTAH
- JOHOR BAHRU
- KAHANG
- KLUANG
- KG KNGAN T DR
- KOTA TINGGI
- KUKUP
- KULAI
- LABIS
- LAYANG-LAYANG
- MASAI
- MERSING
- MUAR
- PAGOH
- PALOH
- PANCHOR
- PARIT JAWA
- PARIT RAJA
- PARIT SULONG
- PASIR GUDANG
- PEKAN NANAS
- PENGERANG
- PONTIAN
- RENGIT
- SEGAMAT
- SKUDAI
- SEMERAH
- SENAI
- SENGGARANG
- SIMPANG RENGGAM
- SUNGAI MATI
- TANGKAK
- ULU TIRAM
- YONG PENG
- SAGIL
- BUKIT KEPONG
- LENGA
- SUNGAI BALANG
- GEMAS BARU
- RENGGAM
- BANDAR PENAWAR
- LEDANG
- ISKANDAR PUTERI
- KULAI JAYA
- KANGKAR PULAI

### 02 - KEDAH (吉打州)
**城市数量**: 37个城市

**城市列表**:
- KEDAH
- ALOR SETAR
- BALING
- BANDAR BAHARU
- BEDONG
- GURUN
- JITRA
- KARANGAN
- KEPALA BATAS
- KODIANG
- KOTA KUALA MUDA
- KUALA KEDAH
- KUALA KETIL
- KUALA NERANG
- KULIM
- KUPANG
- LANGGAR
- LANGKAWI
- LUNAS
- MERBOK
- PADANG SERAI
- PENDANG
- POKOK SENA
- SERDANG
- SIK
- SUNGAI PETANI
- YAN
- CHANGLOON
- SINTOK
- SG. BAKAP
- BUKIT KAYU HITAM
- SIMPANG EMPAT
- KOTA SARANG SEMUT
- JENIANG
- SINTOK

### 03 - KELANTAN (吉兰丹州)
**城市数量**: 22个城市

**城市列表**:
- KELANTAN
- AYER LANAS
- BACHOK
- CHERANG RUKU
- DABONG
- GUA MUSANG
- JELIK DESA PAHLAWAN
- KETEREH
- KOTA BHARU
- KUALA KRAI
- MACHANG
- MELOR
- PASIR MAS
- PASIR PUTEH
- PULAI CHONDONG
- RANTAU PANJANG
- TANAH MERAH
- TEMANGAN
- TUMPAT
- WAKAF BHARU
- SELISING
- KUALA BALAH

### 04 - MELAKA (马六甲州)
**城市数量**: 21个城市

**城市列表**:
- MELAKA
- ALOR GAJAH
- ASAHAN
- BATANG MELAKA
- BEMBAN
- DURIAN TUNGGAL
- GEMAS
- JASIN
- K/SUNGAI BARU
- LUBOK CHINA
- MASJID TANAH
- MELAKA
- MERLIMAU
- SUNGAI RAMBAI
- SUNGAI UDANG
- TAMPIN
- TANJONG KELING
- SELANDAR
- BUKIT KATIL
- AYER KEROH
- BATU BERENDAM

### 05 - NEGERI SEMBILAN (森美兰州)
**城市数量**: 51个城市

**城市列表**:
- N SEMBILAN
- ASAHAN
- BAHAU
- BATANG MELAKA
- BATU KIKIR
- DURIAN TIPUS
- GEMAS
- GEMENCHEH
- JOHOL
- KOTA
- KUALA KLAWANG
- KUALA PILAH
- LABU
- LENGGENG
- LINGGI
- LUBOK CHINA
- MANTIN
- NILAI
- PASIR PANJANG
- PEDAS
- PORT DICKSON
- RANTAU
- REMBAU
- ROMPIN
- SELIAU
- SEREMBAN
- SRI MENANTI
- SI RUSA
- SILIAU
- SIMPANG PERTANG
- SUNGAI GADUT
- TAMPIN
- TANJUNG IPOH
- TITI
- BUKIT PELANDOK
- CHUAH
- LUKUT
- TELUK KEMANG
- PENG.KEMPAS
- JELEBU
- BANDAR SERI JEMPOL
- JUAS
- SEHB BARU JEMPOL
- BANDAR ENSTEK
- PUSAT BANDAR PALONG

### 06 - PAHANG (彭亨州)
**城市数量**: 39个城市

**城市列表**:
- PAHANG
- BANDAR JENGKA
- BENTA
- BENTONG
- BESERAH
- BUKIT FRASER
- CHENOR
- DONG
- GAMBANG
- GENTING
- JERANTUT
- KARAK
- KEMAYAN
- KUALA LIPIS
- KUALA ROMPIN
- KUANTAN
- LANCHANG
- LURAH BILUT
- MARAN
- MENGKARAK
- MENTAKAB
- MERSING
- MUADZAM SHAH
- PADANG TENGKU
- PEKAN
- RAUB
- RINGLET
- SEGAMAT
- SUNGAI LEMBING
- SUNGAI RUAN
- TANAH RATA
- TEMERLOH
- TRIANG
- BDR TUN RAZAK
- ROMPIN
- C HIGHLANDS
- BANDAR BERA
- SEGA
- BRINCHANG

### 07 - PULAU PINANG (槟城州)
**城市数量**: 25个城市

**城市列表**:
- P PINANG
- AYER ITAM
- BALIK PULAU
- BATU FERRINGHI
- BAYAN LEPAS
- BUKIT MERTAJAM
- BUTTERWORTH
- GELUGOR
- GEORGETOWN
- KEPALA BATAS
- NIBONG TEBAL
- PENAGA
- PENANG HILL
- PERAI
- PERMATANG PAUH
- PULAU PINANG
- SIMPANG AMPAT
- SUNGAI JAWI
- TANJONG BUNGA
- TASEK GELUGOR
- USM P/PINANG
- SUNGAI BAKAP
- JELUTONG
- MINDEN
- TANJONG TOKONG
- BAYAN BARU

### 08 - PERAK (霹雳州)
**城市数量**: 88个城市

**城市列表**:
- PERAK
- AYER TAWAR
- BAGAN DATOH
- BAGAN SERAI
- BATU GAJAH
- BATU KURAU
- BERUAS
- BIDOR
- BOTACH
- KT KERUING
- CHEMOR
- CHENDERIANG
- CHENDERONG BALAI
- ENGGOR
- GERIK
- GOPENG
- HUTAN MELINTANG
- KLIAN INTAN
- IPOH
- KAMPAR
- KAMPONG GAJAH
- KG KEPAYANG
- KAMUNTING
- KUALA KANGSAR
- KUALA SEPETANG
- LAHAT
- LANGKAP
- LENGGONG
- LUMUT
- MALIM NAWAR
- MAMBANG DIAWAN
- MANONG
- MATANG
- MENGLEMBU
- PADANG RENGAS
- PANGKOR
- PANTAI REMIS
- PARIT
- PARIT BUNTAR
- PENGKALAN HULU
- PUSING
- SAUK
- SELAMA
- SELEKOH
- SEMANGGOL
- SIMPANG
- SIMPANG AMPAT
- SITIAWAN
- SLIM RIVER
- SUNGAI SIPUT
- SUNGAI SUMUN
- SUNGKAI
- TAIPING
- TANJUNG MALIM
- TG.PIANDANG
- TG.RAMBUTAN
- TANJONG TUALANG
- TAPAH
- TAPAH ROAD
- TELUK INTAN
- TEMOH
- TRONG
- TERONOH
- ULU KINTA
- ULU BERNAM
- SG SIPUT (U)
- BEHRANG STE
- SENTROL
- AKKU
- KUALA DIPANG
- KUALA KURAU
- SERI MANJUNG
- RANTAU PANJANG
- CHIKUS

### 09 - PERLIS (玻璃市州)
**城市数量**: 6个城市

**城市列表**:
- PERLIS
- ARAU
- KAKI BUKIT
- KANGAR
- KUALA PERLIS
- PADANG BESAR
- SIMPANG AMPAT

### 10 - SELANGOR (雪兰莪州)
**城市数量**: 65个城市

**城市列表**:
- SELANGOR
- AMPANG
- BANTING
- BTG.BERJUNTAI
- BATANG KALI
- BATU 9 CHERAS
- BATU ARANG
- BATU CAVES
- BERANANG
- BUKIT ROTAN
- DENGKIL
- HULU LANGAT
- JENJAROM
- JERAM
- KAJANG
- KAPAR
- KLANG
- KERLING
- K. KUBU BHARU
- KUALA LUMPUR
- KUALA SELANGOR
- NILAI
- MANTIN
- PELABUHAN KLANG
- PETALING JAYA
- PUCHONG
- PULAU CAREY
- PULAU KETAM
- PULAU INDAH
- RANTAU PANJANG
- RASA
- RAWANG
- SABAK BERNAM
- SEKINCHAN
- SEMENYIH
- SEPANG
- SERENDAH
- SERI KEMBANGAN
- SHAH ALAM
- SUBANG JAYA
- SG AYER TAWAR
- SUNGAI BESAR
- SUNGAI BULOH
- SUNGAI PELEK
- TANJONG KARANG
- TANJONG SEPAT
- TELOK P. GARANG
- UKM BANGI
- UPM SERDANG
- HULU BERNAM
- BDR. BARU BANGI
- PUTRAJAYA(SEBELUM 1.2.2001)
- CYBERJAYA
- SELAYANG
- BANDAR BARU SUNGAI BULOH
- ULU YAM BARU
- GOMBAK
- SERDANG
- CHERAS
- BANDAR PUNCAK ALAM
- BANDAR PUNCAK ALAM

### 11 - TERENGGANU (登嘉楼州)
**城市数量**: 21个城市

**城市列表**:
- TERENGGANU
- AJIL
- BESUT
- BUKIT BESI
- DUNGUN
- JERTIH
- BDR PERMAISURI
- KEMAMAN
- KEMASE
- KIJAL
- KUALA BERANG
- KUALA BESUT
- KUALA TERENGGANU
- MARANG
- PAKA
- KERTEH
- MUKTAFI BILLAH SHAH
- KAMPONG RAJA
- CUKAIAIR
- KUALA NERUS
- BANDAR KETENGAH JAYA

### 12 - SABAH (沙巴州)
**城市数量**: 32个城市

**城市列表**:
- SABAH
- BEAUFORT
- BELURAN
- BONGAWAN
- INANAM
- KENINGAU
- KOTA BELUD
- KOTA KINABALU
- KOTA MARUDU
- KUALA PENYU
- KUDAT
- KUNAK
- LAHAD DATU
- LIKAS
- MEMBAKUT
- MENUMBOK
- NABAWAN
- PAPAR
- PENAMPANG
- RANAU
- SANDAKAN
- SEMPORNA
- SIPITANG
- TAMBUNAN
- TAMPARULI
- TANJUNG ARU
- TAWAU
- TENOM
- TUARAN
- K. KINABATANGAN
- PUTATAN
- TELUPID

### 13 - SARAWAK (砂拉越州)
**城市数量**: 68个城市

**城市列表**:
- SARAWAK
- ASAJAYA
- BALINGIAN
- BARAM
- BAU
- BEKENU
- BELAGA
- BELAWAI
- BETONG
- BINTANGOR
- BINTULU
- DALAT
- DARO
- DEBAK
- ENGKILILI
- JULAU
- KABONG
- KANOWIT
- KAPIT
- KOTA SAMARAHAN
- KUCHING
- LAWAS
- LIMBANG
- LINGGA
- LONG LAMA
- LUBOK ANTU
- LUNDU
- MATU
- MIRI
- MUKAH
- NANGA MEDAMIT
- NIAH
- PAKAN
- PUSA
- ROBAN
- SARATOK
- SARIKEI
- SEBAUH
- SEBUYAU
- SERIAN
- SIMUNJAN
- SONG
- SPAOH
- SRI AMAN
- SUNDAR
- TATAU
- TEBEDU
- TUBAO
- PAKAN
- SIBU
- BINTANGOR
- TEBEDU
- PAKAN
- SIBU
- TIOYALA
- PANGAN TERBANG KOTA KINABALU
- MARUDU
- SIBURAN

### 14 - WP KUALA LUMPUR (吉隆坡联邦直辖区)
**城市数量**: 3个城市

**城市列表**:
- W.PERSEKUTUAN
- KUALA LUMPUR
- CHERAS
- BATU CAVES

### 15 - WP LABUAN (纳闽联邦直辖区)
**城市数量**: 1个城市

**城市列表**:
- W.P LABUAN

### 16 - WP PUTRAJAYA (布城联邦直辖区)
**城市数量**: 1个城市

**城市列表**:
- PUTRAJAYA

---

## 九、城市选择功能说明

### 动态加载机制
- 当用户选择州属时，系统会通过AJAX调用`retrieveRefCity(value)`函数
- 函数会根据州属代码动态加载对应的城市列表
- 城市选择框会自动更新显示相应州属的所有城市选项

### 级联选择流程
1. **选择州属** → 触发城市列表更新
2. **选择城市** → 触发邮政编码更新（通过`retrievePostcode(value)`函数）
3. **邮政编码** → 可手动输入或自动填充

### 技术实现
- **JavaScript函数**: `retrieveRefCity(value)` - 获取城市列表
- **JavaScript函数**: `retrievePostcode(value)` - 获取邮政编码
- **数据源**: 服务器端数据库，包含完整的州属-城市-邮政编码映射关系
- **请求方式**: AJAX异步请求，确保用户体验流畅

### 数据统计
- **总州属数量**: 16个（13个州 + 3个联邦直辖区）
- **总城市数量**: 约450个城市
- **最多城市的州属**: 霹雳州（88个城市）
- **最少城市的州属**: 纳闽和布城（各1个城市）

---

*文档更新时间: 2025年1月18日*  
*数据来源: https://imigresen-online.imi.gov.my/mdac/main?registerMain*
