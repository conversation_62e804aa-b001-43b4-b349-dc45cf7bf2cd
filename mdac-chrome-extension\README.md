# MDAC Chrome侧边栏扩展

> 马来西亚数字入境卡智能填充助手 - Chrome侧边栏版本

## 🚀 功能特性

- **📱 侧边栏界面**: 在浏览器侧边栏中常驻，随时可用
- **🤖 AI智能解析**: 使用Gemini AI自动解析旅客信息
- **⚡ 一键填充**: 直接在MDAC官网页面自动填充表单
- **💾 数据持久化**: 自动保存用户数据，下次使用更便捷
- **🔒 安全可靠**: 所有数据仅在本地存储，不上传到服务器
- **🌐 实时同步**: 与MDAC官网实时交互，支持级联字段

## 📦 安装方法

### 开发者模式安装

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `mdac-chrome-extension` 文件夹

4. **验证安装**
   - 扩展图标应该出现在工具栏中
   - 点击图标可以打开侧边栏

## 🎯 使用方法

### 基本流程

1. **访问MDAC官网**
   ```
   https://imigresen-online.imi.gov.my/mdac/main?registerMain
   ```

2. **打开侧边栏**
   - 点击浏览器工具栏中的MDAC扩展图标
   - 侧边栏将在右侧打开

3. **输入旅客信息**
   - 在"智能解析输入"区域输入旅客信息
   - 支持自然语言描述，例如：
     ```
     李明，中国护照G12345678，1990年1月1日出生，男性
     邮箱：<EMAIL>，手机：+60123456789
     计划2025年8月1日到达马来西亚，8月7日离开
     乘坐MH123航班，住宿地址：吉隆坡Hotel KL City Center
     ```

4. **AI解析**
   - 点击"🧠 AI解析"按钮
   - AI将自动提取信息并填充到结构化表单中

5. **检查和修正**
   - 检查自动填充的信息是否正确
   - 手动修正任何错误或遗漏的信息

6. **执行填充**
   - 点击"🚀 生成并执行脚本"按钮
   - 扩展将自动在MDAC页面填充表单

### 高级功能

#### 📝 手动填写
- 可以跳过AI解析，直接在结构化表单中手动填写信息
- 所有字段都支持实时验证

#### 👁️ 脚本预览
- 点击"👁️ 预览脚本"查看生成的JavaScript代码
- 可以复制脚本到控制台手动执行

#### 💾 数据保存
- 点击"💾 保存数据"将当前信息保存到本地
- 下次使用时会自动加载保存的数据

#### 📋 示例数据
- 点击"📝 填入示例"快速填充测试数据
- 用于演示和测试功能

## 🔧 技术架构

### 文件结构
```
mdac-chrome-extension/
├── manifest.json          # 扩展配置文件
├── background.js           # Service Worker后台脚本
├── sidepanel.html         # 侧边栏页面
├── sidepanel.js           # 侧边栏主逻辑
├── sidepanel.css          # 侧边栏样式
├── content.js             # 内容脚本
├── utils/                 # 工具模块
│   ├── gemini-api.js      # Gemini API封装
│   ├── form-mapper.js     # 表单字段映射
│   └── data-validator.js  # 数据验证
└── icons/                 # 图标文件
```

### 核心技术
- **Manifest V3**: 最新的Chrome扩展标准
- **Side Panel API**: Chrome侧边栏API
- **Service Workers**: 后台处理和消息传递
- **Content Scripts**: 页面内容操作
- **Chrome Storage API**: 数据持久化
- **Gemini AI API**: 智能信息提取

## 🛠️ 开发指南

### 本地开发

1. **克隆代码**
   ```bash
   git clone <repository-url>
   cd mdac-chrome-extension
   ```

2. **修改API密钥**
   - 编辑 `utils/gemini-api.js`
   - 替换默认的Gemini API Key

3. **加载到Chrome**
   - 按照上述安装方法加载扩展

4. **调试**
   - 右键扩展图标 → "检查弹出窗口"调试侧边栏
   - 在扩展管理页面点击"背景页"调试Service Worker
   - 在MDAC页面按F12调试Content Script

### 修改和扩展

#### 添加新字段
1. 在 `utils/form-mapper.js` 中添加字段映射
2. 在 `utils/data-validator.js` 中添加验证规则
3. 在 `sidepanel.html` 中添加表单元素
4. 在 `sidepanel.js` 中添加处理逻辑

#### 修改AI提示词
- 编辑 `utils/gemini-api.js` 中的 `buildExtractionPrompt` 方法

#### 修改样式
- 编辑 `sidepanel.css` 文件

## 🔒 隐私和安全

### 数据处理
- **本地存储**: 所有用户数据仅存储在本地浏览器中
- **不上传**: 除AI解析外，不向任何服务器发送数据
- **可清除**: 用户可随时清除所有保存的数据

### API使用
- **Gemini API**: 仅用于信息提取，不保存用户数据
- **MDAC官网**: 仅在用户授权下填充表单

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 本地数据存储
- `scripting`: 在MDAC页面执行脚本
- `sidePanel`: 显示侧边栏

## 📞 支持和反馈

### 常见问题

**Q: 为什么无法连接到MDAC网站？**
A: 请确保访问的是官方MDAC网站：`https://imigresen-online.imi.gov.my/mdac/`

**Q: AI解析失败怎么办？**
A: 可以手动填写表单，或检查网络连接和API密钥配置

**Q: 填充后某些字段为空？**
A: 这是正常现象，部分字段可能需要手动选择或填写

**Q: 如何更新扩展？**
A: 重新下载最新版本并重新加载扩展

### 问题报告
如果遇到问题，请提供以下信息：
1. Chrome版本
2. 扩展版本
3. 错误的详细描述
4. 浏览器控制台错误信息

## 📄 许可证

本项目仅供学习和个人使用，请遵守相关法律法规。

---

**版本**: v1.0.0  
**更新时间**: 2025年9月11日  
**兼容性**: Chrome 88+
