# 多平台 AI API 速度测试工具

这## 🚀 使用方法

### 快速开始
1. **打开页面**: 直接打开 `moonshot-api-speed-test.html` 文件
2. **自动配置**: API 密钥已预先配置，无需手动输入
3. **选择平台**: 在"AI 平台"下拉菜单中选择要测试的平台
4. **开始测试**: 选择测试类型（文本/视觉）并运行测试

### 平台选择
- **Moonshot AI**: 使用 Moonshot 平台进行测试
- **阿里云 DashScope**: 使用阿里云平台进行测试

### API 密钥配置
✅ **配置完成**: API 密钥已完全配置，开箱即用
- **Moonshot API Key**: 已配置并可以直接使用
- **阿里云 API Key**: 已配置并可以直接使用

#### 修改 API 密钥
如果需要更换 API 密钥，请编辑 `moonshot-api-speed-test.html` 文件中的 `HARDCODED_API_KEYS` 配置。

```javascript
const HARDCODED_API_KEYS = {
    moonshot: 'sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY',
    aliyun: 'sk-e5619d5a3c7c4d6793b9decc743cea9f'  // 阿里云API密钥
};
```

**注意**: 
- Moonshot API Key 已配置并可以直接使用
- 阿里云 API Key 需要替换为有效的密钥才能正常工作
- API 密钥输入框已设为只读模式，防止意外修改

### 测试流程
1. **文本测试**: 使用预设的测试文本或输入自定义文本
2. **视觉测试**: 上传图片文件，选择合适的视觉模型进行测试
3. **批量测试**: 运行多次测试获取平均性能数据

### 性能监控
- 实时响应时间统计
- 成功率计算
- 平均响应时间跟踪
- 最快/最慢响应记录和阿里云 DashScope API 响应速度和性能的现代化 Web 页面。

## 🎯 新增功能

### 多平台支持
- **Moonshot AI**: 支持文本和视觉 API 测试
- **阿里云 DashScope**: 支持通义千问系列模型的文本和视觉 API 测试

### 支持的阿里云模型
#### 视觉模型
- **通义千问VL Max** (`qwen-vl-max-latest`) - 旗舰版，能力最强
- **通义千问VL Plus** (`qwen-vl-plus-latest`) - 平衡版，性价比高
- **QVQ Max** (`qvq-max-latest`) - 视觉推理旗舰版
- **QVQ Plus** (`qvq-plus-latest`) - 视觉推理平衡版
- **通义千问OCR** (`qwen-vl-ocr-latest`) - 专业文字识别
- **通义千问Omni** (`qwen-omni-turbo`) - 多模态综合模型

#### 文本模型
- **通义千问Turbo** (`qwen-turbo`) - 阿里云文本模型

## 🚀 使用方法

### 1. 选择平台
1. 在"AI 平台"下拉菜单中选择:
   - **Moonshot AI**: 使用 Moonshot 平台
   - **阿里云 DashScope**: 使用阿里云平台

### 2. 配置 API Key
- **Moonshot**: 输入您的 Moonshot API Key
- **阿里云**: 输入您的阿里云 API Key

### 3. 验证配置
点击"🔍 验证 API Key"确认 Key 有效性

### 4. 加载 API
点击"� 加载 API 模块"初始化对应平台的 API

### 5. 选择模型并测试
- **文本测试**: 使用预设的测试文本或输入自定义文本
- **视觉测试**: 上传图片文件，选择合适的视觉模型进行测试

## 📊 性能对比

### Moonshot AI
- **文本模型**: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
- **视觉模型**: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
- **优势**: 专门的护照/文档处理优化，支持多种上下文长度

### 阿里云 DashScope
- **文本模型**: qwen-turbo (0.0008元/1K tokens)
- **视觉模型**: 多种选择，满足不同需求
- **优势**: 丰富的模型选择，灵活的计费

## 🔧 技术实现

### 平台自动切换
- 选择平台时自动显示对应 API Key 输入框
- 选择视觉模型时自动切换到对应平台

### API 调用方式
- **Moonshot**: 使用现有的 moonshot-api.js 模块
- **阿里云**: 直接调用 DashScope API 接口

### 性能监控
- 记录每个平台的响应时间
- 统计成功率和错误率
- 支持批量测试进行性能对比

## 📈 测试建议

### 文本 API 测试
1. 使用相同的测试文本在两个平台上进行对比
2. 进行批量测试获取更准确的性能数据
3. 对比响应时间和结果质量

### 视觉 API 测试
1. 使用相同的图片文件进行跨平台测试
2. 测试不同模型在同一图片上的表现
3. 对比 OCR 准确率和推理能力

## ⚠️ 注意事项

### API Key 安全
- 请妥善保管您的 API Key
- 避免在公共网络环境中使用
- 定期更换 API Key 确保安全

### 使用限制
- 阿里云模型有免费额度限制
- Moonshot API 可能有速率限制
- 建议在测试时添加适当延迟

### 兼容性
- 支持现代浏览器 (Chrome, Firefox, Safari, Edge)
- 需要 JavaScript 启用
- 推荐使用 HTTPS 环境

## 🔄 更新日志

### v2.0.0 (2025-01-13)
- ✨ 新增阿里云 DashScope 平台支持
- 🎯 添加 6 个阿里云视觉模型
- 🔄 实现平台自动切换功能
- 📊 增强性能对比功能
- 🎨 优化用户界面和交互体验

### v1.0.0 (2025-01-13)
- 🚀 初始版本，支持 Moonshot AI 平台
- 📝 文本 API 测试功能
- 👁️ 视觉 API 测试功能
- 📊 实时性能监控

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个测试工具！

## 📄 许可证

本项目仅用于学习和测试目的。
