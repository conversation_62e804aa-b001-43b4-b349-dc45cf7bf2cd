# MDAC Chrome扩展 - 设置指南

## 🚀 快速开始

### 1. 安装扩展

1. **下载扩展文件**
   - 确保 `mdac-chrome-extension` 文件夹包含所有必要文件

2. **打开Chrome扩展管理页面**
   - 地址栏输入: `chrome://extensions/`
   - 或者: 菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"切换开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `mdac-chrome-extension` 文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 扩展应该出现在扩展列表中
   - 工具栏应该显示MDAC扩展图标

### 2. 配置API密钥（可选）

如果需要使用自己的Gemini API密钥：

1. **获取API密钥**
   - 访问: https://makersuite.google.com/
   - 创建新的API密钥

2. **修改配置**
   - 编辑文件: `utils/gemini-api.js`
   - 找到第4行: `this.apiKey = 'YOUR_API_KEY_HERE';`
   - 替换为你的API密钥

3. **重新加载扩展**
   - 在扩展管理页面点击刷新按钮

## 🎯 使用步骤

### 第一次使用

1. **访问MDAC官网**
   ```
   https://imigresen-online.imi.gov.my/mdac/main?registerMain
   ```

2. **打开侧边栏**
   - 点击浏览器工具栏中的MDAC图标
   - 侧边栏将在右侧打开

3. **检查连接状态**
   - 侧边栏顶部应显示"已连接MDAC网站"
   - 如果显示"未连接"，请刷新MDAC页面

### 智能填充流程

1. **输入旅客信息**
   - 在"智能解析输入"文本框中输入旅客信息
   - 可以使用自然语言，例如：
     ```
     张三，中国护照E12345678，男，1985年3月15日出生
     护照2028年3月15日到期，邮箱***************
     手机+60123456789，8月1日到达马来西亚，8月10日离开
     乘坐AK123航班，住吉隆坡希尔顿酒店，邮编50088
     ```

2. **AI解析**
   - 点击"🧠 AI解析"按钮
   - 等待AI处理（通常10-30秒）
   - 检查自动填充的结构化信息

3. **验证和修正**
   - 检查所有自动填充的字段
   - 手动修正任何错误或补充遗漏信息
   - 特别注意日期格式和拼写

4. **执行填充**
   - 点击"🚀 生成并执行脚本"
   - 观察MDAC页面表单是否正确填充
   - 检查级联字段（如州属和城市）

5. **最终检查**
   - 在MDAC页面检查所有填充的信息
   - 手动调整任何需要修正的字段
   - 提交表单

### 手动填写流程

如果不使用AI解析，可以直接手动填写：

1. **跳过AI解析**
   - 直接在"结构化信息"部分填写各个字段

2. **使用验证提示**
   - 系统会实时验证输入格式
   - 注意红色错误提示并及时修正

3. **保存常用信息**
   - 填写完成后点击"💾 保存数据"
   - 下次使用时信息会自动加载

## ⚙️ 高级设置

### 调试模式

如果遇到问题，可以开启调试：

1. **打开开发者工具**
   - 右键扩展图标 → "检查弹出窗口"
   - 或在侧边栏右键 → "检查"

2. **查看控制台**
   - 切换到Console标签
   - 查看错误信息和调试日志

3. **检查网络请求**
   - 切换到Network标签
   - 查看API请求是否成功

### 数据管理

**查看保存的数据:**
- 开发者工具 → Application → Storage → Chrome Extension

**清除保存的数据:**
- 扩展管理页面 → MDAC扩展 → 详细信息 → 网站设置 → 清除数据

**导出数据:**
- 在开发者工具Console中运行:
  ```javascript
  chrome.storage.sync.get(null, (data) => console.log(data));
  ```

## 🔧 故障排除

### 常见问题

**🚫 侧边栏无法打开**
- 检查是否在MDAC官网页面
- 刷新页面后重试
- 重新加载扩展

**❌ AI解析失败**
- 检查网络连接
- 验证API密钥是否正确
- 尝试简化输入信息

**⚠️ 表单填充不完整**
- 检查MDAC页面是否完全加载
- 手动填写未自动填充的字段
- 特别注意级联字段（州属→城市）

**🔄 连接状态显示"未连接"**
- 确保访问的是正确的MDAC网址
- 刷新MDAC页面
- 检查扩展权限设置

### 错误代码

**Error 400 - API请求错误**
- 检查API密钥格式
- 验证输入信息是否包含特殊字符

**Error 403 - API权限错误**
- API密钥无效或已过期
- 检查API使用配额

**Error 网络错误**
- 检查网络连接
- 尝试刷新页面

### 性能优化

**减少API调用:**
- 尽量使用结构化输入而非AI解析
- 保存常用信息避免重复输入

**提高成功率:**
- 使用标准格式输入信息
- 避免包含特殊字符和符号
- 分步骤填写复杂信息

## 📱 移动设备支持

目前扩展仅支持桌面版Chrome浏览器。如需在移动设备使用：

1. **Android设备:**
   - 使用Chrome桌面版
   - 或使用原版MDAC网站生成器

2. **iOS设备:**
   - 使用Safari访问原版网站生成器
   - 或切换到桌面设备

## 🔒 隐私设置

### 数据控制

**最小化数据收集:**
- 只填写必要字段
- 定期清除保存的数据

**API使用控制:**
- 使用自己的API密钥
- 监控API使用量

### 安全建议

1. **不保存敏感信息:**
   - 护照号码等敏感信息建议每次手动输入

2. **定期清理:**
   - 定期清除浏览器存储数据
   - 及时删除不需要的保存信息

3. **网络安全:**
   - 确保在安全网络环境下使用
   - 避免在公共网络使用

---

**需要帮助？**
- 查看详细日志: 开发者工具 → Console
- 重置扩展: 删除并重新安装
- 联系支持: 提供错误日志和使用场景
