{"permissions": {"allow": ["<PERSON><PERSON>(claude config list)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__streamable-mcp-server__chrome_get_web_content", "mcp__streamable-mcp-server__chrome_screenshot", "mcp__streamable-mcp-server__chrome_get_interactive_elements", "mcp__streamable-mcp-server__chrome_console", "mcp__streamable-mcp-server__get_windows_and_tabs", "mcp__streamable-mcp-server__chrome_navigate", "mcp__streamable-mcp-server__chrome_inject_script", "mcp__streamable-mcp-server__chrome_fill_or_select", "mcp__streamable-mcp-server__chrome_click_element", "Bash(node:*)", "Bash(ls:*)", "<PERSON><PERSON>(timeout 3 sleep 3)", "WebFetch(domain:ai.google.dev)", "WebFetch(domain:platform.moonshot.cn)", "WebSearch", "WebFetch(domain:modelstudio.console.alibabacloud.com)", "Bash(start \"\" \"C:\\Users\\<USER>\\Downloads\\mdac\\moonshot-api-speed-test.html\")", "WebFetch(domain:docs.z.ai)", "WebFetch(domain:help.aliyun.com)", "WebFetch(domain:platform.moonshot.ai)", "WebFetch(domain:docs.litellm.ai)", "WebFetch(domain:www.ciyundata.com)", "<PERSON><PERSON>(move:*)", "<PERSON><PERSON>(dir:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(findstr:*)", "<PERSON><PERSON>(powershell:*)", "Bash(del \"C:\\Users\\<USER>\\Downloads\\mdac\\mdac-chrome-extension\\unified\\ERNIEProcessor.js\")", "Bash(find:*)", "Bash(rm:*)", "Read(//c/Users/<USER>/Downloads/database/driver guide/**)"], "deny": []}}