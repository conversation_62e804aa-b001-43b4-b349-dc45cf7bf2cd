<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC扩展系统状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .status-container { max-width: 800px; margin: 0 auto; }
        .module-status { background: white; margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-ok { border-left: 4px solid #4CAF50; }
        .status-warning { border-left: 4px solid #FF9800; }
        .status-error { border-left: 4px solid #F44336; }
        .module-name { font-weight: bold; font-size: 16px; margin-bottom: 5px; }
        .module-status-text { color: #666; font-size: 14px; }
        .timestamp { color: #999; font-size: 12px; margin-top: 10px; }
        h1 { text-align: center; color: #333; }
        .overall-status { text-align: center; padding: 20px; background: white; border-radius: 8px; margin-bottom: 20px; }
        .status-icon { font-size: 24px; margin-right: 10px; }
        button { padding: 10px 20px; margin: 10px 5px; border: none; border-radius: 4px; cursor: pointer; }
        .refresh-btn { background: #2196F3; color: white; }
        .test-btn { background: #4CAF50; color: white; }
        .error-details { background: #ffebee; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="status-container">
        <h1>🚀 MDAC扩展系统状态检查</h1>

        <div class="overall-status" id="overallStatus">
            <div class="status-icon" id="overallIcon">🔄</div>
            <span id="overallText">正在检查系统状态...</span>
        </div>

        <div style="text-align: center; margin-bottom: 20px;">
            <button class="refresh-btn" onclick="checkSystemStatus()">🔄 刷新状态</button>
            <button class="test-btn" onclick="runFunctionTests()">🧪 功能测试</button>
        </div>

        <div id="moduleStatusList"></div>

        <div class="timestamp" id="timestamp"></div>
    </div>

    <script>
        // 系统模块检查配置
        const MODULES = {
            'MDACExtension': {
                check: () => window.mdacExtension,
                description: '主扩展控制器',
                critical: true
            },
            'UIController': {
                check: () => window.uiController,
                description: '统一UI控制器',
                critical: true
            },
            'UnifiedMultiModalProcessor': {
                check: () => window.UnifiedMultiModalProcessor?._instance || window.unifiedProcessor,
                description: '多模态处理器',
                critical: true
            },
            'FormMapper': {
                check: () => window.formMapper,
                description: '表单字段映射器',
                critical: true
            },
            'FieldMappingValidator': {
                check: () => window.fieldMappingValidator,
                description: '字段映射验证器',
                critical: false
            },
            'SmartPreprocessor': {
                check: () => window.SmartPreprocessor,
                description: '智能预处理器',
                critical: false
            },
            'ImageProcessor': {
                check: () => window.ImageProcessor,
                description: '图片处理器',
                critical: false
            },
            'FileProcessor': {
                check: () => window.FileProcessorInstance,
                description: '文件处理器',
                critical: false
            },
            'MDACFieldConstants': {
                check: () => window.MDAC_FIELDS,
                description: 'MDAC字段常量',
                critical: false
            }
        };

        function checkSystemStatus() {
            const statusList = document.getElementById('moduleStatusList');
            statusList.innerHTML = '';

            let criticalErrors = 0;
            let warnings = 0;
            let totalModules = 0;

            for (const [moduleName, config] of Object.entries(MODULES)) {
                totalModules++;
                const statusDiv = document.createElement('div');
                statusDiv.className = 'module-status';

                try {
                    const module = config.check();
                    if (module) {
                        statusDiv.classList.add('status-ok');
                        statusDiv.innerHTML = `
                            <div class="module-name">✅ ${moduleName}</div>
                            <div class="module-status-text">${config.description} - 正常加载</div>
                        `;
                    } else {
                        if (config.critical) {
                            statusDiv.classList.add('status-error');
                            criticalErrors++;
                            statusDiv.innerHTML = `
                                <div class="module-name">❌ ${moduleName}</div>
                                <div class="module-status-text">${config.description} - 未加载（关键模块）</div>
                            `;
                        } else {
                            statusDiv.classList.add('status-warning');
                            warnings++;
                            statusDiv.innerHTML = `
                                <div class="module-name">⚠️ ${moduleName}</div>
                                <div class="module-status-text">${config.description} - 可选模块未加载</div>
                            `;
                        }
                    }
                } catch (error) {
                    statusDiv.classList.add('status-error');
                    criticalErrors++;
                    statusDiv.innerHTML = `
                        <div class="module-name">❌ ${moduleName}</div>
                        <div class="module-status-text">${config.description} - 检查失败</div>
                        <div class="error-details">${error.message}</div>
                    `;
                }

                statusList.appendChild(statusDiv);
            }

            // 更新整体状态
            updateOverallStatus(criticalErrors, warnings, totalModules);

            // 更新时间戳
            document.getElementById('timestamp').textContent = `最后检查: ${new Date().toLocaleString()}`;
        }

        function updateOverallStatus(criticalErrors, warnings, totalModules) {
            const overallDiv = document.getElementById('overallStatus');
            const iconDiv = document.getElementById('overallIcon');
            const textDiv = document.getElementById('overallText');

            if (criticalErrors === 0) {
                if (warnings === 0) {
                    iconDiv.textContent = '✅';
                    textDiv.textContent = '所有模块正常加载';
                    overallDiv.style.background = '#e8f5e8';
                } else {
                    iconDiv.textContent = '⚠️';
                    textDiv.textContent = `核心模块正常，${warnings}个可选模块未加载`;
                    overallDiv.style.background = '#fff8e1';
                }
            } else {
                iconDiv.textContent = '❌';
                textDiv.textContent = `${criticalErrors}个关键模块加载失败`;
                overallDiv.style.background = '#ffebee';
            }
        }

        function runFunctionTests() {
            console.log('🧪 开始功能测试...');

            const tests = [
                {
                    name: '字段映射验证',
                    test: () => {
                        if (window.fieldMappingValidator) {
                            const result = window.fieldMappingValidator.validateAllMappings();
                            console.log('字段映射验证结果:', result);
                            return result ? '通过' : '失败';
                        }
                        return '不可用';
                    }
                },
                {
                    name: '表单映射器',
                    test: () => {
                        if (window.formMapper) {
                            const mappings = window.formMapper.getAllMappings();
                            const count = Object.keys(mappings).length;
                            console.log(`找到 ${count} 个字段映射`);
                            return count > 0 ? '通过' : '失败';
                        }
                        return '不可用';
                    }
                },
                {
                    name: '多模态处理器',
                    test: () => {
                        if (window.UnifiedMultiModalProcessor) {
                            const instance = window.UnifiedMultiModalProcessor.getInstance();
                            console.log('多模态处理器实例:', instance ? '可用' : '不可用');
                            return instance ? '通过' : '失败';
                        }
                        return '不可用';
                    }
                },
                {
                    name: 'UI控制器',
                    test: () => {
                        if (window.uiController) {
                            const fileStatus = window.uiController.getFileStatus ? window.uiController.getFileStatus() : null;
                            console.log('UI控制器文件状态:', fileStatus);
                            return '通过';
                        }
                        return '不可用';
                    }
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    console.log(`✅ ${test.name}: ${result}`);
                } catch (error) {
                    console.error(`❌ ${test.name} 测试失败:`, error);
                }
            });

            alert('功能测试完成，请查看控制台输出了解详细信息');
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', checkSystemStatus);
    </script>
</body>
</html>