// 统一多模态系统与现有系统集成补丁
// 用途：修改现有的AI解析逻辑，支持文件上传的多模态处理
// 依赖：UIController.js, UnifiedMultiModalProcessor.js
// 技术栈：原生JavaScript
// 集成点：sidepanel.js 中的 handleAIParse 方法

(function() {
  'use strict';

  // 等待页面和UIController初始化完成
  function waitForInitialization() {
    return new Promise((resolve) => {
      const checkInit = () => {
        if (window.uiController && window.MDACExtension) {
          resolve();
        } else {
          setTimeout(checkInit, 100);
        }
      };
      checkInit();
    });
  }

  // 重写MDACExtension的handleAIParse方法
  async function patchHandleAIParse() {
    await waitForInitialization();

    // 确保 MDACExtension 类已加载
    if (!window.MDACExtension || !window.MDACExtension.prototype) {
      console.error('❌ MDACExtension 类未加载，跳过补丁');
      return;
    }

    const originalHandleAIParse = window.MDACExtension.prototype.handleAIParse;

    window.MDACExtension.prototype.handleAIParse = async function() {
      console.log('🚀 MDACExtension.handleAIParse 被调用');

      // 检查是否有上传的文件需要处理
      const fileStatus = window.uiController?.getFileStatus();
      console.log('📊 文件状态检查:', fileStatus);

      if (fileStatus?.shouldUseMultiModal) {
        console.log('🔄 检测到文件，开始多模态处理...');
        console.log('📁 文件统计:', {
          totalFiles: fileStatus.totalFiles,
          images: fileStatus.fileCount?.images || 0,
          documents: fileStatus.fileCount?.documents || 0,
          textFiles: fileStatus.fileCount?.textFiles || 0
        });

        try {
          // 使用统一多模态处理器
          const used = await window.uiController.integrateWithExistingAIParse();
          if (used) {
            console.log('✅ 多模态处理完成，跳过文本处理');
            return;
          } else {
            console.log('⚠️ 多模态处理器返回未使用，继续文本处理');
          }
        } catch (error) {
          console.error('❌ 多模态处理失败，回退到原有逻辑:', error);
          console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
          // 回退到原有逻辑
        }
      } else {
        console.log('📝 无文件需要处理，使用原有文本处理逻辑');
      }

      // 没有文件或多模态处理失败，使用原有的文本处理逻辑
      console.log('🔄 开始文本处理流程...');
      try {
        const result = await originalHandleAIParse.call(this);
        console.log('✅ 文本处理完成');
        return result;
      } catch (error) {
        console.error('❌ 文本处理失败:', error);
        throw error;
      }
    };

    console.log('✅ AI解析方法已成功集成多模态处理');
  }

  // 扩展结果处理，支持统一系统的结果显示
  async function enhanceResultHandling() {
    await waitForInitialization();

    console.log('🔧 设置结果处理观察器...');

    // 监听统一系统的结果显示
    const resultObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.target.id === 'resultArea' &&
            mutation.type === 'attributes' &&
            mutation.attributeName === 'style') {

          const resultArea = mutation.target;
          if (resultArea.style.display !== 'none') {
            console.log('🎯 检测到结果显示区域显示');

            // 结果显示时，尝试自动填入表单
            const resultContent = resultArea.querySelector('.result-json pre');
            if (resultContent) {
              try {
                const rawData = resultContent.textContent;
                console.log('📦 解析结果数据，长度:', rawData.length);

                const data = JSON.parse(rawData);
                if (data && typeof data === 'object') {
                  console.log('✅ 数据解析成功，包含字段:', Object.keys(data));

                  // 验证数据映射准确性
                  if (window.fieldMappingValidator && window.formMapper) {
                    console.log('🔍 开始数据映射验证...');
                    const validation = window.fieldMappingValidator.validateDataMapping(data);
                    console.log('🔍 数据映射验证结果:', validation);

                    if (!validation.success) {
                      console.warn('⚠️ 数据映射存在问题:', validation.details);
                    } else {
                      console.log('✅ 数据映射验证通过');
                    }
                  }

                  // 自动填入表单数据
                  if (window.mdacExtension) {
                    console.log('🔄 开始自动填入表单数据...');
                    window.MDACExtension.prototype.applyFieldUpdates.call(
                      window.mdacExtension, data
                    );
                    console.log('✅ 多模态解析结果已自动填入表单');
                  } else {
                    console.warn('⚠️ MDACExtension 未就绪，无法自动填入表单');
                  }
                } else {
                  console.warn('⚠️ 解析的数据不是有效的对象');
                }
              } catch (error) {
                console.error('❌ 解析结果格式错误:', error);
                console.error('错误详情:', {
                  message: error.message,
                  stack: error.stack,
                  name: error.name
                });
              }
            } else {
              console.log('📋 未找到结果内容元素，可能仍在处理中...');
            }
          }
        }
      });
    });

    const resultArea = document.getElementById('resultArea');
    if (resultArea) {
      resultObserver.observe(resultArea, {
        attributes: true,
        attributeFilter: ['style']
      });
      console.log('✅ 结果处理观察器已设置');
    } else {
      console.warn('⚠️ 未找到结果显示区域，观察器设置失败');
    }
  }

  // 增强上传按钮的提示
  function enhanceUploadButtonFeedback() {
    const uploadBtn = document.getElementById('uploadDropzone');
    if (!uploadBtn) return;

    // 添加文件计数提示
    function updateButtonText() {
      if (!window.uiController) return;

      const fileStatus = window.uiController.getFileStatus();
      const baseText = '上传文件';

      if (fileStatus.hasFiles) {
        uploadBtn.textContent = `📁 ${baseText} (${fileStatus.totalFiles})`;
        uploadBtn.classList.add('has-files');
      } else {
        uploadBtn.textContent = `📁 ${baseText}`;
        uploadBtn.classList.remove('has-files');
      }
    }

    // 定期更新按钮状态
    setInterval(updateButtonText, 500);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      #uploadDropzone.has-files {
        background-color: var(--accent-color);
        color: white;
      }

      .upload-status-area {
        margin-top: 15px;
        padding: 15px;
        background: var(--bg-secondary);
        border-radius: 6px;
        border: 1px solid var(--border-color);
      }

      .file-actions {
        margin-top: 10px;
        text-align: right;
      }
    `;
    document.head.appendChild(style);
  }

  // 主初始化函数
  async function initializeIntegration() {
    try {
      console.log('🚀 开始初始化统一多模态系统集成...');
      console.log('🔍 当前环境检查:', {
        hasUIController: !!window.uiController,
        hasMDACExtension: !!window.MDACExtension,
        hasUnifiedProcessor: !!window.UnifiedMultiModalProcessor,
        hasImageProcessor: !!window.ImageProcessor,
        hasFileProcessor: !!window.FileProcessor
      });

      await patchHandleAIParse();
      await enhanceResultHandling();
      enhanceUploadButtonFeedback();

      console.log('✅ 统一多模态系统集成完成');

      // 发布集成完成事件
      window.dispatchEvent(new CustomEvent('unifiedSystemReady', {
        detail: {
          hasUIController: !!window.uiController,
          hasProcessor: !!window.uiController?.getProcessor(),
          hasMDACExtension: !!window.MDACExtension,
          timestamp: Date.now(),
          environment: 'Chrome Extension'
        }
      }));

      console.log('🎉 系统集成事件已发布，等待外部组件响应');

    } catch (error) {
      console.error('❌ 统一多模态系统集成失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      });

      // 尝试继续运行，即使部分集成失败
      console.warn('⚠️ 部分集成失败，尝试继续运行...');
      try {
        enhanceUploadButtonFeedback();
      } catch (fallbackError) {
        console.error('❌ 后备功能也失败:', fallbackError);
      }
    }
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeIntegration);
  } else {
    // 延迟执行，确保其他脚本已加载
    setTimeout(initializeIntegration, 100);
  }

})();