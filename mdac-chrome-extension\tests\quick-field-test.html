<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速字段转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .result-section {
            display: none;
            margin-top: 20px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .test-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 快速字段转换测试</h1>

        <div class="test-section">
            <h3>📋 测试数据</h3>
            <p>以下是从AI返回的示例数据：</p>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>name</td>
                        <td>LIMING</td>
                    </tr>
                    <tr>
                        <td>passNo</td>
                        <td>G12345678</td>
                    </tr>
                    <tr>
                        <td>dob</td>
                        <td>01/01/1990</td>
                    </tr>
                    <tr>
                        <td>passExpDte</td>
                        <td>01/01/2026</td>
                    </tr>
                    <tr>
                        <td>nationality</td>
                        <td>CHN</td>
                    </tr>
                    <tr>
                        <td>sex</td>
                        <td>MALE</td>
                    </tr>
                    <tr>
                        <td>email</td>
                        <td><EMAIL></td>
                    </tr>
                    <tr>
                        <td>region</td>
                        <td>60</td>
                    </tr>
                    <tr>
                        <td>mobile</td>
                        <td>123456789</td>
                    </tr>
                    <tr>
                        <td>arrDt</td>
                        <td>01/08/2025</td>
                    </tr>
                    <tr>
                        <td>depDt</td>
                        <td>07/08/2025</td>
                    </tr>
                    <tr>
                        <td>vesselNm</td>
                        <td>MH123</td>
                    </tr>
                    <tr>
                        <td>accommodationStay</td>
                        <td>HOTEL</td>
                    </tr>
                    <tr>
                        <td>accommodationAddress1</td>
                        <td>Hotel KL City Center</td>
                    </tr>
                    <tr>
                        <td>accommodationPostcode</td>
                        <td>50000</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button class="btn-primary" onclick="testFieldConstants()">测试字段常量</button>
            <button class="btn-primary" onclick="testFieldMapping()">测试字段映射</button>
            <button class="btn-primary" onclick="testUnifiedTransformer()">测试统一转换器</button>
            <button class="btn-secondary" onclick="clearResults()">清除结果</button>

            <div id="testResults" class="result-section">
                <h3>测试结果</h3>
                <div id="resultContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="test-log" id="testLog"></div>
        </div>
    </div>

    <!-- 加载测试依赖 -->
    <script src="../utils/field-constants.js"></script>
    <script src="../utils/field-mapping-config.js"></script>
    <script src="../utils/unified-field-transformer.js"></script>

    <script>
        // 测试数据 - 使用MDAC原生字段名
        const testAIData = {
            name: 'LIMING',
            passNo: 'G12345678',
            dob: '01/01/1990',
            passExpDte: '01/01/2026',
            nationality: 'CHN',
            sex: 'MALE',
            email: '<EMAIL>',
            region: '60',
            mobile: '123456789',
            arrDt: '01/08/2025',
            depDt: '07/08/2025',
            vesselNm: 'MH123',
            accommodationStay: 'HOTEL',
            accommodationAddress1: 'Hotel KL City Center',
            accommodationPostcode: '50000'
        };

        // 日志函数
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('testLog');
            const colorMap = {
                'info': '#e2e8f0',
                'success': '#68d391',
                'warning': '#fbb6ce',
                'error': '#fc8181'
            };
            logDiv.innerHTML += `<div style="color: ${colorMap[level]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 显示测试结果
        function showResults(title, results, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');

            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = `<div class="test-section ${type}"><h4>${title}</h4>${results}</div>`;
        }

        // 测试字段常量
        function testFieldConstants() {
            log('开始测试字段常量...');
            try {
                if (typeof MDACFieldConstants === 'undefined') {
                    throw new Error('MDACFieldConstants 未定义');
                }

                const constants = new MDACFieldConstants();
                log('✅ 字段常量初始化成功');

                // 测试 gender -> sex 映射
                const mappedField = constants.getStandardFieldName('gender');
                log(`📋 gender 映射到: ${mappedField}`);

                // 测试所有字段映射
                let mappingResults = '<h5>字段映射结果:</h5><table class="data-table"><thead><tr><th>原始字段</th><th>标准字段</th><th>值</th></tr></thead><tbody>';

                for (const [field, value] of Object.entries(testAIData)) {
                    const standardField = constants.getStandardFieldName(field);
                    mappingResults += `<tr><td>${field}</td><td>${standardField}</td><td>${value}</td></tr>`;
                    log(`🔍 ${field} -> ${standardField}: ${value}`);
                }
                mappingResults += '</tbody></table>';

                showResults('✅ 字段常量测试通过', mappingResults, 'success');
                log('✅ 字段常量测试完成');

            } catch (error) {
                log(`❌ 字段常量测试失败: ${error.message}`, 'error');
                showResults('❌ 字段常量测试失败', `<p>${error.message}</p>`, 'error');
            }
        }

        // 测试字段映射
        function testFieldMapping() {
            log('开始测试字段映射...');
            try {
                if (typeof MDACFieldMappingConfig === 'undefined') {
                    throw new Error('MDACFieldMappingConfig 未定义');
                }

                const config = new MDACFieldMappingConfig();
                log('✅ 字段映射配置初始化成功');

                // 测试AI到MDAC转换
                const transformedData = config.transformAIToMDAC(testAIData);
                log(`✅ 转换完成，字段数: ${Object.keys(transformedData).length}`);

                // 检查性别字段转换
                const genderValue = transformedData.sex;
                log(`🔍 性别字段转换结果: gender=MALE -> sex=${genderValue}`);

                let mappingResults = '<h5>字段映射结果:</h5><table class="data-table"><thead><tr><th>原始字段</th><th>原始值</th><th>目标字段</th><th>转换值</th></tr></thead><tbody>';

                for (const [aiField, aiValue] of Object.entries(testAIData)) {
                    const mdacField = config.AI_TO_MDAC_MAPPING[aiField];
                    const transformedValue = transformedData[mdacField];
                    mappingResults += `<tr><td>${aiField}</td><td>${aiValue}</td><td>${mdacField || '未映射'}</td><td>${transformedValue || '-'}</td></tr>`;
                }
                mappingResults += '</tbody></table>';

                showResults('✅ 字段映射测试通过', mappingResults, 'success');
                log('✅ 字段映射测试完成');

            } catch (error) {
                log(`❌ 字段映射测试失败: ${error.message}`, 'error');
                showResults('❌ 字段映射测试失败', `<p>${error.message}</p>`, 'error');
            }
        }

        // 测试统一转换器
        function testUnifiedTransformer() {
            log('开始测试统一转换器...');
            try {
                if (typeof UnifiedFieldTransformer === 'undefined') {
                    throw new Error('UnifiedFieldTransformer 未定义');
                }

                const transformer = new UnifiedFieldTransformer();
                log('✅ 统一转换器初始化成功');

                // 测试AI到MDAC转换
                const transformResult = transformer.transformAIToMDAC(testAIData);
                log(`✅ 转换完成，成功: ${transformResult.success}`);

                if (transformResult.success) {
                    log(`📊 转换统计: ${JSON.stringify(transformResult.stats)}`);

                    // 检查性别字段
                    const sexValue = transformResult.data.sex;
                    log(`🔍 性别字段转换结果: ${sexValue}`);

                    let results = `
                        <h5>转换成功!</h5>
                        <p>原始字段数: ${transformResult.stats.sourceFieldCount}</p>
                        <p>目标字段数: ${transformResult.stats.targetFieldCount}</p>
                        <p>转换比率: ${Math.round(transformResult.stats.transformRatio * 100)}%</p>
                        <p>转换耗时: ${transformResult.stats.transformTime}ms</p>
                        <h5>最终转换结果:</h5>
                        <table class="data-table">
                            <thead><tr><th>字段名</th><th>值</th></tr></thead>
                            <tbody>
                    `;

                    for (const [field, value] of Object.entries(transformResult.data)) {
                        results += `<tr><td>${field}</td><td>${value}</td></tr>`;
                    }
                    results += '</tbody></table>';

                    showResults('✅ 统一转换器测试通过', results, 'success');
                    log('✅ 统一转换器测试完成');
                } else {
                    throw new Error(transformResult.error);
                }

            } catch (error) {
                log(`❌ 统一转换器测试失败: ${error.message}`, 'error');
                showResults('❌ 统一转换器测试失败', `<p>${error.message}</p>`, 'error');
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('testLog').innerHTML = '';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('快速字段转换测试页面已加载');
            log('🔍 测试数据包含 ' + Object.keys(testAIData).length + ' 个字段');
        });
    </script>
</body>
</html>