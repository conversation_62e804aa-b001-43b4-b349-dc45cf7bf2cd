<!DOCTYPE html>
<html>
<head>
    <title>MDACFieldConstants 测试</title>
</head>
<body>
    <h1>MDACFieldConstants 测试</h1>
    <div id="test-results"></div>

    <!-- 加载必需的依赖 -->
    <script src="./utils/MDACFieldConstants.js"></script>
    <script src="./utils/unified-field-transformer.js"></script>

    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>测试结果</h2>';

            // 测试 1: MDACFieldConstants 类是否存在
            try {
                if (typeof MDACFieldConstants !== 'undefined') {
                    results.innerHTML += '<p>✅ MDACFieldConstants 类已定义</p>';

                    const constants = new MDACFieldConstants();
                    results.innerHTML += '<p>✅ MDACFieldConstants 实例创建成功</p>';

                    // 测试字段映射
                    if (constants.NATIVE_FIELDS) {
                        results.innerHTML += '<p>✅ NATIVE_FIELDS 已加载</p>';
                    }

                    if (constants.COMPATIBILITY_MAPPING) {
                        results.innerHTML += '<p>✅ COMPATIBILITY_MAPPING 已加载</p>';
                    }

                } else {
                    results.innerHTML += '<p>❌ MDACFieldConstants 类未定义</p>';
                }
            } catch (error) {
                results.innerHTML += `<p>❌ MDACFieldConstants 测试失败: ${error.message}</p>`;
            }

            // 测试 2: UnifiedFieldTransformer 类是否存在
            try {
                if (typeof UnifiedFieldTransformer !== 'undefined') {
                    results.innerHTML += '<p>✅ UnifiedFieldTransformer 类已定义</p>';

                    const transformer = new UnifiedFieldTransformer();
                    results.innerHTML += '<p>✅ UnifiedFieldTransformer 实例创建成功</p>';

                    // 检查状态
                    const status = transformer.getStatus();
                    if (status.constantsLoaded) {
                        results.innerHTML += '<p>✅ 字段常量已加载到转换器</p>';
                    } else {
                        results.innerHTML += '<p>❌ 字段常量未加载到转换器</p>';
                    }

                } else {
                    results.innerHTML += '<p>❌ UnifiedFieldTransformer 类未定义</p>';
                }
            } catch (error) {
                results.innerHTML += `<p>❌ UnifiedFieldTransformer 测试失败: ${error.message}</p>`;
            }

            // 测试 3: 全局常量
            try {
                if (typeof MDAC_FIELDS !== 'undefined') {
                    results.innerHTML += '<p>✅ MDAC_FIELDS 全局常量已定义</p>';
                } else {
                    results.innerHTML += '<p>❌ MDAC_FIELDS 全局常量未定义</p>';
                }

                if (typeof FIELD_CONFIG !== 'undefined') {
                    results.innerHTML += '<p>✅ FIELD_CONFIG 全局常量已定义</p>';
                } else {
                    results.innerHTML += '<p>❌ FIELD_CONFIG 全局常量未定义</p>';
                }
            } catch (error) {
                results.innerHTML += `<p>❌ 全局常量测试失败: ${error.message}</p>`;
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>