<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC字段映射测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .mapped { color: #28a745; }
        .unmapped { color: #dc3545; }
        .transformed { color: #007bff; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .result-section {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>MDAC字段映射测试</h1>

        <div class="test-section">
            <h3>🧪 测试说明</h3>
            <p>此页面用于测试MDAC扩展的字段映射功能，验证AI返回字段能否正确映射到MDAC原生字段。</p>
        </div>

        <div class="test-section">
            <h3>📋 模拟AI返回数据</h3>
            <p>以下是从豆包API返回的示例数据（14个字段）：</p>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>AI返回字段</th>
                        <th>值</th>
                        <th>映射状态</th>
                    </tr>
                </thead>
                <tbody id="aiDataBody">
                    <tr>
                        <td>name</td>
                        <td>LIMING</td>
                        <td class="mapped">✅ name → name</td>
                    </tr>
                    <tr>
                        <td>passportNo</td>
                        <td>G12345678</td>
                        <td class="mapped">✅ passportNo → passNo</td>
                    </tr>
                    <tr>
                        <td>birthDate</td>
                        <td>01/01/1990</td>
                        <td class="mapped">✅ birthDate → dob</td>
                    </tr>
                    <tr>
                        <td>passportExpiry</td>
                        <td>01/01/2026</td>
                        <td class="mapped">✅ passportExpiry → passExpDte</td>
                    </tr>
                    <tr>
                        <td>nationality</td>
                        <td>CHN</td>
                        <td class="mapped">✅ nationality → nationality</td>
                    </tr>
                    <tr>
                        <td>gender</td>
                        <td>MALE</td>
                        <td class="transformed">✅ gender → sex → 1</td>
                    </tr>
                    <tr>
                        <td>email</td>
                        <td><EMAIL></td>
                        <td class="mapped">✅ email → email</td>
                    </tr>
                    <tr>
                        <td>mobile</td>
                        <td>60123456789</td>
                        <td class="mapped">✅ mobile → mobile</td>
                    </tr>
                    <tr>
                        <td>arrivalDate</td>
                        <td>01/08/2025</td>
                        <td class="mapped">✅ arrivalDate → arrDt</td>
                    </tr>
                    <tr>
                        <td>departureDate</td>
                        <td>07/08/2025</td>
                        <td class="mapped">✅ departureDate → depDt</td>
                    </tr>
                    <tr>
                        <td>flightNo</td>
                        <td>MH123</td>
                        <td class="mapped">✅ flightNo → vesselNm</td>
                    </tr>
                    <tr>
                        <td>accommodationType</td>
                        <td>HOTEL</td>
                        <td class="transformed">✅ accommodationType → accommodationStay → 01</td>
                    </tr>
                    <tr>
                        <td>address1</td>
                        <td>Hotel KL City Center</td>
                        <td class="mapped">✅ address1 → accommodationAddress1</td>
                    </tr>
                    <tr>
                        <td>postcode</td>
                        <td>50000</td>
                        <td class="mapped">✅ postcode → accommodationPostcode</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🎯 MDAC原生字段（21个）</h3>
            <p>MDAC网站期望的所有字段：</p>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>MDAC字段</th>
                        <th>选择器</th>
                        <th>数据来源</th>
                        <th>转换值</th>
                    </tr>
                </thead>
                <tbody id="mdacFieldsBody">
                    <!-- 将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🔧 测试操作</h3>
            <button class="btn-primary" onclick="testFieldMapping()">测试字段映射</button>
            <button class="btn-secondary" onclick="clearResults()">清除结果</button>

            <div id="testResults" class="result-section">
                <h3>测试结果</h3>
                <div id="resultContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 修复前后对比</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>修复前</th>
                        <th>修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>legacyMappings变量</td>
                        <td class="error">❌ 未定义，JS错误</td>
                        <td class="success">✅ 已定义</td>
                    </tr>
                    <tr>
                        <td>字段映射标准</td>
                        <td class="error">❌ 多文件不一致</td>
                        <td class="success">✅ 统一配置</td>
                    </tr>
                    <tr>
                        <td>AI字段映射</td>
                        <td class="error">❌ 14个字段只映射4个</td>
                        <td class="success">✅ 14个字段全部映射</td>
                    </tr>
                    <tr>
                        <td>值转换规则</td>
                        <td class="warning">⚠️ 部分转换缺失</td>
                        <td class="success">✅ 完整转换规则</td>
                    </tr>
                    <tr>
                        <td>错误处理</td>
                        <td class="error">❌ 无容错机制</td>
                        <td class="success">✅ 完整容错和备用方案</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟AI返回数据
        const sampleAIData = {
            name: 'LIMING',
            passportNo: 'G12345678',
            birthDate: '01/01/1990',
            passportExpiry: '01/01/2026',
            nationality: 'CHN',
            gender: 'MALE',
            email: '<EMAIL>',
            mobile: '60123456789',
            arrivalDate: '01/08/2025',
            departureDate: '07/08/2025',
            flightNo: 'MH123',
            accommodationType: 'HOTEL',
            address1: 'Hotel KL City Center',
            postcode: '50000'
        };

        // MDAC原生字段列表
        const mdacNativeFields = [
            'name', 'passNo', 'dob', 'passExpDte', 'nationality', 'sex',
            'email', 'confirmEmail', 'region', 'mobile',
            'arrDt', 'depDt', 'vesselNm', 'trvlMode', 'embark',
            'accommodationStay', 'accommodationAddress1', 'accommodationAddress2',
            'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];

        // 测试字段映射函数
        function testFieldMapping() {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');

            resultsDiv.style.display = 'block';
            contentDiv.innerHTML = '';

            try {
                // 测试统一配置类
                if (typeof MDACFieldMappingConfig !== 'undefined') {
                    const config = new MDACFieldMappingConfig();

                    // 测试AI到MDAC的转换
                    const transformedData = config.transformAIToMDAC(sampleAIData);

                    // 验证结果
                    const validation = config.validateDataIntegrity(transformedData);

                    let resultHTML = `
                        <div class="test-section success">
                            <h4>✅ 字段映射测试成功</h4>
                            <p><strong>AI返回字段数:</strong> ${Object.keys(sampleAIData).length}</p>
                            <p><strong>成功映射字段数:</strong> ${Object.keys(transformedData).length}</p>
                            <p><strong>映射成功率:</strong> ${((Object.keys(transformedData).length / Object.keys(sampleAIData).length) * 100).toFixed(1)}%</p>
                        </div>
                    `;

                    // 显示转换详情
                    resultHTML += '<div class="test-section"><h4>📋 转换详情</h4><table class="data-table">';
                    resultHTML += '<thead><tr><th>AI字段</th><th>AI值</th><th>MDAC字段</th><th>转换值</th></tr></thead><tbody>';

                    Object.entries(sampleAIData).forEach(([aiField, aiValue]) => {
                        const mdacField = config.AI_TO_MDAC_MAPPING[aiField];
                        const transformedValue = transformedData[mdacField];
                        resultHTML += `<tr>
                            <td>${aiField}</td>
                            <td>${aiValue}</td>
                            <td>${mdacField || '未映射'}</td>
                            <td>${transformedValue || '-'}</td>
                        </tr>`;
                    });

                    resultHTML += '</tbody></table></div>';

                    // 显示验证结果
                    if (validation.errors.length > 0) {
                        resultHTML += `<div class="test-section error"><h4>❌ 数据验证错误</h4><ul>`;
                        validation.errors.forEach(error => resultHTML += `<li>${error}</li>`);
                        resultHTML += '</ul></div>';
                    }

                    if (validation.warnings.length > 0) {
                        resultHTML += `<div class="test-section warning"><h4>⚠️ 数据验证警告</h4><ul>`;
                        validation.warnings.forEach(warning => resultHTML += `<li>${warning}</li>`);
                        resultHTML += '</ul></div>';
                    }

                    contentDiv.innerHTML = resultHTML;

                    // 更新MDAC字段表
                    updateMDACFieldsTable(transformedData);

                } else {
                    contentDiv.innerHTML = '<div class="test-section error"><h4>❌ MDACFieldMappingConfig 未加载</h4></div>';
                }
            } catch (error) {
                contentDiv.innerHTML = `<div class="test-section error"><h4>❌ 测试失败</h4><p>${error.message}</p></div>`;
            }
        }

        // 更新MDAC字段表
        function updateMDACFieldsTable(transformedData) {
            const tbody = document.getElementById('mdacFieldsBody');
            tbody.innerHTML = '';

            mdacNativeFields.forEach(field => {
                const value = transformedData[field] || '-';
                const source = transformedData[field] ? 'AI映射' : '缺失';
                const rowClass = transformedData[field] ? 'mapped' : 'unmapped';

                tbody.innerHTML += `
                    <tr class="${rowClass}">
                        <td>${field}</td>
                        <td>#${field}</td>
                        <td>${source}</td>
                        <td>${value}</td>
                    </tr>
                `;
            });
        }

        // 清除结果
        function clearResults() {
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('MDAC字段映射测试页面已加载');
        });
    </script>
</body>
</html>