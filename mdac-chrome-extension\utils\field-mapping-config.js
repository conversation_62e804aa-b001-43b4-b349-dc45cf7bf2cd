// MDAC字段映射配置文件 - 统一标准
// 用途：提供MDAC表单字段的统一映射标准
// 依赖：无
// 技术栈：原生JavaScript
// 核心功能：统一AI返回字段与MDAC原生字段的映射关系

class MDACFieldMappingConfig {
    constructor() {
        // MDAC网站原生字段列表（21个）
        this.MDAC_NATIVE_FIELDS = [
            'name', 'passNo', 'dob', 'passExpDte', 'nationality', 'sex',
            'email', 'confirmEmail', 'region', 'mobile',
            'arrDt', 'depDt', 'vesselNm', 'trvlMode', 'embark',
            'accommodationStay', 'accommodationAddress1', 'accommodationAddress2',
            'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];

        // AI返回字段到MDAC原生字段的映射（简化版本 - 零转换映射）
        this.AI_TO_MDAC_MAPPING = {
            // MDAC原生字段 - 直接映射（零转换）
            'name': 'name',
            'passNo': 'passNo',
            'dob': 'dob',
            'passExpDte': 'passExpDte',
            'nationality': 'nationality',
            'sex': 'sex',
            'email': 'email',
            'confirmEmail': 'confirmEmail',
            'region': 'region',
            'mobile': 'mobile',
            'arrDt': 'arrDt',
            'depDt': 'depDt',
            'vesselNm': 'vesselNm',
            'trvlMode': 'trvlMode',
            'embark': 'embark',
            'accommodationStay': 'accommodationStay',
            'accommodationAddress1': 'accommodationAddress1',
            'accommodationAddress2': 'accommodationAddress2',
            'accommodationState': 'accommodationState',
            'accommodationCity': 'accommodationCity',
            'accommodationPostcode': 'accommodationPostcode',

            // 最小化向后兼容 - 仅保留最常见的旧字段名
            'passengerName': 'name',
            'passportNo': 'passNo',
            'birthDate': 'dob',
            'gender': 'sex',
            'phoneNumber': 'mobile',
            'flightNo': 'vesselNm'
        };

        // MDAC原生字段到AI返回字段的反向映射
        this.MDAC_TO_AI_MAPPING = {};
        for (const [aiField, mdacField] of Object.entries(this.AI_TO_MDAC_MAPPING)) {
            this.MDAC_TO_AI_MAPPING[mdacField] = aiField;
        }

        // 数据值转换规则
        this.VALUE_TRANSFORM_RULES = {
            // 性别转换
            'sex': {
                'MALE': '1',
                'FEMALE': '2',
                '1': '1',
                '2': '2',
                'male': '1',
                'female': '2',
                '男性': '1',
                '女性': '2'
            },

            // 住宿类型转换
            'accommodationStay': {
                'HOTEL': '01',
                'RESIDENCE': '02',
                'OTHERS': '99',
                '01': '01',
                '02': '02',
                '99': '99',
                'hotel': '01',
                'residence': '02',
                'others': '99',
                '酒店': '01',
                '亲友': '02',
                '其他': '99'
            },

            // 旅行方式转换
            'trvlMode': {
                'AIR': '1',
                'LAND': '2',
                'SEA': '3',
                '1': '1',
                '2': '2',
                '3': '3',
                'air': '1',
                'land': '2',
                'sea': '3',
                '飞机': '1',
                '陆路': '2',
                '海路': '3'
            }
        };

        // 日期格式转换规则
        this.DATE_FORMAT_PATTERNS = [
            // DD/MM/YYYY 格式
            {
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                transformer: (match) => match // 保持原格式
            },
            // YYYY-MM-DD 格式
            {
                pattern: /^\d{4}-\d{2}-\d{2}$/,
                transformer: (match) => {
                    const [year, month, day] = match.split('-');
                    return `${day}/${month}/${year}`;
                }
            },
            // MM/DD/YYYY 格式
            {
                pattern: /^\d{1,2}\/\d{1,2}\/\d{4}$/,
                transformer: (match) => {
                    const [month, day, year] = match.split('/');
                    return `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
                }
            },
            // 中文日期格式：2025年8月1日
            {
                pattern: /(\d{4})年(\d{1,2})月(\d{1,2})日/,
                transformer: (match, year, month, day) => {
                    return `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
                }
            }
        ];

        // 字段验证规则
        this.FIELD_VALIDATION_RULES = {
            'name': {
                required: true,
                pattern: /^[A-Z\s]+$/,
                maxLength: 60,
                message: '姓名必须是大写英文字母'
            },
            'passNo': {
                required: true,
                pattern: /^[A-Z0-9]{6,12}$/,
                message: '护照号码格式不正确'
            },
            'email': {
                required: true,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '邮箱格式不正确'
            },
            'mobile': {
                required: true,
                pattern: /^\d{7,15}$/,
                message: '手机号码格式不正确'
            },
            'nationality': {
                required: true,
                pattern: /^[A-Z]{3}$/,
                message: '国籍代码必须是3位大写字母'
            }
        };

        // 字段选择器映射（用于DOM操作）
        this.FIELD_SELECTORS = {
            'name': '#name',
            'passNo': '#passNo',
            'dob': '#dob',
            'passExpDte': '#passExpDte',
            'nationality': '#nationality',
            'sex': '#sex',
            'email': '#email',
            'confirmEmail': '#confirmEmail',
            'region': '#region',
            'mobile': '#mobile',
            'arrDt': '#arrDt',
            'depDt': '#depDt',
            'vesselNm': '#vesselNm',
            'trvlMode': '#trvlMode',
            'embark': '#embark',
            'accommodationStay': '#accommodationStay',
            'accommodationAddress1': '#accommodationAddress1',
            'accommodationAddress2': '#accommodationAddress2',
            'accommodationState': '#accommodationState',
            'accommodationCity': '#accommodationCity',
            'accommodationPostcode': '#accommodationPostcode'
        };

        // 特殊字段类型
        this.SPECIAL_FIELD_TYPES = {
            SELECT_FIELDS: ['nationality', 'sex', 'region', 'trvlMode', 'embark', 'accommodationStay', 'accommodationState', 'accommodationCity'],
            DATE_FIELDS: ['dob', 'passExpDte', 'arrDt', 'depDt'],
            CASCADING_FIELDS: ['region', 'accommodationState', 'accommodationCity'] // 级联字段
        };
    }

    // 将AI返回字段转换为MDAC原生字段
    transformAIToMDAC(aiData) {
        const mdacData = {};

        for (const [aiField, value] of Object.entries(aiData)) {
            const mdacField = this.AI_TO_MDAC_MAPPING[aiField];
            if (mdacField && value !== undefined && value !== null && value !== '') {
                mdacData[mdacField] = this.transformFieldValue(mdacField, value);
            }
        }

        return mdacData;
    }

    // 将MDAC原生字段转换为AI返回字段格式
    transformMDACToAI(mdacData) {
        const aiData = {};

        for (const [mdacField, value] of Object.entries(mdacData)) {
            const aiField = this.MDAC_TO_AI_MAPPING[mdacField];
            if (aiField && value !== undefined && value !== null && value !== '') {
                aiData[aiField] = value;
            }
        }

        return aiData;
    }

    // 转换单个字段值
    transformFieldValue(field, value) {
        if (!value && value !== 0) return value;

        // 字符串值统一处理
        let transformedValue = value.toString().trim();

        // 日期字段转换
        if (this.SPECIAL_FIELD_TYPES.DATE_FIELDS.includes(field)) {
            transformedValue = this.transformDateField(transformedValue);
        }

        // 特殊值转换
        if (this.VALUE_TRANSFORM_RULES[field]) {
            const rules = this.VALUE_TRANSFORM_RULES[field];
            if (rules[transformedValue.toUpperCase()]) {
                transformedValue = rules[transformedValue.toUpperCase()];
            }
        }

        // 特定字段格式化
        switch (field) {
            case 'name':
                transformedValue = transformedValue.toUpperCase();
                break;
            case 'passportNo':
                transformedValue = transformedValue.toUpperCase();
                break;
            case 'nationality':
                transformedValue = transformedValue.toUpperCase();
                break;
            case 'email':
                transformedValue = transformedValue.toLowerCase();
                break;
        }

        return transformedValue;
    }

    // 日期字段转换
    transformDateField(dateStr) {
        if (!dateStr) return null;

        // 尝试各种日期格式转换
        for (const rule of this.DATE_FORMAT_PATTERNS) {
            if (rule.pattern.test(dateStr)) {
                if (rule.transformer.length > 1) {
                    // 对于正则表达式匹配，需要传入完整匹配和分组
                    const match = dateStr.match(rule.pattern);
                    return rule.transformer.apply(null, [match[0], ...match.slice(1)]);
                } else {
                    return rule.transformer(dateStr);
                }
            }
        }

        // 尝试使用Date对象解析
        try {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}/${month}/${year}`;
            }
        } catch (error) {
            console.warn('日期解析失败:', dateStr, error);
        }

        return dateStr; // 如果无法解析，返回原值
    }

    // 验证字段数据
    validateField(field, value) {
        const rule = this.FIELD_VALIDATION_RULES[field];
        if (!rule) return { valid: true };

        // 检查必填
        if (rule.required && (!value || value.toString().trim() === '')) {
            return { valid: false, error: `${field} 是必填字段` };
        }

        // 检查长度
        if (rule.maxLength && value.toString().length > rule.maxLength) {
            return { valid: false, error: `${field} 长度不能超过 ${rule.maxLength} 字符` };
        }

        // 检查格式
        if (rule.pattern && !rule.pattern.test(value.toString())) {
            return { valid: false, error: rule.message || `${field} 格式不正确` };
        }

        return { valid: true };
    }

    // 获取字段选择器
    getFieldSelector(field) {
        return this.FIELD_SELECTORS[field] || null;
    }

    // 检查字段是否为特殊类型
    isSpecialFieldType(field, type) {
        return this.SPECIAL_FIELD_TYPES[type] && this.SPECIAL_FIELD_TYPES[type].includes(field);
    }

    // 获取所有MDAC原生字段
    getMDACNativeFields() {
        return [...this.MDAC_NATIVE_FIELDS];
    }

    // 获取字段映射关系
    getFieldMapping() {
        return {
            aiToMDAC: { ...this.AI_TO_MDAC_MAPPING },
            mdacToAI: { ...this.MDAC_TO_AI_MAPPING }
        };
    }

    // 验证数据完整性
    validateDataIntegrity(data) {
        const errors = [];
        const warnings = [];

        // 检查非MDAC字段
        const nonMDACFields = Object.keys(data).filter(field => !this.MDAC_NATIVE_FIELDS.includes(field));
        if (nonMDACFields.length > 0) {
            warnings.push(`发现非MDAC字段: ${nonMDACFields.join(', ')}`);
        }

        // 验证必填字段
        const requiredFields = ['name', 'passNo', 'dob', 'nationality', 'email', 'mobile'];
        for (const field of requiredFields) {
            const validation = this.validateField(field, data[field]);
            if (!validation.valid) {
                errors.push(validation.error);
            }
        }

        return { errors, warnings };
    }
}

// 创建全局实例
window.MDACFieldMappingConfig = MDACFieldMappingConfig;

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACFieldMappingConfig;
}