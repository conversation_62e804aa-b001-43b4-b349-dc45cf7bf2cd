// 统一图片处理模块
// 用途：简化的图片压缩处理，统一参数：1000px + 85%质量
// 依赖：浏览器Canvas API
// 技术栈：原生JavaScript
// 核心功能：图片压缩、格式转换、base64编码

class ImageProcessor {
  constructor() {
    // 统一压缩配置：1000px + 85%质量
    this.config = {
      maxSize: 1000,
      quality: 0.85,
      format: 'image/jpeg'
    };
  }

  // 批量处理图片（优化性能和内存使用）
  async processImages(imageFiles) {
    if (!imageFiles || imageFiles.length === 0) return [];

    // 限制并发数量，避免内存溢出
    const batchSize = 3;
    const results = [];

    for (let i = 0; i < imageFiles.length; i += batchSize) {
      const batch = imageFiles.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(file => this.compressImage(file))
      );
      results.push(...batchResults);

      // 小延迟让浏览器回收内存
      if (i + batchSize < imageFiles.length) {
        await this.delay(100);
      }
    }

    return results.filter(result => result !== null);
  }

  // 延迟函数用于内存管理
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 单张图片压缩
  async compressImage(imageFile) {
    if (!this.isValidImageFile(imageFile)) {
      console.warn(`跳过无效图片文件: ${imageFile.name}`);
      return null;
    }

    try {
      return await this.processImageFile(imageFile);
    } catch (error) {
      console.error(`图片压缩失败: ${imageFile.name}`, error);
      return null;
    }
  }

  // 图片文件验证
  isValidImageFile(file) {
    if (!file || !file.type) return false;
    return file.type.startsWith('image/');
  }

  // 核心压缩处理（优化内存使用）
  async processImageFile(imageFile) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      let objectUrl = null;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        try {
          // 计算压缩后尺寸
          const { width, height } = this.calculateDimensions(img.width, img.height);

          // 设置画布尺寸
          canvas.width = width;
          canvas.height = height;

          // 绘制图片
          ctx.drawImage(img, 0, 0, width, height);

          // 释放原始图片内存
          if (objectUrl) {
            URL.revokeObjectURL(objectUrl);
            objectUrl = null;
          }

          // 转换为Blob
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('图片压缩失败'));
              return;
            }

            // 转换为base64
            const reader = new FileReader();
            reader.onload = () => {
              const base64 = reader.result.split(',')[1];

              // 释放canvas内存
              canvas.width = 0;
              canvas.height = 0;

              resolve({
                name: imageFile.name,
                originalName: imageFile.name,
                mimeType: this.config.format,
                base64: base64,
                size: blob.size,
                originalSize: imageFile.size,
                compressionRatio: Math.round((1 - blob.size / imageFile.size) * 100),
                dimensions: { width, height },
                originalDimensions: { width: img.width, height: img.height }
              });
            };
            reader.onerror = () => {
              reject(new Error('读取压缩图片失败'));
            };
            reader.readAsDataURL(blob);
          }, this.config.format, this.config.quality);
        } catch (error) {
          // 清理资源
          if (objectUrl) {
            URL.revokeObjectURL(objectUrl);
          }
          canvas.width = 0;
          canvas.height = 0;
          reject(error);
        }
      };

      img.onerror = () => {
        if (objectUrl) {
          URL.revokeObjectURL(objectUrl);
        }
        reject(new Error(`无法加载图片: ${imageFile.name}`));
      };

      // 创建对象URL并设置src
      objectUrl = URL.createObjectURL(imageFile);
      img.src = objectUrl;
    });
  }

  // 计算压缩后尺寸
  calculateDimensions(originalWidth, originalHeight) {
    const maxSize = this.config.maxSize;

    if (originalWidth <= maxSize && originalHeight <= maxSize) {
      return { width: originalWidth, height: originalHeight };
    }

    const ratio = Math.min(maxSize / originalWidth, maxSize / originalHeight);
    return {
      width: Math.round(originalWidth * ratio),
      height: Math.round(originalHeight * ratio)
    };
  }

  // 获取压缩统计信息
  getCompressionStats(processedImages) {
    if (!processedImages || processedImages.length === 0) {
      return { totalFiles: 0, totalSaving: 0, avgCompressionRatio: 0 };
    }

    const totalOriginalSize = processedImages.reduce((sum, img) => sum + img.originalSize, 0);
    const totalCompressedSize = processedImages.reduce((sum, img) => sum + img.size, 0);
    const totalSaving = totalOriginalSize - totalCompressedSize;
    const avgCompressionRatio = Math.round(processedImages.reduce((sum, img) => sum + img.compressionRatio, 0) / processedImages.length);

    return {
      totalFiles: processedImages.length,
      totalOriginalSize,
      totalCompressedSize,
      totalSaving,
      avgCompressionRatio,
      savingPercentage: Math.round((totalSaving / totalOriginalSize) * 100)
    };
  }

  // 验证图片是否成功压缩
  validateCompression(processedImage) {
    if (!processedImage || !processedImage.base64) return false;
    if (processedImage.size > processedImage.originalSize * 1.1) {
      console.warn(`压缩后文件更大: ${processedImage.name}`);
    }
    return true;
  }
}

// 全局暴露
window.ImageProcessor = ImageProcessor;