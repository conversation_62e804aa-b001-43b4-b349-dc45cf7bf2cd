// 控制台测试脚本 - 在浏览器开发者工具中运行
console.log('🧪 开始端到端测试验证...');

// 1. 检查环境
console.log('\n=== 环境检查 ===');
console.log('✅ window.LLMAPI:', typeof window.LLMAPI);
console.log('✅ window.llmAPI:', typeof window.llmAPI);
console.log('✅ window.moonshotAPI:', typeof window.moonshotAPI);
console.log('✅ window.geminiAPI:', typeof window.geminiAPI);

// 2. 检查 API 实例方法
if (window.llmAPI) {
    console.log('\n=== API 方法检查 ===');
    const methods = ['setApiKey', 'preprocessImage', 'extractDataFromImagesVision', 'buildExtractionPrompt'];
    methods.forEach(method => {
        console.log(`✅ ${method}:`, typeof window.llmAPI[method]);
    });
}

// 3. 测试提示词构建
console.log('\n=== 提示词构建测试 ===');
try {
    const testPrompt = window.llmAPI.buildExtractionPrompt('测试旅客信息：姓名：ZHANG SAN，护照号：E12345678，出生日期：01/01/1990，性别：MALE');
    console.log('✅ 提示词构建成功，长度:', testPrompt.length);
    console.log('提示词预览:', testPrompt.substring(0, 200) + '...');
} catch (error) {
    console.error('❌ 提示词构建失败:', error);
}

// 4. 检查页面元素
console.log('\n=== 页面元素检查 ===');
const elements = [
    'textPrompt', 'textModel', 'visionModel', 'fileInput', 
    'resultsContainer', 'comparisonContainer'
];
elements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`${element ? '✅' : '❌'} ${id}:`, element ? 'found' : 'not found');
});

// 5. 检查模型选项
console.log('\n=== 模型选项检查 ===');
const textModelSelect = document.getElementById('textModel');
const visionModelSelect = document.getElementById('visionModel');

if (textModelSelect) {
    console.log('文本模型选项:');
    Array.from(textModelSelect.options).forEach(option => {
        console.log(`  - ${option.value} (${option.getAttribute('data-platform')}): ${option.textContent}`);
    });
}

if (visionModelSelect) {
    console.log('视觉模型选项:');
    Array.from(visionModelSelect.options).forEach(option => {
        console.log(`  - ${option.value} (${option.getAttribute('data-platform')}): ${option.textContent}`);
    });
}

// 6. 模拟文本测试（不实际调用 API）
console.log('\n=== 模拟文本测试 ===');
const textPromptElement = document.getElementById('textPrompt');
if (textPromptElement) {
    textPromptElement.value = '测试文本：姓名：ZHANG SAN，护照号：E12345678，出生日期：01/01/1990，性别：MALE';
    console.log('✅ 已设置测试文本');
}

// 7. 检查硬编码密钥配置
console.log('\n=== 密钥配置检查 ===');
if (typeof HARDCODED_API_KEYS !== 'undefined') {
    console.log('✅ HARDCODED_API_KEYS 已定义');
    console.log('  - moonshot:', HARDCODED_API_KEYS.moonshot ? '已配置' : '未配置');
    console.log('  - zhipu:', HARDCODED_API_KEYS.zhipu ? '已配置' : '未配置');
} else {
    console.log('❌ HARDCODED_API_KEYS 未定义');
}

// 8. 检查全局变量
console.log('\n=== 全局变量检查 ===');
const globalVars = ['moonshotAPI', 'zhipuAPI', 'selectedFiles', 'testResults', 'comparisonResults'];
globalVars.forEach(varName => {
    console.log(`${typeof window[varName] !== 'undefined' ? '✅' : '❌'} ${varName}:`, typeof window[varName]);
});

console.log('\n🎉 端到端测试验证完成！');
console.log('请检查上述输出，确认所有 ✅ 项目都正常。');
console.log('如需测试实际 API 调用，请在页面上手动操作。');
