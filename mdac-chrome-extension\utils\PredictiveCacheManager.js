// 预测性缓存管理器 - 智能预加载系统
// 用途：基于用户行为模式预测并预加载可能需要的模块和数据
// 依赖：ModuleLoader, Chrome Storage API
// 技术栈：原生JavaScript + 机器学习预测算法
// 核心功能：行为分析、模式识别、智能预加载、缓存优化
// 重要：提升用户体验，减少等待时间

class PredictiveCacheManager {
    constructor() {
        // 用户行为模式存储
        this.userPatterns = {
            frequentNationality: null,
            frequentTravelType: null,
            commonTravelDates: [],
            preferredAccommodation: null,
            lastUsedFields: new Set(),
            usageFrequency: new Map()
        };

        // 预测模型配置
        this.predictionConfig = {
            confidenceThreshold: 0.7,
            patternHistorySize: 50,
            minPatternOccurrences: 3,
            predictionWindow: 24 * 60 * 60 * 1000 // 24小时
        };

        // 缓存策略
        this.cacheStrategies = {
            IMMEDIATE: 'immediate',    // 立即可用数据
            PREFETCH: 'prefetch',      // 预测性预加载
            BACKGROUND: 'background'   // 后台异步加载
        };

        // 性能监控
        this.cacheStats = {
            predictions: 0,
            hits: 0,
            misses: 0,
            falsePositives: 0,
            averagePredictionAccuracy: 0
        };

        this.init();
    }

    async init() {
        console.log('🔮 PredictiveCacheManager 初始化...');

        try {
            // 加载用户行为历史
            await this.loadUserPatterns();

            // 启动预测引擎
            this.startPredictionEngine();

            // 设置事件监听器
            this.setupEventListeners();

            console.log('✅ PredictiveCacheManager 初始化完成');
        } catch (error) {
            console.error('❌ PredictiveCacheManager 初始化失败:', error);
        }
    }

    /**
     * 分析用户行为并更新模式
     */
    async analyzeUserBehavior(formData) {
        try {
            console.log('📊 分析用户行为数据...', formData);

            // 提取关键字段用于模式分析
            const keyFields = {
                nationality: formData.nationality,
                travelMode: formData.trvlMode,
                accommodationStay: formData.accommodationStay,
                region: formData.region
            };

            // 更新使用频率统计
            for (const [field, value] of Object.entries(keyFields)) {
                if (value) {
                    await this.updateUsageFrequency(field, value);
                }
            }

            // 更新常用模式
            await this.updateCommonPatterns(formData);

            // 触发预测更新
            await this.updatePredictions();

        } catch (error) {
            console.error('❌ 用户行为分析失败:', error);
        }
    }

    /**
     * 更新使用频率统计
     */
    async updateUsageFrequency(field, value) {
        const key = `${field}:${value}`;
        const currentCount = this.userPatterns.usageFrequency.get(key) || 0;
        this.userPatterns.usageFrequency.set(key, currentCount + 1);

        // 更新最近使用字段
        this.userPatterns.lastUsedFields.add(key);

        console.log(`📈 更新使用频率: ${key} = ${currentCount + 1}`);
    }

    /**
     * 更新常用模式
     */
    async updateCommonPatterns(formData) {
        // 更新最常使用的国籍
        if (formData.nationality) {
            const nationalityCount = this.userPatterns.usageFrequency.get(`nationality:${formData.nationality}`) || 0;
            const currentFrequent = this.userPatterns.frequentNationality;

            if (!currentFrequent || nationalityCount > (this.userPatterns.usageFrequency.get(`nationality:${currentFrequent}`) || 0)) {
                this.userPatterns.frequentNationality = formData.nationality;
                console.log(`🌍 更新常用国籍: ${formData.nationality}`);
            }
        }

        // 更新常用旅行方式
        if (formData.trvlMode) {
            const travelCount = this.userPatterns.usageFrequency.get(`trvlMode:${formData.trvlMode}`) || 0;
            const currentFrequent = this.userPatterns.frequentTravelType;

            if (!currentFrequent || travelCount > (this.userPatterns.usageFrequency.get(`trvlMode:${currentFrequent}`) || 0)) {
                this.userPatterns.frequentTravelType = formData.trvlMode;
                console.log(`✈️ 更新常用旅行方式: ${formData.trvlMode}`);
            }
        }

        // 更新常用住宿类型
        if (formData.accommodationStay) {
            const accommodationCount = this.userPatterns.usageFrequency.get(`accommodationStay:${formData.accommodationStay}`) || 0;
            const currentFrequent = this.userPatterns.preferredAccommodation;

            if (!currentFrequent || accommodationCount > (this.userPatterns.usageFrequency.get(`accommodationStay:${currentFrequent}`) || 0)) {
                this.userPatterns.preferredAccommodation = formData.accommodationStay;
                console.log(`🏨 更新常用住宿: ${formData.accommodationStay}`);
            }
        }

        // 保存更新后的模式
        await this.saveUserPatterns();
    }

    /**
     * 基于用户模式进行预测
     */
    async generatePredictions() {
        try {
            console.log('🔮 生成预测...');

            const predictions = {
                highConfidence: [],
                mediumConfidence: [],
                lowConfidence: []
            };

            // 预测常用数据
            if (this.userPatterns.frequentNationality) {
                const confidence = this.calculatePredictionConfidence('nationality', this.userPatterns.frequentNationality);

                if (confidence >= this.predictionConfig.confidenceThreshold) {
                    predictions.highConfidence.push({
                        type: 'nationality_data',
                        data: {
                            nationality: this.userPatterns.frequentNationality,
                            region: this.getRegionForNationality(this.userPatterns.frequentNationality)
                        },
                        confidence: confidence,
                        priority: 'high'
                    });
                }
            }

            // 预测旅行相关数据
            if (this.userPatterns.frequentTravelType) {
                const confidence = this.calculatePredictionConfidence('trvlMode', this.userPatterns.frequentTravelType);

                if (confidence >= this.predictionConfig.confidenceThreshold) {
                    predictions.highConfidence.push({
                        type: 'travel_data',
                        data: {
                            trvlMode: this.userPatterns.frequentTravelType
                        },
                        confidence: confidence,
                        priority: 'medium'
                    });
                }
            }

            // 预测住宿数据
            if (this.userPatterns.preferredAccommodation) {
                const confidence = this.calculatePredictionConfidence('accommodationStay', this.userPatterns.preferredAccommodation);

                if (confidence >= this.predictionConfig.confidenceThreshold) {
                    predictions.mediumConfidence.push({
                        type: 'accommodation_data',
                        data: {
                            accommodationStay: this.userPatterns.preferredAccommodation
                        },
                        confidence: confidence,
                        priority: 'medium'
                    });
                }
            }

            this.cacheStats.predictions++;
            console.log(`🔮 预测生成完成: ${predictions.highConfidence.length}个高置信度, ${predictions.mediumConfidence.length}个中等置信度`);

            return predictions;

        } catch (error) {
            console.error('❌ 预测生成失败:', error);
            return { highConfidence: [], mediumConfidence: [], lowConfidence: [] };
        }
    }

    /**
     * 计算预测置信度
     */
    calculatePredictionConfidence(field, value) {
        const key = `${field}:${value}`;
        const frequency = this.userPatterns.usageFrequency.get(key) || 0;
        const totalUsage = Array.from(this.userPatterns.usageFrequency.values()).reduce((sum, count) => sum + count, 0);

        if (totalUsage === 0) return 0;

        // 基于使用频率计算置信度
        const baseConfidence = frequency / totalUsage;

        // 考虑最近使用的时间权重
        const recencyWeight = this.calculateRecencyWeight(key);

        // 最终置信度
        const confidence = baseConfidence * recencyWeight;

        return Math.min(confidence, 1.0);
    }

    /**
     * 计算时间权重（最近使用权重更高）
     */
    calculateRecencyWeight(key) {
        // 简化版本：如果最近使用过，增加权重
        if (this.userPatterns.lastUsedFields.has(key)) {
            return 1.2; // 20% 权重加成
        }
        return 1.0;
    }

    /**
     * 执行预测性缓存加载
     */
    async executePredictiveCaching(predictions) {
        try {
            console.log('🚀 执行预测性缓存加载...');

            // 高置信度预测 - 立即加载
            for (const prediction of predictions.highConfidence) {
                await this.cachePredictionData(prediction);
            }

            // 中等置信度预测 - 后台加载
            setTimeout(async () => {
                for (const prediction of predictions.mediumConfidence) {
                    await this.cachePredictionData(prediction);
                }
            }, 2000); // 延迟2秒，避免阻塞主要功能

            console.log('✅ 预测性缓存加载完成');

        } catch (error) {
            console.error('❌ 预测性缓存加载失败:', error);
        }
    }

    /**
     * 缓存预测数据
     */
    async cachePredictionData(prediction) {
        try {
            const { type, data, confidence } = prediction;

            switch (type) {
                case 'nationality_data':
                    // 预加载国籍相关数据
                    await this.preloadNationalityData(data.nationality);
                    break;

                case 'travel_data':
                    // 预加载旅行相关数据
                    await this.preloadTravelData(data.trvlMode);
                    break;

                case 'accommodation_data':
                    // 预加载住宿相关数据
                    await this.preloadAccommodationData(data.accommodationStay);
                    break;
            }

            console.log(`💾 预测数据已缓存: ${type} (置信度: ${(confidence * 100).toFixed(1)}%)`);

        } catch (error) {
            console.error(`❌ 预测数据缓存失败: ${prediction.type}`, error);
        }
    }

    /**
     * 预加载国籍相关数据
     */
    async preloadNationalityData(nationality) {
        try {
            // 预加载对应的国家代码数据
            console.log(`🌍 预加载国籍数据: ${nationality}`);

            // 这里可以预加载相关的区号、国家信息等数据
            const regionData = this.getRegionDataForNationality(nationality);

            // 缓存到本地存储
            await chrome.storage.local.set({
                [`predicted_region_${nationality}`]: regionData,
                [`predicted_nationality_${nationality}`]: {
                    nationality: nationality,
                    region: regionData,
                    cachedAt: Date.now()
                }
            });

        } catch (error) {
            console.error('❌ 国籍数据预加载失败:', error);
        }
    }

    /**
     * 预加载旅行相关数据
     */
    async preloadTravelData(travelMode) {
        try {
            console.log(`✈️ 预加载旅行数据: ${travelMode}`);

            // 可以预加载相关的旅行模板、常用航班信息等
            const travelTemplates = this.getTravelTemplates(travelMode);

            await chrome.storage.local.set({
                [`predicted_travel_${travelMode}`]: travelTemplates,
                cachedAt: Date.now()
            });

        } catch (error) {
            console.error('❌ 旅行数据预加载失败:', error);
        }
    }

    /**
     * 预加载住宿相关数据
     */
    async preloadAccommodationData(accommodationType) {
        try {
            console.log(`🏨 预加载住宿数据: ${accommodationType}`);

            // 可以预加载相关的住宿模板、常用地址等
            const accommodationTemplates = this.getAccommodationTemplates(accommodationType);

            await chrome.storage.local.set({
                [`predicted_accommodation_${accommodationType}`]: accommodationTemplates,
                cachedAt: Date.now()
            });

        } catch (error) {
            console.error('❌ 住宿数据预加载失败:', error);
        }
    }

    /**
     * 获取国籍对应的区号数据
     */
    getRegionDataForNationality(nationality) {
        const nationalityRegionMap = {
            'CHN': '86',
            'USA': '1',
            'GBR': '44',
            'SGP': '65',
            'MYS': '60',
            'JPN': '81',
            'KOR': '82'
        };

        return nationalityRegionMap[nationality] || null;
    }

    /**
     * 获取旅行模板
     */
    getTravelTemplates(travelMode) {
        const templates = {
            'AIR': {
                commonFlights: ['CA', 'MU', 'CZ', 'HU'],
                typicalDuration: '3-7 days',
                commonEmbarkPoints: ['CHN', 'SGP', 'THA']
            },
            'LAND': {
                commonCrossings: ['Woodlands', 'Tuas'],
                typicalDuration: '1-3 days',
                commonEmbarkPoints: ['SGP', 'THA']
            },
            'SEA': {
                commonPorts: ['Port Klang', 'Penang Port'],
                typicalDuration: '7-14 days',
                commonEmbarkPoints: ['SGP', 'THA', 'IDN']
            }
        };

        return templates[travelMode] || {};
    }

    /**
     * 获取住宿模板
     */
    getAccommodationTemplates(accommodationType) {
        const templates = {
            'HOTEL': {
                commonChains: ['Marriott', 'Hilton', 'Shangri-La'],
                typicalBookingSites: ['Booking.com', 'Agoda', 'Hotels.com'],
                commonAmenities: ['WiFi', 'Breakfast', 'Pool']
            },
            'RESIDENCE': {
                typicalLocations: ['Residential areas', 'Suburbs'],
                commonTypes: ['Apartment', 'Condo', 'House'],
                considerations: ['Local laws', 'Neighborhood safety']
            },
            'OTHERS': {
                commonOptions: ['Hostel', 'Guesthouse', 'Homestay'],
                bookingConsiderations: ['Reviews', 'Location', 'Price']
            }
        };

        return templates[accommodationType] || {};
    }

    /**
     * 启动预测引擎
     */
    startPredictionEngine() {
        // 定期更新预测（每30分钟）
        setInterval(async () => {
            await this.updatePredictions();
        }, 30 * 60 * 1000); // 30分钟

        // 监听用户活动，实时更新预测
        this.setupActivityMonitoring();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听表单提交事件
        document.addEventListener('mdacFormSubmitted', async (event) => {
            await this.analyzeUserBehavior(event.detail);
        });

        // 监听字段变化事件
        document.addEventListener('mdacFieldChanged', async (event) => {
            await this.handleFieldChange(event.detail);
        });
    }

    /**
     * 处理字段变化
     */
    async handleFieldChange(fieldData) {
        // 实时更新最近使用字段
        const key = `${fieldData.field}:${fieldData.value}`;
        this.userPatterns.lastUsedFields.add(key);

        // 限制最近使用字段数量
        if (this.userPatterns.lastUsedFields.size > 20) {
            const iterator = this.userPatterns.lastUsedFields.values();
            this.userPatterns.lastUsedFields.delete(iterator.next().value);
        }
    }

    /**
     * 设置活动监控
     */
    setupActivityMonitoring() {
        // 监听用户交互，动态调整预测
        let activityTimer = null;

        document.addEventListener('click', () => {
            clearTimeout(activityTimer);
            activityTimer = setTimeout(async () => {
                // 用户停止活动后，更新预测
                await this.updatePredictions();
            }, 5000); // 5秒无活动后更新
        });
    }

    /**
     * 更新预测
     */
    async updatePredictions() {
        try {
            console.log('🔄 更新预测...');

            const predictions = await this.generatePredictions();
            await this.executePredictiveCaching(predictions);

            // 更新统计
            this.updatePredictionStats(predictions);

        } catch (error) {
            console.error('❌ 预测更新失败:', error);
        }
    }

    /**
     * 更新预测统计
     */
    updatePredictionStats(predictions) {
        const totalPredictions = predictions.highConfidence.length + predictions.mediumConfidence.length;

        if (totalPredictions > 0) {
            // 简化的准确率计算
            const estimatedAccuracy = (predictions.highConfidence.length * 0.9 + predictions.mediumConfidence.length * 0.7) / totalPredictions;

            this.cacheStats.averagePredictionAccuracy =
                (this.cacheStats.averagePredictionAccuracy * (this.cacheStats.predictions - 1) + estimatedAccuracy) / this.cacheStats.predictions;
        }
    }

    /**
     * 保存用户模式
     */
    async saveUserPatterns() {
        try {
            const patternsToSave = {
                frequentNationality: this.userPatterns.frequentNationality,
                frequentTravelType: this.userPatterns.frequentTravelType,
                preferredAccommodation: this.userPatterns.preferredAccommodation,
                usageFrequency: Array.from(this.userPatterns.usageFrequency.entries()),
                lastUsedFields: Array.from(this.userPatterns.lastUsedFields),
                savedAt: Date.now()
            };

            await chrome.storage.local.set({
                'mdac_user_patterns': patternsToSave,
                'mdac_patterns_version': '1.0'
            });

            console.log('💾 用户模式已保存');

        } catch (error) {
            console.error('❌ 用户模式保存失败:', error);
        }
    }

    /**
     * 加载用户模式
     */
    async loadUserPatterns() {
        try {
            const result = await chrome.storage.local.get(['mdac_user_patterns']);
            const savedPatterns = result.mdac_user_patterns;

            if (savedPatterns) {
                this.userPatterns.frequentNationality = savedPatterns.frequentNationality;
                this.userPatterns.frequentTravelType = savedPatterns.frequentTravelType;
                this.userPatterns.preferredAccommodation = savedPatterns.preferredAccommodation;

                if (savedPatterns.usageFrequency) {
                    this.userPatterns.usageFrequency = new Map(savedPatterns.usageFrequency);
                }

                if (savedPatterns.lastUsedFields) {
                    this.userPatterns.lastUsedFields = new Set(savedPatterns.lastUsedFields);
                }

                console.log('📋 用户模式已加载:', {
                    nationality: this.userPatterns.frequentNationality,
                    travelType: this.userPatterns.frequentTravelType,
                    accommodation: this.userPatterns.preferredAccommodation
                });
            }

        } catch (error) {
            console.error('❌ 用户模式加载失败:', error);
        }
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        return {
            ...this.cacheStats,
            userPatterns: {
                frequentNationality: this.userPatterns.frequentNationality,
                frequentTravelType: this.userPatterns.frequentTravelType,
                preferredAccommodation: this.userPatterns.preferredAccommodation,
                usageFrequencyCount: this.userPatterns.usageFrequency.size,
                lastUsedFieldsCount: this.userPatterns.lastUsedFields.size
            }
        };
    }

    /**
     * 内存清理
     */
    cleanup() {
        // 清理事件监听器
        document.removeEventListener('mdacFormSubmitted', this.analyzeUserBehavior);
        document.removeEventListener('mdacFieldChanged', this.handleFieldChange);

        // 清理定时器
        if (this.predictionInterval) {
            clearInterval(this.predictionInterval);
        }

        console.log('🧹 PredictiveCacheManager 内存清理完成');
    }
}

/**
 * 全局预测性缓存管理器实例
 */
window.PredictiveCacheManager = new PredictiveCacheManager();

// 导出便捷函数
window.analyzeUserBehavior = function(formData) {
    return window.PredictiveCacheManager.analyzeUserBehavior(formData);
};

window.getPredictiveCacheStats = function() {
    return window.PredictiveCacheManager.getCacheStats();
};

console.log('✅ PredictiveCacheManager 系统已初始化');