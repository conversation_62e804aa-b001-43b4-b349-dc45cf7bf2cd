# MDAC字段零转换映射规范

## 概述

本规范定义了MDAC（马来西亚数字入境卡）智能填充助手的字段零转换映射标准。所有AI解析结果必须直接使用MDAC网站原生字段ID，无需任何字段映射或数据转换操作。

## MDAC网站原生字段定义

### 个人信息字段
| 字段ID | 字段名称 | 数据类型 | 格式要求 | 示例 |
|--------|----------|----------|----------|------|
| `name` | 姓名 | 文本 | 大写英文字母，最大60字符 | `ZHANG SAN` |
| `passNo` | 护照号码 | 文本 | 字母+数字，最大12字符 | `E12345678` |
| `dob` | 出生日期 | 日期 | DD/MM/YYYY格式 | `01/01/1990` |
| `passExpDte` | 护照有效期 | 日期 | DD/MM/YYYY格式 | `01/01/2030` |
| `nationality` | 国籍 | 选择 | 3字母国家代码 | `CHN` |
| `sex` | 性别 | 选择 | MALE/FEMALE | `MALE` |

### 联系信息字段
| 字段ID | 字段名称 | 数据类型 | 格式要求 | 示例 |
|--------|----------|----------|----------|------|
| `email` | 电子邮箱 | 文本 | 标准邮箱格式，最大100字符 | `<EMAIL>` |
| `confirmEmail` | 确认邮箱 | 文本 | 必须与email一致 | `<EMAIL>` |
| `region` | 电话区号 | 选择 | 国际区号代码 | `86` |
| `mobile` | 手机号码 | 文本 | 数字和特殊字符，最大12字符 | `13800138000` |

### 旅行信息字段
| 字段ID | 字段名称 | 数据类型 | 格式要求 | 示例 |
|--------|----------|----------|----------|------|
| `arrDt` | 到达日期 | 日期 | DD/MM/YYYY格式 | `01/01/2024` |
| `depDt` | 出发日期 | 日期 | DD/MM/YYYY格式 | `07/01/2024` |
| `vesselNm` | 航班号 | 文本 | 最大30字符 | `CA123` |
| `trvlMode` | 旅行方式 | 选择 | AIR/LAND/SEA | `AIR` |
| `embark` | 最后登船港 | 选择 | 国家/地区代码 | `CHN` |

### 住宿信息字段
| 字段ID | 字段名称 | 数据类型 | 格式要求 | 示例 |
|--------|----------|----------|----------|------|
| `accommodationStay` | 住宿类型 | 选择 | HOTEL/RESIDENCE/OTHERS | `HOTEL` |
| `accommodationAddress1` | 住宿地址1 | 文本 | 必填，详细地址 | `123 Hotel Street` |
| `accommodationAddress2` | 住宿地址2 | 文本 | 可选，地址补充 | `Apt 456` |
| `accommodationState` | 住宿州属 | 选择 | 马来西亚16个州属代码 | `14` |
| `accommodationCity` | 住宿城市 | 选择 | 级联加载城市列表 | `Kuala Lumpur` |
| `accommodationPostcode` | 住宿邮编 | 文本 | 5位数字 | `50000` |

## 零转换映射原则

### 1. 原生字段优先
- 所有AI解析结果必须直接使用MDAC网站原生字段ID
- 禁止使用任何中间字段名或别名
- 禁止进行字段名映射转换

### 2. 数据格式标准化
- 日期格式必须为DD/MM/YYYY
- 姓名字段必须为大写英文字母
- 国籍代码必须为3字母大写格式
- 性别必须为MALE/FEMALE
- 旅行方式必须为AIR/LAND/SEA
- 住宿类型必须为HOTEL/RESIDENCE/OTHERS

### 3. 向后兼容处理
- 系统可以识别遗留字段名并转换为MDAC原生字段
- 遗留字段映射仅用于输入兼容性，输出必须使用原生字段

## 豆包API提示词规范

### 系统提示词
```
您是MDAC（马来西亚数字入境卡）全能分析专家。请直接处理用户提供的所有原始材料：

您可以直接处理：
• 各类图片文件：护照、身份证、签证、机票截图等
• PDF文档：机票确认单、酒店预订单、行程单等
• 文本文件：行程描述、联系信息等
• 用户文字描述：补充说明信息

请您自主完成：
1. 自动识别每个文件的类型和内容
2. 从各类证件中提取准确信息
3. 解析PDF文档获取旅行详情
4. 交叉验证多个信息源的一致性
5. 智能补全缺失字段信息

MDAC表单必需字段（使用原生字段ID）：
name, passNo, nationality, dob, passExpDte, sex, email, mobile, region, arrDt, depDt, vesselNm, accommodationAddress1, accommodationState, accommodationCity, accommodationPostcode

请综合所有材料，返回完整准确的JSON格式结果，使用MDAC网站原生字段ID。
```

### 用户提示词模板
```
以下是需要分析的材料：
- {imageCount}张图片/证件照片
- {documentCount}个文档（PDF/文本等）
- 用户文字描述：{textInput}

请您智能分析所有材料，自动识别证件类型和内容，解析PDF文档，提取MDAC表单所需信息。
```

## 代码实现规范

### 1. 字段常量定义
```javascript
// MDAC网站原生字段定义
const MDAC_FIELDS = {
    NAME: 'name',
    PASS_NO: 'passNo',
    DOB: 'dob',
    PASS_EXP_DTE: 'passExpDte',
    NATIONALITY: 'nationality',
    SEX: 'sex',
    EMAIL: 'email',
    CONFIRM_EMAIL: 'confirmEmail',
    REGION: 'region',
    MOBILE: 'mobile',
    ARR_DT: 'arrDt',
    DEP_DT: 'depDt',
    VESSEL_NM: 'vesselNm',
    TRVL_MODE: 'trvlMode',
    EMBARK: 'embark',
    ACCOMMODATION_STAY: 'accommodationStay',
    ACCOMMODATION_ADDRESS1: 'accommodationAddress1',
    ACCOMMODATION_ADDRESS2: 'accommodationAddress2',
    ACCOMMODATION_STATE: 'accommodationState',
    ACCOMMODATION_CITY: 'accommodationCity',
    ACCOMMODATION_POSTCODE: 'accommodationPostcode'
};
```

### 2. 数据验证函数
```javascript
function validateMDACData(data) {
    const validated = {};
    const nativeFields = Object.values(MDAC_FIELDS);

    // 只保留MDAC原生字段
    for (const field of nativeFields) {
        if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
            validated[field] = data[field];
        }
    }

    // 检查非原生字段
    const dataFields = Object.keys(data);
    const nonNativeFields = dataFields.filter(field => !nativeFields.includes(field));
    if (nonNativeFields.length > 0) {
        console.warn('⚠️ 发现非MDAC原生字段，将被忽略:', nonNativeFields);
    }

    return { data: validated, errors: [] };
}
```

### 3. 表单填充脚本生成
```javascript
createFillScript(data) {
    // 零转换映射 - 直接使用MDAC原生字段ID
    const mdacFields = Object.values(MDAC_FIELDS);

    let script = '// MDAC表单自动填充脚本 - 零转换映射\n';
    script += '(function() {\n';

    // 直接使用MDAC字段ID作为key
    mdacFields.forEach(fieldId => {
        if (data[fieldId]) {
            script += `document.getElementById('${fieldId}').value = '${data[fieldId]}';\n`;
        }
    });

    script += '})();';
    return script;
}
```

## 测试验证

### 1. 字段验证测试
```javascript
const testData = {
    name: 'ZHANG SAN',
    passNo: 'E12345678',
    dob: '01/01/1990',
    nationality: 'CHN',
    sex: 'MALE',
    passExpDte: '01/01/2030',
    email: '<EMAIL>',
    confirmEmail: '<EMAIL>',
    region: '86',
    mobile: '13800138000',
    arrDt: '01/01/2024',
    depDt: '07/01/2024',
    vesselNm: 'CA123',
    trvlMode: 'AIR',
    embark: 'CHN',
    accommodationStay: 'HOTEL',
    accommodationAddress1: '123 Hotel Street',
    accommodationState: '14',
    accommodationCity: 'Kuala Lumpur',
    accommodationPostcode: '50000'
};

// 验证数据格式
const validation = validateMDACData(testData);
console.log('验证结果:', validation);
```

### 2. 遗留字段兼容性测试
```javascript
const legacyData = {
    passengerName: 'ZHANG SAN',  // 旧字段名
    passportNo: 'E12345678',     // 旧字段名
    birthDate: '01/01/1990',     // 旧字段名
    gender: 'MALE',              // 旧字段名
    phoneRegion: '86',           // 旧字段名
    phoneNumber: '13800138000',  // 旧字段名
    arrivalDate: '01/01/2024',   // 旧字段名
    departureDate: '07/01/2024', // 旧字段名
    flightNo: 'CA123'            // 旧字段名
};

// 应该正确转换为MDAC原生字段
const standardized = standardizeData(legacyData);
console.log('标准化结果:', standardized);
```

## 更新日志

### v2.0.0 (2025-09-16)
- ✅ 实现零转换映射，所有字段使用MDAC网站原生ID
- ✅ 移除所有字段映射转换逻辑
- ✅ 更新豆包API提示词，要求返回原生字段格式
- ✅ 添加向后兼容处理，支持遗留字段名输入
- ✅ 优化数据验证逻辑，确保字段格式正确
- ✅ 更新所有相关文档和代码注释

### v1.x.x (之前版本)
- 使用字段映射转换
- 存在多层字段转换逻辑
- 性能开销较大
- 维护复杂度较高

---

**重要提醒**：本规范实施后，所有AI解析结果必须直接使用MDAC网站原生字段ID，这是实现零转换映射的核心要求。任何中间转换都将被视为违反规范。```markdown

这个规范文档详细定义了MDAC字段的零转换映射标准，确保所有组件都使用统一的MDAC网站原生字段ID，完全避免了字段转换和映射操作。这样可以大大提高系统的性能和可维护性。