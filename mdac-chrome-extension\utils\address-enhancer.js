// 地址智能增强：基于已有地址推断 city/state/postcode
// 注意：formMapper 是全局实例，不需要导入

const postcodePattern = /\b(\d{5})\b/;

function enhanceAddress(data) {
  const updated = { ...data };
  const address = (data.accommodationAddress1 || '') + ' ' + (data.accommodationAddress2 || '');
  const lower = address.toLowerCase();
  // 1. 提取邮编
  if (!updated.accommodationPostcode) {
    const m = address.match(postcodePattern);
    if (m) updated.accommodationPostcode = m[1];
  }
  // 2. 城市匹配
  if (!updated.accommodationCity) {
    // 使用全局 formMapper 实例
    const cityMapping = window.formMapper?.cityMapping || {};
    for (const [kw, city] of Object.entries(cityMapping)) {
      if (lower.includes(kw)) { updated.accommodationCity = city; break; }
    }
  }
  // 3. 州属推断：根据城市或关键词
  if (!updated.accommodationState) {
    const stateHints = [
      { kw: ['kuala lumpur','kl'], code: '14' },
      { kw: ['johor','johor bahru','jb','新山'], code: '01' },
      { kw: ['penang','george town','槟城'], code: '07' },
      { kw: ['melaka','马六甲'], code: '04' },
      { kw: ['selangor','shah alam','petaling','subang','klang'], code: '10' },
      { kw: ['putrajaya'], code: '16' }
    ];
    for (const item of stateHints) {
      if (item.kw.some(k => lower.includes(k))) { updated.accommodationState = item.code; break; }
    }
  }
  return updated;
}

// 创建全局实例
window.addressEnhancer = { enhance: enhanceAddress };
