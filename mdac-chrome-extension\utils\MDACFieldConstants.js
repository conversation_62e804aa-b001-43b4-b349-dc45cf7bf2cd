// MDAC字段常量定义 - 统一规范所有字段，零转换映射
// 用途：定义MDAC网站原生字段ID，避免任何字段映射转换
// 技术栈：原生JavaScript常量定义

/**
 * MDAC网站原生字段ID定义
 * 这些字段ID直接对应MDAC网站的HTML元素ID，无需任何转换
 */
const MDAC_FIELDS = {
    // 个人信息 (Personal Information)
    NAME: 'name',                           // 姓名 - 文本输入框
    PASS_NO: 'passNo',                      // 护照号码 - 文本输入框
    DOB: 'dob',                             // 出生日期 - 日期选择器
    PASS_EXP_DTE: 'passExpDte',             // 护照有效期 - 日期选择器
    NATIONALITY: 'nationality',             // 国籍 - 下拉选择框
    SEX: 'sex',                             // 性别 - 下拉选择框

    // 联系信息 (Contact Information)
    EMAIL: 'email',                         // 电子邮箱 - 文本输入框
    CONFIRM_EMAIL: 'confirmEmail',          // 确认电子邮箱 - 文本输入框
    REGION: 'region',                       // 电话区号 - 下拉选择框
    MOBILE: 'mobile',                       // 手机号码 - 文本输入框

    // 旅行信息 (Travel Information)
    ARR_DT: 'arrDt',                        // 到达日期 - 日期选择器
    DEP_DT: 'depDt',                        // 出发日期 - 日期选择器
    VESSEL_NM: 'vesselNm',                  // 航班号/船名 - 文本输入框
    TRVL_MODE: 'trvlMode',                  // 旅行方式 - 下拉选择框
    EMBARK: 'embark',                       // 最后登船港 - 下拉选择框

    // 住宿信息 (Accommodation Information)
    ACCOMMODATION_STAY: 'accommodationStay',    // 住宿类型 - 下拉选择框
    ACCOMMODATION_ADDRESS1: 'accommodationAddress1', // 住宿地址1 - 文本输入框
    ACCOMMODATION_ADDRESS2: 'accommodationAddress2', // 住宿地址2 - 文本输入框
    ACCOMMODATION_STATE: 'accommodationState',     // 住宿州属 - 下拉选择框
    ACCOMMODATION_CITY: 'accommodationCity',       // 住宿城市 - 下拉选择框
    ACCOMMODATION_POSTCODE: 'accommodationPostcode' // 住宿邮编 - 文本输入框
};

/**
 * 必填字段集合
 */
const REQUIRED_FIELDS = [
    MDAC_FIELDS.NAME,
    MDAC_FIELDS.PASS_NO,
    MDAC_FIELDS.DOB,
    MDAC_FIELDS.PASS_EXP_DTE,
    MDAC_FIELDS.NATIONALITY,
    MDAC_FIELDS.SEX,
    MDAC_FIELDS.EMAIL,
    MDAC_FIELDS.CONFIRM_EMAIL,
    MDAC_FIELDS.REGION,
    MDAC_FIELDS.MOBILE,
    MDAC_FIELDS.ARR_DT,
    MDAC_FIELDS.DEP_DT,
    MDAC_FIELDS.VESSEL_NM,
    MDAC_FIELDS.TRVL_MODE,
    MDAC_FIELDS.EMBARK,
    MDAC_FIELDS.ACCOMMODATION_STAY,
    MDAC_FIELDS.ACCOMMODATION_ADDRESS1,
    MDAC_FIELDS.ACCOMMODATION_STATE,
    MDAC_FIELDS.ACCOMMODATION_CITY,
    MDAC_FIELDS.ACCOMMODATION_POSTCODE
];

/**
 * 字段类型定义
 */
const FIELD_TYPES = {
    TEXT: 'text',
    DATE: 'date',
    SELECT: 'select',
    EMAIL: 'email',
    TEL: 'tel'
};

/**
 * 字段配置映射 - 定义每个字段的属性和验证规则
 */
const FIELD_CONFIG = {
    [MDAC_FIELDS.NAME]: {
        type: FIELD_TYPES.TEXT,
        maxLength: 60,
        required: true,
        pattern: /^[A-Za-z\s]+$/,
        uppercase: true,
        description: '姓名 - 仅字母字符'
    },
    [MDAC_FIELDS.PASS_NO]: {
        type: FIELD_TYPES.TEXT,
        maxLength: 12,
        required: true,
        pattern: /^[A-Za-z0-9]+$/,
        uppercase: true,
        description: '护照号码 - 字母和数字组合'
    },
    [MDAC_FIELDS.DOB]: {
        type: FIELD_TYPES.DATE,
        required: true,
        format: 'DD/MM/YYYY',
        description: '出生日期 - DD/MM/YYYY格式'
    },
    [MDAC_FIELDS.PASS_EXP_DTE]: {
        type: FIELD_TYPES.DATE,
        required: true,
        format: 'DD/MM/YYYY',
        description: '护照有效期 - DD/MM/YYYY格式'
    },
    [MDAC_FIELDS.NATIONALITY]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        description: '国籍 - 3字母国家代码'
    },
    [MDAC_FIELDS.SEX]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        options: ['MALE', 'FEMALE'],
        description: '性别 - MALE或FEMALE'
    },
    [MDAC_FIELDS.EMAIL]: {
        type: FIELD_TYPES.EMAIL,
        maxLength: 100,
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        description: '电子邮箱 - 标准邮箱格式'
    },
    [MDAC_FIELDS.CONFIRM_EMAIL]: {
        type: FIELD_TYPES.EMAIL,
        maxLength: 100,
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        description: '确认电子邮箱 - 必须与邮箱一致'
    },
    [MDAC_FIELDS.REGION]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        description: '电话区号 - 国际区号'
    },
    [MDAC_FIELDS.MOBILE]: {
        type: FIELD_TYPES.TEL,
        maxLength: 12,
        required: true,
        pattern: /^[0-9\+\-\s]+$/,
        description: '手机号码 - 数字和特殊字符'
    },
    [MDAC_FIELDS.ARR_DT]: {
        type: FIELD_TYPES.DATE,
        required: true,
        format: 'DD/MM/YYYY',
        description: '到达日期 - DD/MM/YYYY格式'
    },
    [MDAC_FIELDS.DEP_DT]: {
        type: FIELD_TYPES.DATE,
        required: true,
        format: 'DD/MM/YYYY',
        description: '出发日期 - DD/MM/YYYY格式'
    },
    [MDAC_FIELDS.VESSEL_NM]: {
        type: FIELD_TYPES.TEXT,
        maxLength: 30,
        required: true,
        description: '航班号/船名 - 最大30字符'
    },
    [MDAC_FIELDS.TRVL_MODE]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        options: ['AIR', 'LAND', 'SEA'],
        description: '旅行方式 - AIR/LAND/SEA'
    },
    [MDAC_FIELDS.EMBARK]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        description: '最后登船港 - 国家/地区代码'
    },
    [MDAC_FIELDS.ACCOMMODATION_STAY]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        options: ['HOTEL', 'RESIDENCE', 'OTHERS'],
        description: '住宿类型 - HOTEL/RESIDENCE/OTHERS'
    },
    [MDAC_FIELDS.ACCOMMODATION_ADDRESS1]: {
        type: FIELD_TYPES.TEXT,
        required: true,
        description: '住宿地址1 - 详细地址'
    },
    [MDAC_FIELDS.ACCOMMODATION_ADDRESS2]: {
        type: FIELD_TYPES.TEXT,
        required: false,
        description: '住宿地址2 - 可选地址补充'
    },
    [MDAC_FIELDS.ACCOMMODATION_STATE]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        description: '住宿州属 - 马来西亚16个州属'
    },
    [MDAC_FIELDS.ACCOMMODATION_CITY]: {
        type: FIELD_TYPES.SELECT,
        required: true,
        description: '住宿城市 - 级联加载城市列表'
    },
    [MDAC_FIELDS.ACCOMMODATION_POSTCODE]: {
        type: FIELD_TYPES.TEXT,
        required: true,
        pattern: /^\d{5}$/,
        description: '住宿邮编 - 5位数字'
    }
};

/**
 * 获取字段选择器
 * @param {string} fieldName - MDAC字段名称
 * @returns {string} CSS选择器
 */
function getFieldSelector(fieldName) {
    return `#${fieldName}`;
}

/**
 * 验证字段值是否符合要求
 * @param {string} fieldName - MDAC字段名称
 * @param {any} value - 字段值
 * @returns {object} 验证结果 {valid: boolean, error?: string}
 */
function validateField(fieldName, value) {
    const config = FIELD_CONFIG[fieldName];
    if (!config) {
        return { valid: false, error: `未知字段: ${fieldName}` };
    }

    // 必填验证
    if (config.required && (!value || value.toString().trim() === '')) {
        return { valid: false, error: `${fieldName} 是必填字段` };
    }

    // 非必填且为空时跳过其他验证
    if (!config.required && (!value || value.toString().trim() === '')) {
        return { valid: true };
    }

    const strValue = value.toString();

    // 长度验证
    if (config.maxLength && strValue.length > config.maxLength) {
        return { valid: false, error: `${fieldName} 最大长度为 ${config.maxLength} 字符` };
    }

    // 格式验证
    if (config.pattern && !config.pattern.test(strValue)) {
        return { valid: false, error: `${fieldName} 格式不正确: ${config.description}` };
    }

    // 选项验证
    if (config.options && !config.options.includes(strValue)) {
        return { valid: false, error: `${fieldName} 必须是以下选项之一: ${config.options.join(', ')}` };
    }

    return { valid: true };
}

/**
 * 获取所有MDAC字段名称列表
 * @returns {string[]} 字段名称数组
 */
function getAllFieldNames() {
    return Object.values(MDAC_FIELDS);
}

/**
 * 获取必填字段列表
 * @returns {string[]} 必填字段名称数组
 */
function getRequiredFields() {
    return REQUIRED_FIELDS;
}

/**
 * 创建标准化的空数据对象
 * @returns {object} 包含所有MDAC字段的空对象
 */
function createEmptyData() {
    const emptyData = {};
    Object.values(MDAC_FIELDS).forEach(field => {
        emptyData[field] = '';
    });
    return emptyData;
}

// 全局暴露这些函数和常量，供其他脚本使用
if (typeof window !== 'undefined') {
    window.MDAC_FIELDS = MDAC_FIELDS;
    window.REQUIRED_FIELDS = REQUIRED_FIELDS;
    window.FIELD_TYPES = FIELD_TYPES;
    window.FIELD_CONFIG = FIELD_CONFIG;
    window.getFieldSelector = getFieldSelector;
    window.validateField = validateField;
    window.getAllFieldNames = getAllFieldNames;
    window.getRequiredFields = getRequiredFields;
    window.createEmptyData = createEmptyData;
}

/**
 * MDAC字段常量类
 * 提供统一的字段常量访问接口
 */
class MDACFieldConstants {
    constructor() {
        // 原生字段ID
        this.NATIVE_FIELDS = MDAC_FIELDS;

        // 必填字段
        this.REQUIRED_FIELDS = REQUIRED_FIELDS;

        // 字段类型
        this.FIELD_TYPES = FIELD_TYPES;

        // 字段配置
        this.FIELD_CONFIG = FIELD_CONFIG;

        // 兼容性映射 - UI字段名到MDAC原生字段ID的映射
        this.COMPATIBILITY_MAPPING = {
            // 个人信息
            'passengerName': MDAC_FIELDS.NAME,
            'name': MDAC_FIELDS.NAME,
            'passportNo': MDAC_FIELDS.PASS_NO,
            'birthDate': MDAC_FIELDS.DOB,
            'dob': MDAC_FIELDS.DOB,
            'passportExpiry': MDAC_FIELDS.PASS_EXP_DTE,
            'passExpDte': MDAC_FIELDS.PASS_EXP_DTE,
            'nationality': MDAC_FIELDS.NATIONALITY,
            'gender': MDAC_FIELDS.SEX,
            'sex': MDAC_FIELDS.SEX,

            // 联系信息
            'email': MDAC_FIELDS.EMAIL,
            'confirmEmail': MDAC_FIELDS.CONFIRM_EMAIL,
            'phoneRegion': MDAC_FIELDS.REGION,
            'region': MDAC_FIELDS.REGION,
            'phoneNumber': MDAC_FIELDS.MOBILE,
            'mobile': MDAC_FIELDS.MOBILE,

            // 旅行信息
            'arrivalDate': MDAC_FIELDS.ARR_DT,
            'arrDt': MDAC_FIELDS.ARR_DT,
            'departureDate': MDAC_FIELDS.DEP_DT,
            'depDt': MDAC_FIELDS.DEP_DT,
            'flightNo': MDAC_FIELDS.VESSEL_NM,
            'vesselNm': MDAC_FIELDS.VESSEL_NM,
            'travelMode': MDAC_FIELDS.TRVL_MODE,
            'trvlMode': MDAC_FIELDS.TRVL_MODE,
            'embark': MDAC_FIELDS.EMBARK,

            // 住宿信息
            'accommodationType': MDAC_FIELDS.ACCOMMODATION_STAY,
            'accommodationStay': MDAC_FIELDS.ACCOMMODATION_STAY,
            'address1': MDAC_FIELDS.ACCOMMODATION_ADDRESS1,
            'accommodationAddress1': MDAC_FIELDS.ACCOMMODATION_ADDRESS1,
            'address2': MDAC_FIELDS.ACCOMMODATION_ADDRESS2,
            'accommodationAddress2': MDAC_FIELDS.ACCOMMODATION_ADDRESS2,
            'state': MDAC_FIELDS.ACCOMMODATION_STATE,
            'accommodationState': MDAC_FIELDS.ACCOMMODATION_STATE,
            'city': MDAC_FIELDS.ACCOMMODATION_CITY,
            'accommodationCity': MDAC_FIELDS.ACCOMMODATION_CITY,
            'postcode': MDAC_FIELDS.ACCOMMODATION_POSTCODE,
            'accommodationPostcode': MDAC_FIELDS.ACCOMMODATION_POSTCODE
        };

        // 反向映射 - MDAC字段到UI字段的映射
        this.MDAC_TO_UI_MAPPING = {};
        for (const [uiField, mdacField] of Object.entries(this.COMPATIBILITY_MAPPING)) {
            this.MDAC_TO_UI_MAPPING[mdacField] = uiField;
        }

        // 日期字段集合
        this.DATE_FIELDS = [
            MDAC_FIELDS.DOB,
            MDAC_FIELDS.PASS_EXP_DTE,
            MDAC_FIELDS.ARR_DT,
            MDAC_FIELDS.DEP_DT
        ];

        // 选择字段集合
        this.SELECT_FIELDS = [
            MDAC_FIELDS.NATIONALITY,
            MDAC_FIELDS.SEX,
            MDAC_FIELDS.REGION,
            MDAC_FIELDS.TRVL_MODE,
            MDAC_FIELDS.EMBARK,
            MDAC_FIELDS.ACCOMMODATION_STAY,
            MDAC_FIELDS.ACCOMMODATION_STATE,
            MDAC_FIELDS.ACCOMMODATION_CITY
        ];
    }

    /**
     * 获取标准字段名
     * @param {string} fieldName - 字段名
     * @returns {string} 标准字段名
     */
    getStandardFieldName(fieldName) {
        return this.COMPATIBILITY_MAPPING[fieldName] || fieldName;
    }

    /**
     * 获取UI字段名
     * @param {string} mdacField - MDAC字段名
     * @returns {string} UI字段名
     */
    getUIFieldName(mdacField) {
        return this.MDAC_TO_UI_MAPPING[mdacField] || mdacField;
    }

    /**
     * 检查字段是否为必填
     * @param {string} fieldName - 字段名
     * @returns {boolean} 是否必填
     */
    isRequiredField(fieldName) {
        const standardField = this.getStandardFieldName(fieldName);
        return this.REQUIRED_FIELDS.includes(standardField);
    }

    /**
     * 获取字段配置
     * @param {string} fieldName - 字段名
     * @returns {object} 字段配置
     */
    getFieldConfig(fieldName) {
        const standardField = this.getStandardFieldName(fieldName);
        return this.FIELD_CONFIG[standardField] || null;
    }

    /**
     * 获取所有标准字段
     * @returns {string[]} 标准字段数组
     */
    getAllStandardFields() {
        return Object.values(this.NATIVE_FIELDS);
    }

    /**
     * 验证字段值
     * @param {string} fieldName - 字段名
     * @param {any} value - 字段值
     * @returns {object} 验证结果
     */
    validateField(fieldName, value) {
        const standardField = this.getStandardFieldName(fieldName);
        return validateField(standardField, value);
    }

    /**
     * 转换AI数据到标准格式
     * @param {object} aiData - AI数据
     * @returns {object} 标准格式数据
     */
    transformAIToStandard(aiData) {
        const transformed = {};

        for (const [field, value] of Object.entries(aiData)) {
            const standardField = this.getStandardFieldName(field);

            // 验证字段值
            const validation = this.validateField(standardField, value);
            if (validation.valid) {
                transformed[standardField] = value;
            }
        }

        return transformed;
    }

    /**
     * 获取字段选择器
     * @param {string} fieldName - 字段名
     * @returns {string} CSS选择器
     */
    getFieldSelector(fieldName) {
        const standardField = this.getStandardFieldName(fieldName);
        return getFieldSelector(standardField);
    }

    /**
     * 创建空数据对象
     * @returns {object} 空数据对象
     */
    createEmptyData() {
        return createEmptyData();
    }
}

// 全局暴露类
if (typeof window !== 'undefined') {
    window.MDACFieldConstants = MDACFieldConstants;
}

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACFieldConstants;
}