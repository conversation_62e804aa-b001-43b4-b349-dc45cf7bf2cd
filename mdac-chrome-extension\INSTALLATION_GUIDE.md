# MDAC Chrome Extension - 安装与使用指南

## 🚀 快速安装

### 1. 下载扩展文件
```
下载 mdac-chrome-extension 文件夹到本地
```

### 2. 在Chrome中安装
```
1. 打开 Chrome 浏览器
2. 地址栏输入: chrome://extensions/
3. 右上角启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 mdac-chrome-extension 文件夹
6. 点击"选择文件夹"完成安装
```

### 3. 验证安装成功
```
- Chrome工具栏出现MDAC扩展图标
- 点击图标可打开侧边栏面板
```

## 📖 使用步骤

### Step 1: 打开MDAC官网
```
访问: https://imigresen-online.imi.gov.my/mdac/main?registerMain
```

### Step 2: 启动扩展
```
点击Chrome工具栏的MDAC扩展图标
侧边栏面板将在右侧打开
```

### Step 3: 输入旅客信息
**方法A: AI智能解析**
```
1. 在"旅客信息"文本框中输入自然语言描述
   例如: "李明，中国护照G12345678，1990年1月1日出生..."
2. 点击"AI解析"按钮
3. 系统自动填充各个字段
```

**方法B: 手动填写**
```
直接在各个字段中填写信息:
- 姓名、护照号码、生日等个人信息
- 邮箱、电话等联系信息  
- 行程日期、航班、住宿等旅行信息
```

### Step 4: 执行自动填充
```
1. 确认所有必要信息已填写
2. 点击"生成并执行脚本"按钮
3. 系统自动在MDAC页面填充表单
4. 查看填充结果通知
```

### Step 5: 检查并提交
```
1. 检查MDAC页面的填充结果
2. 手动调整任何需要修正的字段
3. 在MDAC页面点击提交完成申请
```

## 🔧 功能说明

### AI解析功能
- 支持中文和英文自然语言输入
- 自动识别护照号、日期、联系方式等关键信息
- 智能推断国籍、性别等字段

### 智能填充功能
- 自动映射扩展表单到MDAC官方字段
- 处理日期格式转换(DD/MM/YYYY)
- 智能选择州属和城市级联字段
- 支持电话区号自动处理

### 错误处理
- 实时数据验证和错误提示
- 填充失败自动重试机制
- 详细的控制台日志输出

## ⚠️ 注意事项

### 浏览器要求
- Chrome 88+ (支持Manifest V3)
- 需要启用JavaScript

### 网络要求  
- AI功能需要互联网连接
- 访问Gemini API进行智能解析

### 数据安全
- 所有数据在本地处理，不上传到服务器
- 仅在MDAC官网页面激活
- 遵循最小权限原则

### 使用限制
- 仅适用于马来西亚MDAC官方网站
- 不支持其他国家的入境表单
- AI解析准确性依赖输入信息的完整性

## 🐛 故障排除

### 扩展无法加载
```
检查Chrome版本是否支持Manifest V3
重新下载扩展文件并安装
```

### 侧边栏无法打开
```
确认已正确安装扩展
检查是否在MDAC网站页面
尝试刷新页面后重新点击
```

### 自动填充失败
```
1. 检查是否在正确的MDAC页面
2. 确认表单数据已完整填写
3. 查看浏览器控制台(F12)的错误信息
4. 尝试手动调整后重新执行
```

### AI解析错误
```
检查网络连接是否正常
确认输入信息格式正确
重新整理输入内容后再次尝试
```

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查扩展是否正确安装
3. 确认MDAC网站页面结构未发生变化
4. 尝试重新安装扩展

---

**版本**: 1.0.0  
**更新时间**: 2025年1月18日  
**兼容性**: Chrome 88+ (Manifest V3)
