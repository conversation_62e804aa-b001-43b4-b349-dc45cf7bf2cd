// 智能数据预处理器 - MDAC原生字段版本
// 用途：处理文本解析和数据预处理，输出MDAC网站原生字段格式
// 依赖：无
// 技术栈：原生JavaScript
// 核心功能：文本清理、字段提取、数据标准化
// 重要：所有输出字段使用MDAC网站原生字段ID，零转换映射

class SmartPreprocessor {
    constructor() {
        this.fieldPatterns = {
            // 姓名模式 - 使用MDAC原生字段名
            name: [/^([a-zA-Z\s]+)$/, /^[\u4e00-\u9fa5]{2,4}$/,
                   /^(Mr|Mrs|Ms|Dr|Prof)\.\s+[a-zA-Z\s]+$/i],

            // 护照号码模式 - 使用MDAC原生字段名
            passNo: [/^[A-Z][0-9]{8}$/, /^[A-Z]{1,2}[0-9]{7,8}$/],

            // 日期模式
            date: [/^\d{4}-\d{2}-\d{2}$/, /^\d{2}\/\d{2}\/\d{4}$/,
                   /^\d{4}年\d{1,2}月\d{1,2}日$/],

            // 邮箱模式
            email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

            // 手机号模式 - 使用MDAC原生字段名
            mobile: /^\+?[\d\s\-\(\)]{8,20}$/,

            // 国籍代码
            nationality: /^[A-Z]{3}$/,

            // 性别 - 使用MDAC原生字段名
            sex: [/^(男|男性|Male|MALE|1)$/i, /^(女|女性|Female|FEMALE|2)$/i]
        };
    }

    // 预处理文本内容
    preprocess(text) {
        if (!text || typeof text !== 'string') {
            return {
                success: false,
                error: '无效的输入文本'
            };
        }

        console.log('🔄 开始预处理文本:', text);

        try {
            // 文本清理
            const cleanedText = this.cleanText(text);
            console.log('🧹 清理后的文本:', cleanedText);

            // 提取结构化数据
            const extractedData = this.extractFields(cleanedText);
            console.log('📋 提取的数据:', extractedData);

            // 标准化数据格式
            const standardizedData = this.standardizeData(extractedData);
            console.log('📊 标准化后的数据:', standardizedData);

            return {
                success: true,
                data: standardizedData,
                cleanedText: cleanedText
            };

        } catch (error) {
            console.error('❌ 预处理失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 清理文本
    cleanText(text) {
        return text
            .replace(/\s+/g, ' ')           // 多个空格替换为单个空格
            .replace(/\n+/g, '\n')         // 多个换行替换为单个换行
            .replace(/[\u200B-\u200D\uFEFF]/g, '') // 移除零宽度字符
            .trim();                       // 去除首尾空格
    }

    // 提取字段
    extractFields(text) {
        const data = {};
        const lines = text.split('\n');

        // 遍历每一行，提取信息
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) continue;

            // 姓名提取
            if (this.matchPattern(trimmedLine, this.fieldPatterns.name)) {
                if (!data.name) {
                    data.name = this.extractName(trimmedLine);
                }
            }

            // 护照号提取
            if (this.matchPattern(trimmedLine, this.fieldPatterns.passportNo)) {
                if (!data.passNo) {
                    data.passNo = this.extractPassportNo(trimmedLine);
                }
            }

            // 邮箱提取
            if (this.matchPattern(trimmedLine, this.fieldPatterns.email)) {
                if (!data.email) {
                    data.email = this.extractEmail(trimmedLine);
                }
            }

            // 手机号提取
            if (this.matchPattern(trimmedLine, this.fieldPatterns.phone)) {
                if (!data.mobile) {
                    data.mobile = this.extractPhone(trimmedLine);
                }
            }

            // 日期提取
            const dates = this.extractDates(trimmedLine);
            if (dates.length > 0) {
                if (!data.dob && this.isBirthDate(trimmedLine)) {
                    data.dob = dates[0];
                } else if (!data.arrDt && this.isArrivalDate(trimmedLine)) {
                    data.arrDt = dates[0];
                } else if (!data.depDt && this.isDepartureDate(trimmedLine)) {
                    data.depDt = dates[0];
                }
            }

            // 国籍提取
            const nationality = this.extractNationality(trimmedLine);
            if (nationality && !data.nationality) {
                data.nationality = nationality;
            }

            // 性别提取
            const gender = this.extractGender(trimmedLine);
            if (gender !== null && !data.sex) {
                data.sex = gender;
            }
        }

        return data;
    }

    // 匹配模式
    matchPattern(text, patterns) {
        return patterns.some(pattern => {
            if (pattern instanceof RegExp) {
                return pattern.test(text);
            }
            return text.includes(pattern);
        });
    }

    // 提取姓名
    extractName(text) {
        // 移除常见前缀
        const nameText = text.replace(/^(姓名|名字|Name):?\s*/i, '');
        // 提取字母和中文
        const nameMatch = nameText.match(/^([a-zA-Z\s\u4e00-\u9fa5]{2,20})/);
        return nameMatch ? nameMatch[1].toUpperCase() : nameText.toUpperCase();
    }

    // 提取护照号
    extractPassportNo(text) {
        const passportMatch = text.match(/([A-Z][0-9]{7,8})/);
        return passportMatch ? passportMatch[1].toUpperCase() : text.toUpperCase();
    }

    // 提取邮箱
    extractEmail(text) {
        const emailMatch = text.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        return emailMatch ? emailMatch[1].toLowerCase() : text.toLowerCase();
    }

    // 提取手机号
    extractPhone(text) {
        const phoneMatch = text.match(/(\+?[\d\s\-\(\)]{8,20})/);
        return phoneMatch ? phoneMatch[1].replace(/[\s\-\(\)]/g, '') : text;
    }

    // 提取日期
    extractDates(text) {
        const dates = [];
        const datePatterns = [
            /(\d{4})-(\d{1,2})-(\d{1,2})/,
            /(\d{1,2})\/(\d{1,2})\/(\d{4})/,
            /(\d{4})年(\d{1,2})月(\d{1,2})日/
        ];

        for (const pattern of datePatterns) {
            const match = text.match(pattern);
            if (match) {
                // 转换为 DD/MM/YYYY 格式
                const formattedDate = this.formatDate(match, pattern);
                if (formattedDate) {
                    dates.push(formattedDate);
                }
            }
        }

        return dates;
    }

    // 格式化日期
    formatDate(match, pattern) {
        let day, month, year;

        if (pattern.source.includes('\\/')) {
            // DD/MM/YYYY 格式
            day = match[1].padStart(2, '0');
            month = match[2].padStart(2, '0');
            year = match[3];
        } else if (pattern.source.includes('年')) {
            // YYYY年MM月DD日 格式
            day = match[3].padStart(2, '0');
            month = match[2].padStart(2, '0');
            year = match[1];
        } else {
            // YYYY-MM-DD 格式
            day = match[3].padStart(2, '0');
            month = match[2].padStart(2, '0');
            year = match[1];
        }

        // 验证日期
        if (this.isValidDate(day, month, year)) {
            return `${day}/${month}/${year}`;
        }

        return null;
    }

    // 验证日期
    isValidDate(day, month, year) {
        const date = new Date(year, month - 1, day);
        return date.getFullYear() == year &&
               date.getMonth() == month - 1 &&
               date.getDate() == day;
    }

    // 判断是否为出生日期
    isBirthDate(text) {
        const keywords = ['出生', '生日', 'birth', 'dob', 'born'];
        return keywords.some(keyword => text.toLowerCase().includes(keyword));
    }

    // 判断是否为到达日期
    isArrivalDate(text) {
        const keywords = ['到达', '抵达', 'arrival', 'arrive'];
        return keywords.some(keyword => text.toLowerCase().includes(keyword));
    }

    // 判断是否为离开日期
    isDepartureDate(text) {
        const keywords = ['离开', '出发', 'departure', 'depart', 'return'];
        return keywords.some(keyword => text.toLowerCase().includes(keyword));
    }

    // 提取国籍
    extractNationality(text) {
        const countryMap = {
            '中国': 'CHN', 'CHINA': 'CHN',
            '美国': 'USA', 'USA': 'USA',
            '英国': 'GBR', 'UK': 'GBR',
            '新加坡': 'SGP', 'SINGAPORE': 'SGP',
            '日本': 'JPN', 'JAPAN': 'JPN',
            '韩国': 'KOR', 'KOREA': 'KOR',
            '马来西亚': 'MYS', 'MALAYSIA': 'MYS'
        };

        for (const [country, code] of Object.entries(countryMap)) {
            if (text.toUpperCase().includes(country.toUpperCase())) {
                return code;
            }
        }

        return null;
    }

    // 提取性别
    extractGender(text) {
        if (/^(男|男性|Male|MALE|1)$/i.test(text)) {
            return '1';
        }
        if (/^(女|女性|Female|FEMALE|2)$/i.test(text)) {
            return '2';
        }
        return null;
    }

    // 标准化数据 - 零转换映射（简化版本）
    standardizeData(data) {
        const standardized = {};

        // MDAC网站原生字段列表 - 直接使用，无需转换
        const mdacNativeFields = [
            'name', 'passNo', 'dob', 'passExpDte', 'nationality', 'sex',
            'email', 'confirmEmail', 'region', 'mobile',
            'arrDt', 'depDt', 'vesselNm', 'trvlMode', 'embark',
            'accommodationStay', 'accommodationAddress1', 'accommodationAddress2',
            'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];

        // 零转换映射 - 直接复制MDAC原生字段
        mdacNativeFields.forEach(fieldId => {
            if (data[fieldId] !== undefined && data[fieldId] !== null && data[fieldId] !== '') {
                standardized[fieldId] = data[fieldId];
            }
        });

        // 最小化向后兼容：仅处理最常见的旧字段名
        const essentialLegacyMapping = {
            'passengerName': 'name',
            'passportNo': 'passNo',
            'birthDate': 'dob',
            'gender': 'sex',
            'phoneNumber': 'mobile',
            'flightNo': 'vesselNm'
        };

        // 仅处理必要的遗留字段名
        for (const [legacyField, mdacField] of Object.entries(essentialLegacyMapping)) {
            if (data[legacyField] && !standardized[mdacField]) {
                standardized[mdacField] = data[legacyField];
            }
        }

        // 自动填充确认邮箱
        if (standardized.email && !standardized.confirmEmail) {
            standardized.confirmEmail = standardized.email;
        }

        // 数据格式标准化
        if (standardized.name) {
            standardized.name = standardized.name.toUpperCase();
        }
        if (standardized.passNo) {
            standardized.passNo = standardized.passNo.toUpperCase();
        }
        if (standardized.email) {
            standardized.email = standardized.email.toLowerCase();
        }
        if (standardized.confirmEmail) {
            standardized.confirmEmail = standardized.confirmEmail.toLowerCase();
        }

        console.log('✅ 数据标准化完成（MDAC原生字段格式，零转换映射）:', standardized);
        return standardized;
    }
}

// 导出类
window.SmartPreprocessor = SmartPreprocessor;

console.log('✅ SmartPreprocessor 已加载');