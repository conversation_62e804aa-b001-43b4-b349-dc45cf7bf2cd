#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 乱码映射表
GARBLED_MAP = {
    'åˆå§‹åŒ–': '初始化',
    'ä¾§è¾¹æ ': '侧边栏',
    'äº‹ä»¶': '事件',
    'ç»'å®š': '绑定',
    'å®Œæˆ': '完成',
    'å¤±è´¥': '失败',
    'ç»„ä»¶': '组件',
    'æ‰©å±•': '扩展',
    'è‡ªåŠ¨': '自动',
    'è§£æž': '解析',
    'æ‰¾ä¸åˆ°': '找不到',
    'å…ƒç´ ': '元素',
    'åŠŸèƒ½': '功能',
    'å°†ä¸å·¥ä½œ': '将不工作',
    'åŠ è½½': '加载',
    'ä¿å­˜': '保存',
    'é"™è¯¯': '错误',
    'æˆåŠŸ': '成功',
    'å¼€å§‹': '开始',
    'ç»"æŸ': '结束',
    'çŠ¶æ€': '状态',
    'ä¸»é¢˜': '主题',
    'è®¾ç½®': '设置',
    'é…ç½®': '配置',
    'æ¸…ç†': '清理',
    'éœ€è¦': '需要',
    'ä½¿ç"¨': '使用',
    'å†…å®¹': '内容',
    'æ•°æ®': '数据',
    'æ–‡ä»¶': '文件',
    'å›¾ç‰‡': '图片',
    'ä¿¡æ¯': '信息',
    'è¿žæŽ¥': '连接',
    'ç½'ç»œ': '网络',
    'å¤„ç†å™¨': '处理器',
    'å¤„ç†': '处理',
    'å·¥å…·': '工具',
    'åŠ©æ‰‹': '助手',
    'æœåŠ¡': '服务',
    'ç³»ç»Ÿ': '系统',
    'æˆ–è€…': '或者',
    'æ–¹æ³•': '方法',
    'è¯·æ±‚': '请求'
}

def fix_file(file_path):
    try:
        # 尝试多种编码读取
        content = None
        for encoding in ['utf-8', 'utf-8-sig', 'gbk', 'gb2312']:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"Successfully read with {encoding} encoding")
                break
            except:
                continue

        if content is None:
            print("Failed to read file with any encoding")
            return False

        # 应用修复
        for garbled, correct in GARBLED_MAP.items():
            content = content.replace(garbled, correct)

        # 保存为UTF-8
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ File encoding fixed successfully")
        return True

    except Exception as e:
        print(f"❌ Error fixing file: {e}")
        return False

if __name__ == "__main__":
    file_path = "C:/Users/<USER>/Downloads/mdac/mdac-chrome-extension/sidepanel.js"
    fix_file(file_path)