<!DOCTYPE html>
<html>
<head>
    <title>创建测试图片</title>
</head>
<body>
    <h2>创建测试图片</h2>
    <canvas id="canvas" width="400" height="300" style="border: 1px solid #000;"></canvas>
    <br><br>
    <button onclick="createTestImage()">生成测试图片</button>
    <button onclick="downloadImage()">下载图片</button>
    
    <script>
        function createTestImage() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 400, 300);
            
            // 绘制护照样式的测试内容
            ctx.fillStyle = '#000000';
            ctx.font = '20px Arial';
            ctx.fillText('PASSPORT TEST', 50, 50);
            
            ctx.font = '16px Arial';
            ctx.fillText('Name: ZHANG SAN', 50, 100);
            ctx.fillText('Passport No: E12345678', 50, 130);
            ctx.fillText('Date of Birth: 01/01/1990', 50, 160);
            ctx.fillText('Nationality: CHN', 50, 190);
            ctx.fillText('Sex: M', 50, 220);
            ctx.fillText('Date of Expiry: 01/01/2030', 50, 250);
            
            console.log('测试图片已生成');
        }
        
        function downloadImage() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'test-passport.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时自动生成
        window.onload = createTestImage;
    </script>
</body>
</html>
