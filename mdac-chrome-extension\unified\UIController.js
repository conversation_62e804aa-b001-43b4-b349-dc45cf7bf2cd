// 统一用户界面控制器
// 用途：管理统一的多模态上传界面和用户交互
// 依赖：UnifiedMultiModalProcessor.js, ImageProcessor.js, FileProcessor.js
// 技术栈：原生JavaScript + HTML5拖拽API
// 核心功能：文件上传控制、进度显示、结果展示、用户反馈

class UIController {
  constructor() {
    // 严格单例：通过工厂方法获取同一实例
    this.processor = UnifiedMultiModalProcessor.getInstance();
    this.currentFiles = {
      images: [],
      documents: [],
      textFiles: []
    };
    this.isProcessing = false;
    this.uploadIntegrationInitialized = false;
    this.initializeUI();
  }

  // 初始化用户界面
  initializeUI() {
    this.setupUploadIntegration();
    this.setupEventListeners();
    this.setupProgressDisplay();
  }

  // 集成到现有的uploadDropzone
  setupUploadIntegration() {
    console.log('🔧 开始设置文件上传集成...');

    // 防止重复初始化
    if (this.uploadIntegrationInitialized) {
      console.log('ℹ️ 上传集成已初始化，跳过重复设置');
      this.ensureFileInputEvents(); // 确保事件绑定正常
      return;
    }

    // 查找现有的上传按钮
    const existingUploadBtn = document.getElementById('uploadDropzone');
    if (existingUploadBtn) {
      console.log('✅ 找到上传按钮，创建文件输入元素');

      // 创建文件输入元素
      const existingFileInput = document.getElementById('unifiedFileInput');
      if (!existingFileInput) {
        const fileInput = this.createFileInputElement();
        // 插入到上传按钮后面
        existingUploadBtn.parentNode.insertBefore(fileInput, existingUploadBtn.nextSibling);
        console.log('✅ 文件输入元素创建成功');

        // 立即绑定事件监听器
        this.bindFileInputEvents(fileInput);
      } else {
        console.log('ℹ️ 文件输入元素已存在，确保事件绑定');
        this.bindFileInputEvents(existingFileInput);
      }

      // 添加上传状态显示区域
      if (!document.getElementById('uploadStatusArea')) {
        const statusArea = document.createElement('div');
        statusArea.id = 'uploadStatusArea';
        statusArea.className = 'upload-status-area';
        statusArea.style.display = 'none';
        statusArea.innerHTML = this.getUploadStatusHTML();

        // 插入到输入区域下方
        const inputSection = document.querySelector('.input-group') || document.getElementById('statusArea');
        if (inputSection) {
          inputSection.appendChild(statusArea);
        }
      }

      // 标记初始化完成
      this.uploadIntegrationInitialized = true;
      console.log('✅ 文件上传集成初始化完成');
    } else {
      console.warn('⚠️ 未找到上传按钮，跳过文件上传集成');
    }
  }

  // 获取上传状态HTML
  getUploadStatusHTML() {
    return `
      <!-- 文件列表显示 -->
      <div class="file-list" id="fileList" style="display:none;">
        <h4>📋 已选择文件:</h4>
        <div class="file-categories">
          <div class="file-category" id="imageFiles">
            <h5>🖼️ 图片文件</h5>
            <div class="file-items"></div>
          </div>
          <div class="file-category" id="documentFiles">
            <h5>📄 文档文件</h5>
            <div class="file-items"></div>
          </div>
          <div class="file-category" id="textFiles">
            <h5>📝 文本文件</h5>
            <div class="file-items"></div>
          </div>
        </div>
        <div class="file-actions">
          <button id="clearFilesBtn" type="button" class="secondary-button">🗑️ 清空文件</button>
        </div>
      </div>

      <!-- 进度显示 -->
      <div class="progress-area" id="progressArea" style="display:none;">
        <div class="progress-header">
          <h4>⏳ 处理进度</h4>
          <div class="progress-stats" id="progressStats"></div>
        </div>
        <div class="progress-steps" id="progressSteps"></div>
      </div>

      <!-- 结果显示 -->
      <div class="result-area" id="resultArea" style="display:none;">
        <div class="result-header">
          <h4>✅ 处理结果</h4>
          <div class="result-actions">
            <button id="copyResultBtn" type="button">📋 复制</button>
            <button id="fillFormBtn" type="button">📝 填入表单</button>
          </div>
        </div>
        <div class="result-content" id="resultContent"></div>
      </div>
    `;
  }

  // 创建文件输入元素
  createFileInputElement() {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.id = 'unifiedFileInput';
    fileInput.multiple = true;
    fileInput.accept = 'image/*,.pdf,.txt,.md,.json,.doc,.docx';
    fileInput.style.display = 'none';

    // 添加数据属性标记，便于识别
    fileInput.dataset.controller = 'UIController';
    fileInput.dataset.version = '1.0';

    console.log('🔧 创建文件输入元素:', {
      id: fileInput.id,
      accept: fileInput.accept,
      multiple: fileInput.multiple
    });

    return fileInput;
  }

  // 确保文件输入元素事件绑定正常
  ensureFileInputEvents() {
    const fileInput = document.getElementById('unifiedFileInput');
    if (fileInput) {
      console.log('🔍 检查文件输入元素事件绑定状态');

      // 检查是否已有事件监听器
      const hasListener = fileInput.dataset.eventsBound === 'true';
      if (!hasListener) {
        console.log('🔗 重新绑定文件输入元素事件');
        this.bindFileInputEvents(fileInput);
        fileInput.dataset.eventsBound = 'true';
      } else {
        console.log('ℹ️ 文件输入元素事件已绑定');
      }
    } else {
      console.warn('⚠️ 文件输入元素不存在，无法确保事件绑定');
    }
  }

  // 绑定文件输入元素事件（在元素创建后调用）
  bindFileInputEvents(fileInput) {
    if (!fileInput) {
      console.error('❌ 无法绑定事件：文件输入元素为空');
      return;
    }

    console.log('🔗 为文件输入元素绑定事件监听器');

    // 移除已存在的事件监听器（避免重复绑定）
    fileInput.removeEventListener('change', this.handleFileSelect);

    // 绑定新的事件监听器
    fileInput.addEventListener('change', this.handleFileSelect.bind(this));

    // 标记事件已绑定
    fileInput.dataset.eventsBound = 'true';

    console.log('✅ 文件输入元素事件绑定完成');
  }

  // 设置事件监听器
  setupEventListeners() {
    // 注意：上传按钮的事件监听器由 sidepanel.js 管理，避免重复绑定
    // 这里只检查并绑定文件输入元素的事件（如果元素已存在）
    const fileInput = document.getElementById('unifiedFileInput');

    if (fileInput) {
      console.log('🔗 在 setupEventListeners 中绑定文件输入元素事件');
      this.bindFileInputEvents(fileInput);
    } else {
      console.log('ℹ️ 文件输入元素不存在，等待 setupUploadIntegration 创建');
    }

    // 文件拖拽到整个输入区域
    const inputSection = document.querySelector('.input-group') || document.getElementById('statusArea');
    if (inputSection) {
      inputSection.addEventListener('dragover', this.handleDragOver.bind(this));
      inputSection.addEventListener('drop', this.handleDrop.bind(this));
      inputSection.addEventListener('dragleave', this.handleDragLeave.bind(this));
    }

    // 清空文件按钮
    const clearFilesBtn = document.getElementById('clearFilesBtn');
    if (clearFilesBtn) {
      clearFilesBtn.addEventListener('click', this.handleClearFiles.bind(this));
    }

    // 结果操作按钮
    const copyBtn = document.getElementById('copyResultBtn');
    const fillBtn = document.getElementById('fillFormBtn');
    if (copyBtn) copyBtn.addEventListener('click', this.handleCopyResult.bind(this));
    if (fillBtn) fillBtn.addEventListener('click', this.handleFillForm.bind(this));
  }

  // 设置进度显示
  setupProgressDisplay() {
    // 如果存在performance monitor，连接其事件
    if (window.performanceMonitor) {
      // 可以在这里设置进度更新的回调
      this.connectPerformanceMonitor();
    }
  }

  // 处理拖拽事件
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
  }

  handleDragLeave(event) {
    event.currentTarget.classList.remove('drag-over');
  }

  handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');

    const files = Array.from(event.dataTransfer.files);
    this.processSelectedFiles(files);
  }

  // 处理文件选择
  handleFileSelect(event) {
    console.log('🎯 handleFileSelect 被调用，开始处理选择的文件');

    const files = Array.from(event.target.files);
    console.log('📁 选择的文件信息:', {
      totalFiles: files.length,
      fileDetails: files.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified).toISOString()
      }))
    });

    if (files.length === 0) {
      console.log('⚠️ 没有选择任何文件');
      return;
    }

    console.log('🚀 开始处理选中的文件...');
    this.processSelectedFiles(files);

    // 重置input，允许重复选择相同文件
    event.target.value = '';
    console.log('✅ 文件选择处理完成，input已重置');
  }

  // 处理选择的文件
  processSelectedFiles(files) {
    console.log('🔄 processSelectedFiles 被调用，开始分类和处理文件');

    const beforeCount = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    console.log('📊 处理前的文件数量:', beforeCount);

    files.forEach(file => {
      const category = this.categorizeFile(file);
      console.log(`📂 文件分类: ${file.name} -> ${category}`);
      this.currentFiles[category].push(file);
    });

    const afterCount = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    console.log('📊 处理后的文件数量:', afterCount);

    console.log('📋 当前文件分类统计:', {
      images: this.currentFiles.images.length,
      documents: this.currentFiles.documents.length,
      textFiles: this.currentFiles.textFiles.length
    });

    this.updateFileDisplay();
    this.updateProcessButton();

    console.log('🔄 准备触发自动AI解析...');
    // 🔄 触发自动AI解析
    this.triggerAutoParse();
  }

  // 文件分类
  categorizeFile(file) {
    if (file.type.startsWith('image/')) return 'images';
    if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) return 'documents';
    if (file.type.startsWith('text/') || ['.txt', '.md', '.json'].some(ext => file.name.toLowerCase().endsWith(ext))) return 'textFiles';
    return 'documents'; // 默认归档为文档
  }

  // 更新文件显示
  updateFileDisplay() {
    const fileList = document.getElementById('fileList');
    if (!fileList) { return; }
    const hasFiles = Object.values(this.currentFiles).some(arr => arr.length > 0);

    if (hasFiles) {
      fileList.style.display = 'block';
      this.updateFileCategory('images', '🖼️ 图片文件');
      this.updateFileCategory('documents', '📄 文档文件');
      this.updateFileCategory('textFiles', '📝 文本文件');
    } else {
      fileList.style.display = 'none';
    }
  }

  // 更新文件分类显示
  updateFileCategory(category, title) {
    const categoryEl = document.getElementById(category === 'images' ? 'imageFiles' :
                                            category === 'documents' ? 'documentFiles' : 'textFiles');
    const itemsContainer = categoryEl?.querySelector('.file-items');

    if (!itemsContainer) return;

    const files = this.currentFiles[category];

    if (files.length === 0) {
      categoryEl.style.display = 'none';
      return;
    }

    categoryEl.style.display = 'block';
    itemsContainer.innerHTML = files.map((file, index) => `
      <div class="file-item" data-category="${category}" data-index="${index}">
        <span class="file-name">${file.name}</span>
        <span class="file-size">(${this.formatFileSize(file.size)})</span>
        <button class="remove-file" onclick="window.uiController.removeFile('${category}', ${index})">❌</button>
      </div>
    `).join('');
  }

  // 移除文件
  removeFile(category, index) {
    this.currentFiles[category].splice(index, 1);
    this.updateFileDisplay();
    this.updateProcessButton();

    // 🔄 触发自动AI解析
    this.triggerAutoParse();
  }

  // 更新处理按钮状态
  updateProcessButton() {
    const processBtn = document.getElementById('processBtn');
    const hasContent = this.hasValidInput();

    if (processBtn) {
      processBtn.disabled = !hasContent || this.isProcessing;
      processBtn.textContent = this.isProcessing ? '⏳ 处理中...' : '🚀 开始处理';
    }
  }

  // 检查是否有有效输入
  hasValidInput() {
    const textInput = document.getElementById('userTextInput')?.value?.trim();
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);

    return textInput || totalFiles > 0;
  }

  // 处理AI解析（集成到现有按钮）
  async handleAIParseWithFiles() {
    if (this.isProcessing) {
      console.log('⚠️ 正在处理中，忽略重复请求');
      return false;
    }

    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    if (totalFiles === 0) {
      console.log('📝 没有文件需要处理，返回false让文本处理逻辑处理');
      return false;
    }

    console.log('🚀 开始多模态处理流程，文件数量:', totalFiles);
    console.log('📁 文件分类统计:', {
      images: this.currentFiles.images.length,
      documents: this.currentFiles.documents.length,
      textFiles: this.currentFiles.textFiles.length
    });

    this.isProcessing = true;
    this.showProgressArea();

    try {
      const inputs = this.collectInputs();
      const sessionId = `multimodal_${Date.now()}`;

      console.log('📤 输入数据收集完成:', {
        textInputLength: inputs.textInput.length,
        imageCount: inputs.images.length,
        fileCount: inputs.files.length,
        sessionId
      });

      // 验证处理器是否就绪
      if (!this.processor) {
        throw new Error('统一多模态处理器未就绪');
      }

      this.updateProgressStats('正在压缩和处理文件...', {
        progress: 20,
        stage: 'compressing',
        steps: ['文件准备', '压缩处理', 'AI解析', '数据提取']
      });

      const result = await this.processor.processMultiModalInput({
        ...inputs,
        sessionId
      });

      console.log('✅ 多模态处理完成，结果概要:', {
        success: result.success,
        hasData: !!result.data,
        hasValidData: result.hasValidData,
        hasMetadata: !!result.metadata,
        processingTime: result.metadata?.processingTime
      });

      this.showResult(result);

      // 自动将结果填入表单
      if (result.success && result.data && window.mdacExtension) {
        console.log('🔄 开始自动填入表单数据...');
        try {
          window.mdacExtension.applyFieldUpdates(result.data);
          console.log('✅ 表单数据填入完成');

          // 检查是否提取到了有效数据
          if (result.hasValidData) {
            this.showToast('✅ 智能识别完成，已提取文档中的个人信息');
          } else {
            this.showToast('📄 文档解析完成，但未找到可提取的个人信息字段');
          }
        } catch (formError) {
          console.error('❌ 表单填入失败:', formError);
          this.showToast('⚠️ 解析成功，但表单填入失败，请手动填入');
        }
      } else {
        console.warn('⚠️ 跳过自动表单填入:', {
          success: result.success,
          hasData: !!result.data,
          hasMDACExtension: !!window.mdacExtension
        });
      }

      return true; // 表示成功处理

    } catch (error) {
      console.error('❌ 多模态处理失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      });

      this.showError({
        message: error.message || '多模态处理失败，请重试',
        details: error.stack
      });

      return false; // 表示处理失败

    } finally {
      this.isProcessing = false;
      this.hideProgressArea();
      console.log('🏁 多模态处理流程结束');
    }
  }

  // 检查是否应该使用多模态处理
  shouldUseMultiModalProcessing() {
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    return totalFiles > 0;
  }

  // 收集所有输入
  collectInputs() {
    // 使用现有的文字输入框
    const textInput = document.getElementById('travelerInfo')?.value?.trim() || '';
    const allImages = this.currentFiles.images;
    const allFiles = [...this.currentFiles.documents, ...this.currentFiles.textFiles];

    return {
      textInput,
      images: allImages,
      files: allFiles,
      systemPrompt: null // 使用默认的MDAC提取提示词
    };
  }

  // 显示进度区域
  showProgressArea() {
    const progressArea = document.getElementById('progressArea');
    const loadingIndicator = document.getElementById('loadingIndicator');

    if (progressArea) {
      progressArea.style.display = 'block';
    }

    // 显示loadingIndicator以启用动画
    if (loadingIndicator) {
      loadingIndicator.classList.remove('hidden');
    }

    this.updateProgressStats('开始处理...', {
      progress: 10,
      stage: 'starting',
      steps: ['初始化', '文件验证', '内容分析', '结果生成']
    });
  }

  // 隐藏进度区域
  hideProgressArea() {
    const progressArea = document.getElementById('progressArea');
    const loadingIndicator = document.getElementById('loadingIndicator');

    if (progressArea) {
      progressArea.style.display = 'none';
    }

    // 隐藏loadingIndicator
    if (loadingIndicator) {
      loadingIndicator.classList.add('hidden');
    }
  }

  // 更新进度统计 (增强版 - 支持动画)
  updateProgressStats(message, options = {}) {
    const { progress = 0, stage = 'processing', steps = [] } = options;

    // 确保 message 有值，避免 undefined
    const displayMessage = message || '正在处理...';

    console.log('🔄 updateProgressStats 被调用:', {
      message: displayMessage,
      progress,
      stage,
      steps
    });

    // 更新进度文本
    const progressStats = document.getElementById('progressStats');
    if (progressStats) {
      progressStats.textContent = displayMessage;
    }

    // 更新加载指示器
    this.updateLoadingAnimation(displayMessage, stage, progress);

    // 更新进度条
    this.updateProgressBar(progress);

    // 更新处理步骤
    this.updateProcessSteps(steps);
  }

  // 更新加载动画
  updateLoadingAnimation(message, stage = 'processing', progress = 0) {
    console.log('🎭 updateLoadingAnimation 被调用:', {
      message,
      stage,
      progress
    });

    const loadingIndicator = document.getElementById('loadingIndicator');
    if (!loadingIndicator) {
      console.warn('⚠️ loadingIndicator 元素不存在');
      return;
    }

    const spinner = loadingIndicator.querySelector('.spinner');
    const loadingText = loadingIndicator.querySelector('.loading-text');

    console.log('🔍 找到的元素:', {
      loadingIndicator: !!loadingIndicator,
      spinner: !!spinner,
      loadingText: !!loadingText
    });

    // 更新文本
    if (loadingText) {
      loadingText.textContent = message;
      console.log('✅ 更新loadingText文本:', message);
    } else {
      console.warn('⚠️ loadingText 元素不存在');
    }

    // 更新spinner状态
    if (spinner) {
      spinner.className = `spinner ${stage}`;
      console.log('✅ 更新spinner类名:', `spinner ${stage}`);
    } else {
      console.warn('⚠️ spinner 元素不存在');
    }

    // 添加脉冲效果基于进度
    if (progress > 0 && progress < 100) {
      loadingIndicator.style.opacity = '0.8';
      setTimeout(() => {
        loadingIndicator.style.opacity = '1';
      }, 200);
    }
  }

  // 更新进度条
  updateProgressBar(progress) {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
      progressFill.style.width = `${progress}%`;
    }
  }

  // 更新处理步骤
  updateProcessSteps(steps = []) {
    const processStepsContainer = document.querySelector('.process-steps');
    if (!processStepsContainer) return;

    // 清空现有步骤
    processStepsContainer.innerHTML = '';

    // 默认步骤
    const defaultSteps = [
      { id: 'upload', icon: '📤', text: '上传文件', status: 'pending' },
      { id: 'process', icon: '🔄', text: '处理文件', status: 'pending' },
      { id: 'analyze', icon: '🧠', text: 'AI解析', status: 'pending' },
      { id: 'complete', icon: '✅', text: '完成', status: 'pending' }
    ];

    const stepsToShow = steps.length > 0 ? steps : defaultSteps;

    stepsToShow.forEach(step => {
      const stepElement = document.createElement('div');
      stepElement.className = `step ${step.status}`;
      stepElement.innerHTML = `
        <span class="step-icon">${step.icon}</span>
        <span class="step-text">${step.text}</span>
      `;
      processStepsContainer.appendChild(stepElement);
    });
  }

  // 显示结果
  showResult(result) {
    const resultArea = document.getElementById('resultArea');
    const resultContent = document.getElementById('resultContent');

    if (resultArea && resultContent) {
      resultArea.style.display = 'block';

      if (result.success && result.data) {
        resultContent.innerHTML = `
          <div class="result-json">
            <pre>${JSON.stringify(result.data, null, 2)}</pre>
          </div>
          ${result.metadata ? `<div class="result-metadata">
            <small>处理统计: ${this.formatMetadata(result.metadata)}</small>
          </div>` : ''}
        `;
      } else {
        resultContent.innerHTML = `
          <div class="result-raw">
            <h5>原始响应:</h5>
            <pre>${result.rawResponse || '无响应内容'}</pre>
          </div>
        `;
      }
    }
  }

  // 显示错误
  showError(error) {
    console.error('🚨 显示错误信息:', error);

    const resultArea = document.getElementById('resultArea');
    const resultContent = document.getElementById('resultContent');

    if (resultArea && resultContent) {
      resultArea.style.display = 'block';

      const errorMessage = error.message || '未知错误';
      const errorDetails = error.details || '';

      resultContent.innerHTML = `
        <div class="error-message">
          <h5>❌ 处理失败</h5>
          <p><strong>错误信息:</strong> ${errorMessage}</p>
          ${errorDetails ? `<p><strong>详细信息:</strong> <code>${errorDetails.substring(0, 200)}${errorDetails.length > 200 ? '...' : ''}</code></p>` : ''}
          <div class="error-suggestions">
            <p><strong>建议解决方案:</strong></p>
            <ul>
              <li>检查文件格式是否支持</li>
              <li>确保网络连接正常</li>
              <li>尝试重新上传文件</li>
              <li>如果问题持续，请使用文本输入方式</li>
            </ul>
          </div>
        </div>
      `;

      // 添加错误样式
      if (!document.getElementById('errorStyles')) {
        const style = document.createElement('style');
        style.id = 'errorStyles';
        style.textContent = `
          .error-message {
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
          }
          .error-message h5 {
            color: #c33;
            margin: 0 0 10px 0;
          }
          .error-message code {
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
          }
          .error-suggestions {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #fcc;
          }
          .error-suggestions ul {
            margin: 5px 0;
            padding-left: 20px;
          }
          .error-suggestions li {
            margin: 3px 0;
            color: #666;
          }
        `;
        document.head.appendChild(style);
      }

      this.showToast('❌ 处理失败，请查看错误信息');
    }
  }

  // 清空文件
  handleClearFiles() {
    // 清空文件
    this.currentFiles = { images: [], documents: [], textFiles: [] };

    // 隐藏文件列表
    const fileList = document.getElementById('fileList');
    if (fileList) fileList.style.display = 'none';

    // 隐藏结果区域
    const resultArea = document.getElementById('resultArea');
    if (resultArea) resultArea.style.display = 'none';

    this.showToast('🗑️ 已清空所有文件');

    // 🔄 触发自动AI解析（回退到文本解析）
    this.triggerAutoParse();
  }

  // 复制结果
  handleCopyResult() {
    const resultContent = document.querySelector('#resultArea .result-json pre');
    if (resultContent) {
      navigator.clipboard.writeText(resultContent.textContent).then(() => {
        this.showToast('✅ 结果已复制到剪贴板');
      });
    }
  }

  // 填入表单
  handleFillForm() {
    // 这里需要与现有的表单填充逻辑集成
    this.showToast('🚧 表单填充功能开发中...');
  }

  // 工具方法
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatMetadata(metadata) {
    const parts = [];
    if (metadata.imageStats) parts.push(`图片:${metadata.imageStats.totalFiles}`);
    if (metadata.fileStats) parts.push(`文件:${metadata.fileStats.totalFiles}`);
    if (metadata.inputSummary) parts.push(`文字:${metadata.inputSummary.textLength}字符`);
    return parts.join(', ');
  }

  showToast(message) {
    // 简单的消息提示
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed; top: 20px; right: 20px; z-index: 10000;
      background: #4CAF50; color: white; padding: 12px 20px;
      border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
  }

  // 连接性能监控器（如果可用）
  connectPerformanceMonitor() {
    // 可以在这里添加进度更新的回调
  }

  // 与现有AI解析集成的入口方法
  async integrateWithExistingAIParse() {
    console.log('🔍 integrateWithExistingAIParse 被调用');

    // 检查是否有文件需要处理
    const shouldUseMultiModal = this.shouldUseMultiModalProcessing();
    console.log('📊 多模态处理决策:', {
      shouldUseMultiModal,
      fileStatus: this.getFileStatus()
    });

    if (shouldUseMultiModal) {
      console.log('🔄 检测到文件，开始多模态处理...');
      try {
        const result = await this.handleAIParseWithFiles();
        console.log('🎯 多模态处理结果:', result);
        return result;
      } catch (error) {
        console.error('❌ 多模态处理异常:', error);
        return false;
      }
    }

    console.log('📝 无文件需要处理，使用原有文本处理逻辑');
    return false; // 让原有逻辑处理
  }

  // 获取统一处理器实例（供外部调用）
  getProcessor() {
    return this.processor;
  }

  // 获取当前文件状态（供外部调用）
  getFileStatus() {
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    return {
      hasFiles: totalFiles > 0,
      totalFiles,
      fileCount: this.currentFiles,
      shouldUseMultiModal: this.shouldUseMultiModalProcessing()
    };
  }

  // 🔄 触发自动AI解析
  triggerAutoParse() {
    console.log('🔄 triggerAutoParse 被调用，准备触发自动AI解析...');

    const fileStatus = this.getFileStatus();
    console.log('📊 当前文件状态:', fileStatus);

    // 检查是否有文件需要处理
    if (!fileStatus.hasFiles) {
      console.log('⚠️ 没有文件需要处理，跳过AI解析');
      return;
    }

    console.log('🚀 准备调用AI解析，延迟100ms确保UI更新完成...');

    // 延迟触发，确保UI更新完成
    setTimeout(() => {
      console.log('⏰ 延迟结束，开始检查MDACExtension状态...');

      if (window.mdacExtension && typeof window.mdacExtension.handleAIParse === 'function') {
        console.log('✅ MDACExtension 已就绪，调用AI解析方法');
        console.log('🔍 MDACExtension 信息:', {
          exists: !!window.mdacExtension,
          hasHandleAIParse: typeof window.mdacExtension.handleAIParse === 'function'
        });

        try {
          window.mdacExtension.handleAIParse();
          console.log('✅ AI解析方法调用成功');
        } catch (error) {
          console.error('❌ AI解析方法调用失败:', error);
        }
      } else {
        console.warn('⚠️ MDACExtension 未就绪，无法触发自动解析');
        console.warn('MDACExtension 状态:', {
          exists: !!window.mdacExtension,
          type: typeof window.mdacExtension,
          hasHandleAIParse: window.mdacExtension && typeof window.mdacExtension.handleAIParse
        });
      }
    }, 100);
  }
}

// 全局暴露和自动初始化
window.UIController = UIController;

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.uiController = new UIController();
  });
} else {
  window.uiController = new UIController();
}