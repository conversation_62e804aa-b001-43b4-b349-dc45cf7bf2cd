# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个MDAC（马来西亚数字到达卡）智能填充助手项目，主要包含：

- **Chrome扩展**: 完整的浏览器扩展，提供侧边栏界面和自动填充功能
- **统一多模态系统**: 支持文本、图片、文档等多种输入方式的AI处理
- **字段映射系统**: 精确的表单字段识别和数据转换
- **测试验证框架**: 全面的功能测试和验证工具

## 项目文件结构

```
mdac/
├── CLAUDE.md                           # 📋 项目核心指南
├── mdac-chrome-extension/              # 🚀 Chrome扩展主目录
│   ├── manifest.json                   # 扩展配置文件
│   ├── background.js                   # 服务工作者
│   ├── content.js                      # 内容脚本 (表单填充)
│   ├── sidepanel.html                  # 侧边栏界面
│   ├── sidepanel.css                   # 样式文件
│   ├── sidepanel.js                    # 主要业务逻辑
│   ├── icons/                          # 扩展图标 (16,32,48,128px)
│   ├── utils/                          # 🔧 工具模块
│   │   ├── SmartPreprocessor.js        # 智能文本预处理
│   │   ├── form-mapper.js              # 表单字段映射
│   │   ├── data-validator.js           # 数据验证
│   │   ├── address-enhancer.js         # 地址增强
│   │   └── performance-analytics.js    # 性能分析
│   └── unified/                        # 🎯 统一多模态系统
│       ├── UnifiedMultiModalProcessor.js  # 多模态处理核心
│       ├── UIController.js             # UI控制器
│       ├── ImageProcessor.js           # 图片处理
│       ├── FileProcessor.js            # 文件处理
│       ├── field-mapping-validator.js  # 字段映射验证
│       ├── integration-patch.js        # 系统集成补丁
│       └── unified-styles.css          # 统一样式
├── docs/                               # 📚 项目文档
│   ├── MDAC-web-field-analysis.md     # MDAC网站字段详细分析
│   ├── REGION_FIELD_FIX_SUMMARY.md    # 区号字段修复总结
│   ├── FIELD_MAPPING_AUDIT.md         # 字段映射审计
│   ├── PROJECT_STRUCTURE.md           # 项目结构说明
│   ├── SAMPLE_DATA_FIX_REPORT.md      # 数据修复报告
│   ├── ALTERNATIVE_SOLUTIONS_SUMMARY.md # 替代方案总结
│   └── [其他技术文档...]
├── tests/                              # 🧪 测试文件
│   ├── test-unified-system.html       # 统一系统测试
│   ├── field-mapping-test.html        # 字段映射测试
│   ├── api-test-validation.html       # API测试验证
│   └── [其他测试文件...]
├── tools/                              # 🔧 工具脚本
│   └── encoding_fix.py                 # 编码修复脚本
└── reference/                          # 📄 参考文件
    └── Malaysia Digital Arrival Card - MDAC.html  # MDAC官网参考
```

## MDAC表单字段架构

### 核心字段映射 (UI → MDAC网站)

#### **个人信息字段**
- `passengerName` / `name` → **姓名** (`name`) - 最大60字符，仅字母，自动大写
- `passportNo` / `passNo` → **护照号** (`passNo`) - 最大12字符，数字和字符
- `birthDate` / `dob` → **出生日期** (`dob`) - DD/MM/YYYY格式日历控件
- `passportExpiry` / `passExpDte` → **护照有效期** (`passExpDte`) - DD/MM/YYYY格式
- `nationality` → **国籍** (`nationality`) - 280个国家/地区选项
- `gender` / `sex` → **性别** (`sex`) - 1=MALE, 2=FEMALE

#### **联系信息字段**
- `email` → **电子邮箱** (`email`) - 最大100字符，需验证
- `confirmEmail` → **确认邮箱** (`confirmEmail`) - 与邮箱匹配验证
- `phoneNumber` / `mobile` → **手机号** (`mobile`) - 最大12字符
- `phoneRegion` / `region` → **电话区号** (`region`) - 国际区号选项

#### **旅行信息字段**
- `arrivalDate` / `arrDt` → **到达日期** (`arrDt`) - 日历控件
- `departureDate` / `depDt` → **出发日期** (`depDt`) - 日历控件
- `flightNo` / `vesselNm` → **航班号** (`vesselNm`) - 最大30字符
- `travelMode` / `trvlMode` → **旅行方式** (`trvlMode`) - 1=AIR, 2=LAND, 3=SEA
- `embark` → **最后登船港** (`embark`) - 国家/地区选项

#### **住宿信息字段**
- `accommodationType` / `accommodationStay` → **住宿类型** (`accommodationStay`) - 01=HOTEL, 02=RESIDENCE, 99=OTHERS
- `address1` / `accommodationAddress1` → **住宿地址** (`accommodationAddress1`)
- `address2` / `accommodationAddress2` → **地址第二行** (`accommodationAddress2`)
- `state` / `accommodationState` → **州属** (`accommodationState`) - 16个马来西亚州属
- `city` / `accommodationCity` → **城市** (`accommodationCity`) - 级联加载，约450个城市
- `postcode` / `accommodationPostcode` → **邮政编码** (`accommodationPostcode`) - 5位数字

## 系统架构特点

### **统一多模态处理**
- **文本输入**: AI解析自然语言描述
- **图片识别**: 护照、身份证等证件识别
- **文件上传**: 支持多种格式文档处理
- **智能预处理**: 自动数据清理和标准化

### **字段锁定机制**
- 支持单个字段锁定/解锁
- 批量锁定功能 (游客信息/行程信息)
- 锁定状态持久化保存
- 清理操作时保留锁定字段

### **实时验证和转换**
- 实时字段映射验证
- 数据格式自动转换
- 错误检测和提示
- 性能监控和分析

## 核心技术特性

### **防逆向工程保护**
- 多数输入字段禁用粘贴和复制
- 级联下拉菜单 (州属→城市→邮编)
- JavaScript客户端验证
- 安全令牌机制 (`_sourcePage`, `__fp`)

### **AI解析能力**
- **豆包API集成**: 文本内容智能解析
- **多模态支持**: 文本、图片、文档综合处理
- **字段提取**: 自动识别姓名、护照号、日期等信息
- **数据标准化**: 自动格式转换和验证

### **Chrome扩展特性**
- **Manifest V3**: 最新Chrome扩展标准
- **侧边栏模式**: 不干扰页面浏览
- **内容脚本注入**: 直接操作MDAC网站表单
- **权限最小化**: 仅请求必要权限

## 关键修复历史

### **字段映射修复** ✅
- **问题**: UI字段名与MDAC网站字段不匹配
- **修复**: 完善字段映射，支持多种命名方式
- **影响**: `passengerName→name`, `phoneRegion→region`, `gender→sex`

### **文件依赖修复** ✅
- **问题**: `SmartPreprocessor.js`文件缺失
- **修复**: 创建完整的智能预处理器
- **功能**: 文本解析、字段提取、数据标准化

### **"填入示例"功能修复** ✅
- **问题**: 直接填充表单，跳过AI解析
- **修复**: 填入文本框，触发完整AI解析流程
- **流程**: 示例文本 → 输入框 → 自动解析 → 字段更新

## 开发指南

### **新增功能开发**
1. 在对应的utils或unified目录添加模块
2. 在sidepanel.html中引入脚本
3. 更新字段映射 (sidepanel.js和content.js)
4. 添加相应测试文件

### **字段映射更新**
1. 修改`sidepanel.js`中的`createFillScript`方法
2. 更新`content.js`中的`transformDataForMDAC`函数
3. 验证字段映射验证器
4. 更新表单映射器配置

### **测试验证**
- 使用`tests/`目录中的测试文件
- 重点测试字段映射准确性
- 验证AI解析结果
- 确保Chrome扩展功能完整

## 数据规模
- **国家/地区选项**: 280个
- **马来西亚州属**: 16个（13个州+3个联邦直辖区）
- **城市总数**: 约450个
- **最多城市州属**: 霹雳州（88个城市）

## 安全考虑
- 所有用户输入需要验证和清理
- 安全令牌机制保护表单提交
- 防止XSS和CSRF攻击
- 遵循Chrome扩展安全最佳实践
- API密钥和敏感信息保护

## 常见问题排查

### **扩展无法加载**
1. 检查manifest.json语法
2. 验证所有引用文件存在
3. 查看Chrome扩展开发者模式错误信息

### **表单填充失败**
1. 确认在MDAC网站页面上操作
2. 检查字段映射是否正确
3. 验证content.js注入成功

### **AI解析不准确**
1. 检查输入文本格式
2. 验证API配置和网络连接
3. 查看智能预处理器日志

---

*最后更新: 2025年9月17日*
*项目状态: 生产就绪*

## 开发和部署

### **Chrome扩展开发**
- **开发模式**: 在Chrome扩展管理页面启用"开发者模式"
- **加载扩展**: 点击"加载已解压的扩展程序"，选择`mdac-chrome-extension`目录
- **调试工具**: 使用Chrome开发者工具调试侧边栏和内容脚本
- **热重载**: 修改代码后需在扩展管理页面重新加载扩展

### **测试验证**
- **单元测试**: 使用`tests/`目录中的HTML测试文件验证功能
- **集成测试**: 在MDAC官网 https://imigresen-online.imi.gov.my/ 上测试表单填充
- **字段锁定测试**: 验证"全部锁定"按钮能正确锁定对应组的所有字段

### **字段锁定机制**
所有HTML元素现在使用MDAC原生字段ID作为统一标准：

**联系信息组**: `email`, `confirmEmail`, `region`, `mobile`
**游客信息组**: `name`, `passNo`, `dob`, `nationality`, `sex`, `passExpDte`
**行程信息组**: `arrDt`, `depDt`, `vesselNm`, `trvlMode`, `embark`, `accommodationStay`, `accommodationAddress1`, `accommodationAddress2`, `accommodationState`, `accommodationCity`, `accommodationPostcode`

### **关键架构决策**
- **MDAC原生字段ID**: 作为整个系统的统一标识符，避免字段映射转换
- **零转换映射**: HTML元素ID、JavaScript变量名、数据键名全部使用MDAC原生字段ID
- **字段分组锁定**: 按功能域分组，支持批量锁定/解锁操作
- **统一字段转换器**: 使用 `UnifiedFieldTransformer` 进行数据转换和验证
- **字段常量系统**: `MDACFieldConstants.js` 定义所有MDAC原生字段和验证规则